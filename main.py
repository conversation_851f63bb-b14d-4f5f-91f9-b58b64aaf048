import sys
import os
import logging
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer
from unified_launcher import UnifiedLauncher
from logger_utils import log, log_exception, init_logger
from version import __version__, __app_name__

# 设置日志记录器，将控制台输出重定向到日志文件
class LogRedirector:
    def __init__(self, log_file=None):
        # 保存原始的stdout
        self.terminal = sys.stdout
        self.log_file = log_file
        self.log_file_handle = None

        # 如果没有提供日志文件路径，只使用控制台输出
        if log_file is None:
            return

        try:
            # 创建日志目录（如果不存在）
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)

            # 初始化日志文件
            self.log_file_handle = open(self.log_file, 'w', encoding='utf-8')
            self.log_file_handle.write("=== 性能监控数据分析器日志 ===\n\n")
            self.log_file_handle.flush()
        except Exception as e:
            print(f"无法创建日志文件: {e}")
            if self.log_file_handle:
                try:
                    self.log_file_handle.close()
                except:
                    pass
            self.log_file_handle = None

    def write(self, message):
        # 写入终端
        try:
            if self.terminal:
                self.terminal.write(message)
        except Exception as e:
            # 如果终端写入失败，不要抛出异常
            pass

        # 写入日志文件
        if self.log_file_handle is not None:
            try:
                self.log_file_handle.write(message)
                self.log_file_handle.flush()
            except Exception:
                # 如果日志文件写入失败，关闭文件句柄
                try:
                    self.log_file_handle.close()
                except:
                    pass
                self.log_file_handle = None

    def flush(self):
        try:
            if self.terminal:
                self.terminal.flush()
        except Exception:
            pass

        if self.log_file_handle is not None:
            try:
                self.log_file_handle.flush()
            except Exception:
                try:
                    self.log_file_handle.close()
                except:
                    pass
                self.log_file_handle = None

    def __del__(self):
        if self.log_file_handle is not None:
            try:
                self.log_file_handle.close()
            except Exception:
                pass

def setup_logging():
    # 首先设置一个基本的日志配置，以防出错
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    # 初始化一个简单的日志重定向器，不使用文件
    # 这样即使后面的代码出错，也不会影响程序运行
    safe_redirector = LogRedirector()
    sys.stdout = safe_redirector
    sys.stderr = safe_redirector

    # 初始化日志记录器，但不指定文件，使用默认设置
    log_file = None

    try:
        # 获取应用程序路径
        if getattr(sys, 'frozen', False):
            # 如果是打包后的环境，使用sys._MEIPASS或当前目录
            try:
                app_path = sys._MEIPASS
            except AttributeError:
                app_path = os.path.dirname(sys.executable)
        else:
            # 如果是开发环境，使用当前文件目录
            app_path = os.path.dirname(os.path.abspath(__file__))

        # 尝试创建日志目录
        log_dir = None
        for potential_dir in [os.path.join(app_path, 'logs'), app_path, os.getcwd(), os.path.expanduser('~')]:
            try:
                if not os.path.exists(potential_dir):
                    os.makedirs(potential_dir)
                # 测试目录是否可写
                test_file = os.path.join(potential_dir, 'test_write.tmp')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                log_dir = potential_dir
                break
            except Exception:
                continue

        # 如果所有目录都不可写，仅使用控制台输出
        if log_dir is None:
            print("无法找到可写的目录来存放日志文件，仅使用控制台输出")
            # 使用默认设置初始化日志记录器
            init_logger()
            return

        # 设置日志文件路径，使用日期作为文件名的一部分
        from datetime import datetime
        log_file = os.path.join(log_dir, f"app_{datetime.now().strftime('%Y%m%d')}.log")
        print(f"尝试使用日志文件: {log_file}")

        # 创建日志重定向器
        log_redirector = LogRedirector(log_file)
        sys.stdout = log_redirector
        sys.stderr = log_redirector

        # 使用统一的日志初始化函数
        init_logger(log_dir=log_dir)
    except Exception as e:
        print(f"设置日志系统时出错: {e}")
        # 如果出错，使用默认设置
        try:
            init_logger()
        except Exception as e2:
            print(f"初始化默认日志记录器时出错: {e2}")

# 全局异常处理函数
def global_exception_handler(exctype, value, tb):
    """处理未捕获的异常"""
    # 记录异常信息
    error_msg = f"未捕获的异常: {exctype.__name__}: {value}"
    tb_str = ''.join(traceback.format_tb(tb))

    # 记录到日志
    log(error_msg, "CRITICAL")
    log(tb_str, "CRITICAL")
    log(f"Python版本: {sys.version}", "DEBUG")
    log(f"当前工作目录: {os.getcwd()}", "DEBUG")

    # 显示错误对话框
    error_box = QMessageBox()
    error_box.setIcon(QMessageBox.Critical)
    error_box.setWindowTitle("程序错误")
    error_box.setText("程序发生了严重错误")
    error_box.setInformativeText(error_msg)
    error_box.setDetailedText(f"{error_msg}\n\n{tb_str}")
    error_box.setStandardButtons(QMessageBox.Ok)
    error_box.exec_()

    # 调用原始异常处理程序
    sys.__excepthook__(exctype, value, tb)

# 添加一个特殊的异常处理函数来捕获保存后退出的问题
_original_excepthook = sys.excepthook

def custom_excepthook(exctype, value, tb):
    """增强的异常处理函数，特别关注保存后退出的问题"""
    try:
        # 记录异常信息
        error_msg = f"未捕获的异常: {exctype.__name__}: {value}"
        tb_str = ''.join(traceback.format_tb(tb))

        # 记录到日志
        log(error_msg, "CRITICAL")
        log(tb_str, "CRITICAL")
        log(f"Python版本: {sys.version}", "DEBUG")
        log(f"当前工作目录: {os.getcwd()}", "DEBUG")

        # 特别记录当前所有线程的状态
        import threading
        log(f"当前活动线程数: {threading.active_count()}", "DEBUG")
        for thread in threading.enumerate():
            log(f"线程: {thread.name}, 活动: {thread.is_alive()}, 守护: {thread.daemon}", "DEBUG")

        # 记录内存使用情况
        import psutil
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        log(f"内存使用: {memory_info.rss / 1024 / 1024:.2f} MB", "DEBUG")

        # 调用原始异常处理程序
        _original_excepthook(exctype, value, tb)
    except Exception as e:
        # 如果在异常处理中又出现异常，使用原始处理程序
        print(f"在异常处理中又出现异常: {e}")
        sys.__excepthook__(exctype, value, tb)

# 添加系统退出处理函数
def handle_exit():
    """在程序退出时调用的函数"""
    try:
        log("系统退出函数被调用", "CRITICAL")
        # 记录调用堆栈
        import traceback
        stack = traceback.format_stack()
        log(f"退出时的调用堆栈: {''.join(stack)}", "CRITICAL")

        # 记录所有线程的状态
        import threading
        log(f"退出时的活动线程数: {threading.active_count()}", "CRITICAL")
        for thread in threading.enumerate():
            log(f"线程: {thread.name}, 活动: {thread.is_alive()}, 守护: {thread.daemon}", "CRITICAL")

        # 确保日志被写入文件
        import logging
        logging.shutdown()
    except Exception as e:
        print(f"处理退出时出错: {e}")

# 注册退出处理函数
import atexit
atexit.register(handle_exit)

def main():
    """应用程序的主入口点。"""
    # 设置日志
    setup_logging()

    # 设置全局异常处理
    sys.excepthook = custom_excepthook

    try:
        # 确保字体可用
        try:
            from ensure_fonts import ensure_fonts
            ensure_fonts()
            log("字体初始化完成", "INFO")
        except Exception as e:
            log(f"字体初始化失败，但将继续运行: {e}", "WARNING")
            # 即使字体初始化失败，也继续运行程序

        # 创建应用程序
        app = QApplication(sys.argv)
        log("QApplication创建成功", "INFO")

        # 设置应用程序属性
        app.setApplicationName(__app_name__)
        app.setApplicationVersion(__version__)
        log(f"应用程序属性设置: {__app_name__} v{__version__}", "INFO")

        # 创建统一启动器窗口
        window = UnifiedLauncher()
        window.show()

        # 设置定时器强制垃圾回收
        gc_timer = QTimer()
        gc_timer.timeout.connect(lambda: __import__('gc').collect())
        gc_timer.start(60000)  # 每分钟进行一次垃圾回收

        # 添加一个定时器来定期记录应用程序状态
        status_timer = QTimer()
        def log_app_status():
            try:
                log("应用程序状态检查", "INFO")
                import threading
                log(f"当前活动线程数: {threading.active_count()}", "DEBUG")
                # 记录内存使用情况
                try:
                    import psutil
                    process = psutil.Process(os.getpid())
                    memory_info = process.memory_info()
                    log(f"内存使用: {memory_info.rss / 1024 / 1024:.2f} MB", "DEBUG")
                except ImportError:
                    log("psutil模块不可用，无法记录内存使用情况", "WARNING")
                except Exception as e:
                    log(f"记录内存使用情况时出错: {e}", "ERROR")
            except Exception as e:
                log(f"记录应用程序状态时出错: {e}", "ERROR")

        status_timer.timeout.connect(log_app_status)
        status_timer.start(300000)  # 每5分钟记录一次应用程序状态

        # 添加一个特殊的处理来防止程序在保存后退出
        # 创建一个守护线程，定期检查应用程序是否仍在运行
        import threading
        def watchdog():
            try:
                log("守护线程启动", "INFO")
                while True:
                    # 每5秒检查一次
                    threading.Event().wait(5)
                    log("守护线程仍在运行", "DEBUG")
            except Exception as e:
                log(f"守护线程出错: {e}", "ERROR")

        # 启动守护线程
        watchdog_thread = threading.Thread(target=watchdog, daemon=True, name="WatchdogThread")
        watchdog_thread.start()
        log(f"守护线程已启动: {watchdog_thread.name}", "INFO")

        # 运行应用程序
        log("应用程序开始运行", "INFO")

        # 使用try-finally结构确保我们可以捕获到程序退出
        try:
            exit_code = app.exec_()
        finally:
            log(f"应用程序退出，退出代码: {exit_code if 'exit_code' in locals() else 'unknown'}", "INFO")
            # 确保日志被写入文件
            import logging
            logging.shutdown()

        # 正常退出
        sys.exit(exit_code if 'exit_code' in locals() else 1)
    except Exception as e:
        # 记录启动错误
        error_msg, error_tb = log_exception(e, "启动应用程序时")

        # 显示错误对话框
        error_box = QMessageBox()
        error_box.setIcon(QMessageBox.Critical)
        error_box.setWindowTitle("启动错误")
        error_box.setText("启动应用程序时发生错误")
        error_box.setInformativeText(error_msg)
        error_box.setDetailedText(error_tb)
        error_box.setStandardButtons(QMessageBox.Ok)
        error_box.exec_()

        # 退出应用程序
        sys.exit(1)


if __name__ == '__main__':
    main()
