"""
Raw Nmon文件分析器

这个模块用于直接分析nmon原始文件，提取性能数据并生成报告。
"""

import os
import sys
import pandas as pd
import numpy as np
# 设置 Matplotlib 使用非交互式后端，避免在非主线程中创建图形的警告
import matplotlib
matplotlib.use('Agg')  # 必须在导入 pyplot 之前设置
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from datetime import datetime
import re
from collections import defaultdict

from nmon_parser import NmonParser
from excel_generator import ExcelGenerator

# 配置matplotlib支持中文
def configure_matplotlib_chinese_font():
    """配置matplotlib使用中文字体"""
    # 尝试使用系统中可能存在的中文字体
    chinese_fonts = [
        'Microsoft YaHei',  # 微软雅黑
        'SimHei',           # 黑体
        'SimSun',           # 宋体
        'NSimSun',          # 新宋体
        'FangSong',         # 仿宋
        'KaiTi',            # 楷体
        'STSong',           # 华文宋体
        'STKaiti',          # 华文楷体
        'STHeiti',          # 华文黑体
        'STFangsong',       # 华文仿宋
        'Arial Unicode MS', # Arial Unicode MS
        'WenQuanYi Micro Hei', # 文泉驿微米黑
        'WenQuanYi Zen Hei',   # 文泉驿正黑
        'Source Han Sans CN',  # 思源黑体
        'Source Han Serif CN', # 思源宋体
        'Noto Sans CJK SC',    # Noto Sans CJK SC
        'Noto Serif CJK SC'    # Noto Serif CJK SC
    ]

    # 查找系统中存在的中文字体
    font_found = False
    for font_name in chinese_fonts:
        font_path = None
        try:
            # 尝试查找字体文件
            font_path = fm.findfont(fm.FontProperties(family=font_name))
            if font_path and not font_path.endswith('DejaVuSans.ttf'):
                plt.rcParams['font.family'] = [font_name, 'sans-serif']
                print(f"使用中文字体: {font_name}, 路径: {font_path}")
                font_found = True
                break
        except:
            continue

    if not font_found:
        # 如果没有找到中文字体，使用英文标题
        print("未找到中文字体，将使用英文标题")
        plt.rcParams['font.family'] = ['sans-serif']
        return False

    # 设置字体
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    return True

# 调用配置函数
use_chinese = configure_matplotlib_chinese_font()


class RawNmonAnalyzer:
    """原始Nmon文件分析器类。"""

    def __init__(self, nmon_file_path, output_dir=None, progress_callback=None, log_callback=None):
        """
        初始化原始Nmon文件分析器。

        参数:
            nmon_file_path: nmon文件路径
            output_dir: 输出目录，默认为nmon文件所在目录
            progress_callback: 进度回调函数
            log_callback: 日志回调函数
        """
        # 检查参数
        if not nmon_file_path or not os.path.exists(nmon_file_path):
            raise ValueError(f"nmon文件不存在或路径无效: {nmon_file_path}")

        self.nmon_file_path = nmon_file_path
        self.output_dir = output_dir or os.path.dirname(nmon_file_path)
        self.progress_callback = progress_callback
        self.log_callback = log_callback

        # 创建输出目录（如果不存在）
        if not os.path.exists(self.output_dir):
            try:
                os.makedirs(self.output_dir)
                self.log(f"创建输出目录: {self.output_dir}")
            except Exception as e:
                raise ValueError(f"创建输出目录失败: {str(e)}")

        # 分析选项
        self.include_charts = True  # 默认包含性能图表
        self.include_trend_analysis = True  # 默认包含趋势分析

        # 使用现有的NmonParser
        try:
            self.parser = NmonParser(nmon_file_path)
            self.parser.set_callbacks(progress_callback, log_callback)
        except Exception as e:
            raise ValueError(f"初始化NmonParser失败: {str(e)}")

        # 分析结果
        self.analysis_result = None
        self.parsed_data = None
        self.time_series_data = None

    def log(self, message):
        """记录日志消息。"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def update_progress(self, percent):
        """更新进度。"""
        if self.progress_callback:
            self.progress_callback(percent)

    def analyze(self):
        """
        分析nmon文件并生成结果。

        返回:
            dict: 分析结果
        """
        try:
            self.log(f"开始分析nmon文件: {self.nmon_file_path}")

            # 检查文件是否存在
            if not os.path.exists(self.nmon_file_path):
                raise ValueError(f"nmon文件不存在: {self.nmon_file_path}")

            # 检查输出目录是否存在
            if not os.path.exists(self.output_dir):
                try:
                    os.makedirs(self.output_dir)
                    self.log(f"创建输出目录: {self.output_dir}")
                except Exception as e:
                    raise ValueError(f"创建输出目录失败: {str(e)}")

            # 解析nmon文件
            self.update_progress(10)
            self.log("开始解析nmon文件...")
            self.parsed_data = self.parser.parse()
            self.log("解析nmon文件完成")

            # 提取时间序列数据
            self.update_progress(40)
            self.log("开始提取时间序列数据...")
            self.extract_time_series_data()
            self.log("提取时间序列数据完成")

            # 计算性能指标
            self.update_progress(70)
            self.log("开始计算性能指标...")
            self.calculate_performance_metrics()
            self.log("计算性能指标完成")

            # 生成Excel报告
            self.update_progress(90)
            self.log("开始生成Excel报告...")
            output_file = self.generate_excel_report()
            self.log(f"Excel报告生成完成: {output_file}")

            self.update_progress(100)
            self.log(f"nmon文件分析完成，结果已保存到: {output_file}")

            # 检查结果
            if not self.analysis_result:
                raise ValueError("分析结果为空")

            if not os.path.exists(output_file):
                raise ValueError(f"Excel报告文件不存在: {output_file}")

            return {
                'output_file': output_file,
                'analysis_result': self.analysis_result
            }
        except Exception as e:
            self.log(f"分析nmon文件时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            raise

    def extract_time_series_data(self):
        """提取时间序列数据。"""
        self.log("提取时间序列数据...")

        # 初始化时间序列数据结构
        self.time_series_data = {
            'timestamps': [],
            'cpu': {
                'user': [],
                'sys': [],
                'wait': [],
                'idle': [],
                'busy': []
            },
            'memory': {
                'total': [],
                'free': [],
                'used_percent': []
            },
            'disk': defaultdict(lambda: {
                'busy': [],
                'read': [],
                'write': []
            }),
            'network': defaultdict(lambda: {
                'read': [],
                'write': []
            })
        }

        # 从解析的数据中提取时间序列
        timestamps = list(self.parser.timestamps.values())
        self.time_series_data['timestamps'] = timestamps

        # 提取CPU数据
        for timestamp_id, cpu_data in self.parser.cpu_data.items():
            self.time_series_data['cpu']['user'].append(cpu_data['user'])
            self.time_series_data['cpu']['sys'].append(cpu_data['sys'])
            self.time_series_data['cpu']['wait'].append(cpu_data['wait'])
            self.time_series_data['cpu']['idle'].append(cpu_data['idle'])
            self.time_series_data['cpu']['busy'].append(cpu_data['busy'])

        # 提取内存数据
        for timestamp_id, mem_data in self.parser.memory_data.items():
            # 使用memtotal和memfree字段，如果存在
            if 'memtotal' in mem_data:
                total = mem_data['memtotal']
            elif 'total' in mem_data:
                total = mem_data['total']
            else:
                total = 0

            if 'memfree' in mem_data:
                free = mem_data['memfree']
            elif 'free' in mem_data:
                free = mem_data['free']
            else:
                free = 0

            # 计算使用百分比，使用新公式: (memtotal - memfree - cached - buffers) / memtotal * 100
            cached = mem_data.get('cached', 0)
            buffers = mem_data.get('buffers', 0)
            used_percent = ((total - free - cached - buffers) / total * 100) if total > 0 else 0
            # 移除详细日志输出，减少日志量

            # 存储基本内存数据
            self.time_series_data['memory']['total'].append(total)
            self.time_series_data['memory']['free'].append(free)
            self.time_series_data['memory']['used_percent'].append(used_percent)

            # 添加其他内存字段
            for field, value in mem_data.items():
                if field not in ['total', 'free', 'memtotal', 'memfree']:
                    if field not in self.time_series_data['memory']:
                        self.time_series_data['memory'][field] = []
                    self.time_series_data['memory'][field].append(value)

        # 提取磁盘数据
        all_disks = set()
        for timestamp_id, disks in self.parser.disk_data.items():
            all_disks.update(disks.keys())

        for disk_name in all_disks:
            for timestamp_id, disks in self.parser.disk_data.items():
                if disk_name in disks:
                    disk_data = disks[disk_name]
                    self.time_series_data['disk'][disk_name]['busy'].append(disk_data['busy'])
                    self.time_series_data['disk'][disk_name]['read'].append(disk_data['read'])
                    self.time_series_data['disk'][disk_name]['write'].append(disk_data['write'])
                else:
                    # 如果该时间点没有该磁盘的数据，填充0
                    self.time_series_data['disk'][disk_name]['busy'].append(0)
                    self.time_series_data['disk'][disk_name]['read'].append(0)
                    self.time_series_data['disk'][disk_name]['write'].append(0)

        # 提取网络数据
        all_interfaces = set()
        for timestamp_id, interfaces in self.parser.network_data.items():
            all_interfaces.update(interfaces.keys())

        for interface_name in all_interfaces:
            for timestamp_id, interfaces in self.parser.network_data.items():
                if interface_name in interfaces:
                    interface_data = interfaces[interface_name]
                    self.time_series_data['network'][interface_name]['read'].append(interface_data['read'])
                    self.time_series_data['network'][interface_name]['write'].append(interface_data['write'])
                else:
                    # 如果该时间点没有该接口的数据，填充0
                    self.time_series_data['network'][interface_name]['read'].append(0)
                    self.time_series_data['network'][interface_name]['write'].append(0)

        self.log("时间序列数据提取完成")

    def calculate_performance_metrics(self):
        """计算性能指标。"""
        self.log("计算性能指标...")

        # 使用已解析的数据
        self.analysis_result = self.parsed_data

        # 添加额外的分析结果
        self.analysis_result['time_series'] = self.time_series_data

        # 添加系统详情
        system_details = {
            '主机名': self.parser.hostname,
            '操作系统': self.parser.os_version,
            '日期': self.parser.date,
            '时间': self.parser.time,
            '采样间隔(秒)': self.parser.interval_seconds,
            '采样点数量': len(self.parser.timestamps)
        }

        # 从系统信息中提取更多详细信息
        if 'system_info' in self.parsed_data:
            system_info = self.parsed_data['system_info']

            # 添加OS信息
            if 'os_info' in system_info and system_info['os_info']:
                for key, value in system_info['os_info'].items():
                    if 'pretty_name' in key or 'distrib_id' in key or 'distrib_release' in key:
                        system_details[f'操作系统_{key}'] = value

            # 添加CPU信息
            if 'cpu_info' in system_info and system_info['cpu_info']:
                for key, value in system_info['cpu_info'].items():
                    if 'model_name' in key or 'cpu_mhz' in key:
                        system_details[f'CPU_{key}'] = value

            # 添加磁盘信息
            if 'disk_info' in system_info and system_info['disk_info']:
                disk_count = 0
                for key, value in system_info['disk_info'].items():
                    if 'disk' in value or 'part' in value:
                        system_details[f'磁盘_{disk_count}'] = f"{key}: {value}"
                        disk_count += 1

        self.analysis_result['system_details'] = system_details

        # 添加CPU详情
        cpu_busy = self.time_series_data['cpu']['busy']
        if cpu_busy:
            self.analysis_result['cpu_details'] = {
                'CPU平均使用率': round(np.mean(cpu_busy), 2),
                'CPU最大使用率': round(np.max(cpu_busy), 2),
                'CPU最小使用率': round(np.min(cpu_busy), 2),
                'CPU标准差': round(np.std(cpu_busy), 2)
            }

        # 添加内存详情
        mem_total = self.time_series_data['memory']['total']
        mem_free = self.time_series_data['memory']['free']
        mem_used_percent = self.time_series_data['memory']['used_percent']

        # 获取cached和buffers数据（如果存在）
        mem_cached = self.time_series_data['memory'].get('cached', [])
        mem_buffers = self.time_series_data['memory'].get('buffers', [])

        if mem_total and mem_free and mem_used_percent:
            memory_details = {
                '内存总量': round(np.mean(mem_total), 2),
                '内存空闲': round(np.mean(mem_free), 2),
                '内存平均使用率': round(np.mean(mem_used_percent), 2),
                '内存最大使用率': round(np.max(mem_used_percent), 2),
                '内存最小使用率': round(np.min(mem_used_percent), 2)
            }

            # 添加cached和buffers信息（如果存在）
            if mem_cached:
                memory_details['缓存大小'] = round(np.mean(mem_cached), 2)
            if mem_buffers:
                memory_details['缓冲区大小'] = round(np.mean(mem_buffers), 2)

            self.analysis_result['memory_details'] = memory_details

        # 添加磁盘详情
        disk_details = {}
        for disk_name, disk_data in self.time_series_data['disk'].items():
            disk_busy = disk_data['busy']
            disk_read = disk_data['read']
            disk_write = disk_data['write']
            if disk_busy and disk_read and disk_write:
                disk_details[f'磁盘 {disk_name} 平均使用率'] = round(np.mean(disk_busy), 2)
                disk_details[f'磁盘 {disk_name} 最大使用率'] = round(np.max(disk_busy), 2)
                disk_details[f'磁盘 {disk_name} 平均读取速率'] = round(np.mean(disk_read), 2)
                disk_details[f'磁盘 {disk_name} 平均写入速率'] = round(np.mean(disk_write), 2)

        if disk_details:
            self.analysis_result['disk_details'] = disk_details

        # 添加网络详情
        network_details = {}
        for interface_name, interface_data in self.time_series_data['network'].items():
            net_read = interface_data['read']
            net_write = interface_data['write']
            if net_read and net_write:
                network_details[f'网络 {interface_name} 平均读取速率'] = round(np.mean(net_read), 2)
                network_details[f'网络 {interface_name} 平均写入速率'] = round(np.mean(net_write), 2)
                network_details[f'网络 {interface_name} 最大读取速率'] = round(np.max(net_read), 2)
                network_details[f'网络 {interface_name} 最大写入速率'] = round(np.max(net_write), 2)

        if network_details:
            self.analysis_result['network_details'] = network_details

        # 根据选项决定是否计算趋势
        if hasattr(self, 'include_trend_analysis') and self.include_trend_analysis:
            self.log("计算性能趋势分析...")

            # 计算CPU使用率趋势
            cpu_busy = self.time_series_data['cpu']['busy']
            if cpu_busy:
                # 计算CPU使用率的变化趋势
                if len(cpu_busy) > 1:
                    cpu_trend = np.polyfit(range(len(cpu_busy)), cpu_busy, 1)[0]
                    self.analysis_result['cpu_trend'] = {
                        'slope': cpu_trend,
                        'direction': '上升' if cpu_trend > 0.1 else ('下降' if cpu_trend < -0.1 else '稳定')
                    }

                # 计算CPU使用率的波动性
                cpu_std = np.std(cpu_busy)
                self.analysis_result['cpu_volatility'] = {
                    'std': cpu_std,
                    'level': '高' if cpu_std > 15 else ('中' if cpu_std > 5 else '低')
                }

                # 识别CPU使用率峰值
                peak_threshold = np.mean(cpu_busy) + 2 * np.std(cpu_busy)
                peaks = [i for i, v in enumerate(cpu_busy) if v > peak_threshold]
                if peaks:
                    self.analysis_result['cpu_peaks'] = {
                        'count': len(peaks),
                        'indices': peaks,
                        'values': [cpu_busy[i] for i in peaks]
                    }
        else:
            self.log("跳过趋势分析，因为选项未启用")

        # 计算内存使用率趋势
        if hasattr(self, 'include_trend_analysis') and self.include_trend_analysis:
            mem_used = self.time_series_data['memory']['used_percent']
            if mem_used:
                # 计算内存使用率的变化趋势
                if len(mem_used) > 1:
                    mem_trend = np.polyfit(range(len(mem_used)), mem_used, 1)[0]
                    self.analysis_result['memory_trend'] = {
                        'slope': mem_trend,
                        'direction': '上升' if mem_trend > 0.1 else ('下降' if mem_trend < -0.1 else '稳定')
                    }

                # 计算内存使用率的波动性
                mem_std = np.std(mem_used)
                self.analysis_result['memory_volatility'] = {
                    'std': mem_std,
                    'level': '高' if mem_std > 10 else ('中' if mem_std > 3 else '低')
                }

        # 计算磁盘I/O趋势
        if hasattr(self, 'include_trend_analysis') and self.include_trend_analysis:
            for disk_name, disk_data in self.time_series_data['disk'].items():
                disk_busy = disk_data['busy']
                if disk_busy:
                    # 计算磁盘使用率的变化趋势
                    if len(disk_busy) > 1:
                        disk_trend = np.polyfit(range(len(disk_busy)), disk_busy, 1)[0]
                        if 'disk_trends' not in self.analysis_result:
                            self.analysis_result['disk_trends'] = {}
                        self.analysis_result['disk_trends'][disk_name] = {
                            'slope': disk_trend,
                            'direction': '上升' if disk_trend > 0.1 else ('下降' if disk_trend < -0.1 else '稳定')
                        }

                    # 计算磁盘I/O的波动性
                    disk_std = np.std(disk_busy)
                    if 'disk_volatility' not in self.analysis_result:
                        self.analysis_result['disk_volatility'] = {}
                    self.analysis_result['disk_volatility'][disk_name] = {
                        'std': disk_std,
                        'level': '高' if disk_std > 20 else ('中' if disk_std > 10 else '低')
                    }

        # 计算网络I/O趋势
        if hasattr(self, 'include_trend_analysis') and self.include_trend_analysis:
            for interface_name, interface_data in self.time_series_data['network'].items():
                net_read = interface_data['read']
                net_write = interface_data['write']
                if net_read and net_write:
                    # 计算网络读取的变化趋势
                    if len(net_read) > 1:
                        read_trend = np.polyfit(range(len(net_read)), net_read, 1)[0]
                        if 'network_trends' not in self.analysis_result:
                            self.analysis_result['network_trends'] = {}
                        if interface_name not in self.analysis_result['network_trends']:
                            self.analysis_result['network_trends'][interface_name] = {}
                        self.analysis_result['network_trends'][interface_name]['read'] = {
                            'slope': read_trend,
                            'direction': '上升' if read_trend > 1 else ('下降' if read_trend < -1 else '稳定')
                        }

                    # 计算网络写入的变化趋势
                    if len(net_write) > 1:
                        write_trend = np.polyfit(range(len(net_write)), net_write, 1)[0]
                        if 'network_trends' not in self.analysis_result:
                            self.analysis_result['network_trends'] = {}
                        if interface_name not in self.analysis_result['network_trends']:
                            self.analysis_result['network_trends'][interface_name] = {}
                        self.analysis_result['network_trends'][interface_name]['write'] = {
                            'slope': write_trend,
                            'direction': '上升' if write_trend > 1 else ('下降' if write_trend < -1 else '稳定')
                        }

        self.log("性能指标计算完成")

    def generate_excel_report(self):
        """生成Excel报告。"""
        self.log("生成Excel报告...")

        # 使用现有的ExcelGenerator
        excel_generator = ExcelGenerator(self.analysis_result)

        # 添加当前时间戳到文件名，确保唯一性
        current_time = datetime.now().strftime("%Y%m%d%H%M%S")
        output_file = os.path.join(
            self.output_dir,
            os.path.splitext(os.path.basename(self.nmon_file_path))[0] + f"_analyzed_{current_time}.xlsx"
        )
        excel_generator.generate(output_file)

        # 根据选项决定是否添加时间序列图表
        if hasattr(self, 'include_charts') and self.include_charts:
            self.log("添加性能图表到Excel报告...")
            self.add_charts_to_excel(output_file)
        else:
            self.log("跳过添加性能图表，因为选项未启用")

        return output_file

    def add_charts_to_excel(self, excel_file):
        """
        向Excel文件添加时间序列图表。

        参数:
            excel_file: Excel文件路径
        """
        self.log("添加图表到Excel报告...")

        # 处理时间戳
        excel_timestamps = []  # 用于Excel表格的完整时间戳
        chart_timestamps = []   # 用于图表的时间戳（优化显示）

        # 确定时间戳的数量
        total_timestamps = len(self.time_series_data['timestamps'])
        self.log(f"总时间戳数量: {total_timestamps}")

        # 计算图表标签的显示间隔
        # 根据时间戳数量决定标签显示策略
        label_interval = 1
        if total_timestamps > 100:
            # 如果时间戳过多，计算一个合适的间隔显示标签
            label_interval = max(1, total_timestamps // 20)  # 大约20个标签
            self.log(f"时间戳过多，图表标签显示间隔: {label_interval}")

        # 处理所有时间戳
        for i, ts in enumerate(self.time_series_data['timestamps']):
            # 处理Excel表格的完整时间戳
            if isinstance(ts, dict) and 'timestamp' in ts:
                # 完整格式化时间戳
                excel_timestamps.append(ts['timestamp'].strftime("%m-%d %H:%M:%S"))  # 显示月日小时分钟秒
            elif isinstance(ts, dict) and 'date' in ts and 'time' in ts:
                # 如果没有timestamp字段，使用日期和时间字段
                excel_timestamps.append(f"{ts['date']} {ts['time']}")
            else:
                # 如果无法解析，使用索引作为标识
                excel_timestamps.append(f"时间点 {i+1}")

            # 处理图表的时间戳标签（优化显示）
            if i % label_interval == 0 or i == total_timestamps - 1:  # 每隔一定间隔显示标签，并确保显示最后一个
                if isinstance(ts, dict) and 'timestamp' in ts:
                    chart_timestamps.append(ts['timestamp'].strftime("%H:%M"))  # 图表上只显示小时和分钟
                elif isinstance(ts, dict) and 'date' in ts and 'time' in ts:
                    time_parts = ts['time'].split(':')[:2]  # 只取小时和分钟
                    chart_timestamps.append(':'.join(time_parts))
                else:
                    chart_timestamps.append(f"时间点 {i+1}")
            else:
                chart_timestamps.append("")  # 空字符串不显示标签

        # 首先生成图表并保存
        self._generate_performance_charts(chart_timestamps)

        # 创建一个ExcelWriter对象
        with pd.ExcelWriter(excel_file, engine='openpyxl', mode='a') as writer:
            # 添加CPU时间序列数据
            cpu_df = pd.DataFrame({
                '时间戳': excel_timestamps,
                'CPU使用率(%)': self.time_series_data['cpu']['busy'],
                'CPU用户态(%)': self.time_series_data['cpu']['user'],
                'CPU系统态(%)': self.time_series_data['cpu']['sys'],
                'CPU等待IO(%)': self.time_series_data['cpu']['wait'],
                'CPU空闲(%)': self.time_series_data['cpu']['idle']
            })
            cpu_df.to_excel(writer, sheet_name='CPU时间序列', index=False)

            # 添加内存时间序列数据
            memory_data = {
                '时间戳': excel_timestamps,
                '内存总量(MB)': self.time_series_data['memory']['total'],
                '内存空闲(MB)': self.time_series_data['memory']['free'],
                '内存使用率(%)': self.time_series_data['memory']['used_percent']
            }

            # 添加其他内存字段
            for field, values in self.time_series_data['memory'].items():
                if field not in ['total', 'free', 'used_percent']:
                    # 将字段名转换为可读格式
                    readable_field = field.replace('_', ' ').title()
                    memory_data[f'{readable_field}(MB)'] = values

            memory_df = pd.DataFrame(memory_data)

            # 添加内存使用率计算公式说明
            formula_df = pd.DataFrame({
                '内存使用率计算公式': [
                    '(memtotal - memfree - cached - buffers) / memtotal * 100',
                    '该公式考虑了缓存和缓冲区，更准确地反映了实际内存使用情况。'
                ]
            })

            # 将内存数据和公式说明合并
            memory_with_formula_df = pd.concat([memory_df, pd.DataFrame([[''] * len(memory_df.columns)]), formula_df])

            # 将合并后的数据写入工作表
            memory_with_formula_df.to_excel(writer, sheet_name='内存时间序列', index=False)

            # 添加磁盘时间序列数据
            for disk_name, disk_data in self.time_series_data['disk'].items():
                disk_df = pd.DataFrame({
                    '时间戳': excel_timestamps,
                    f'{disk_name} 使用率(%)': disk_data['busy'],
                    f'{disk_name} 读取(KB/s)': disk_data['read'],
                    f'{disk_name} 写入(KB/s)': disk_data['write']
                })
                disk_df.to_excel(writer, sheet_name=f'磁盘_{disk_name[:10]}', index=False)

            # 添加网络时间序列数据
            for interface_name, interface_data in self.time_series_data['network'].items():
                net_df = pd.DataFrame({
                    '时间戳': excel_timestamps,
                    f'{interface_name} 读取(KB/s)': interface_data['read'],
                    f'{interface_name} 写入(KB/s)': interface_data['write']
                })
                net_df.to_excel(writer, sheet_name=f'网络_{interface_name[:10]}', index=False)

            # 添加性能趋势分析
            trend_data = {
                '指标': [],
                '趋势': [],
                '波动性': []
            }

            # CPU趋势
            if 'cpu_trend' in self.analysis_result:
                trend_data['指标'].append('CPU使用率')
                trend_data['趋势'].append(self.analysis_result['cpu_trend']['direction'])
                trend_data['波动性'].append(self.analysis_result['cpu_volatility']['level'])

            # 内存趋势
            if 'memory_trend' in self.analysis_result:
                trend_data['指标'].append('内存使用率')
                trend_data['趋势'].append(self.analysis_result['memory_trend']['direction'])
                trend_data['波动性'].append(self.analysis_result['memory_volatility']['level'])

            # 磁盘趋势
            if 'disk_trends' in self.analysis_result:
                for disk_name, trend in self.analysis_result['disk_trends'].items():
                    trend_data['指标'].append(f'磁盘 {disk_name} 使用率')
                    trend_data['趋势'].append(trend['direction'])
                    trend_data['波动性'].append(self.analysis_result['disk_volatility'][disk_name]['level'])

            # 网络趋势
            if 'network_trends' in self.analysis_result:
                for interface_name, trends in self.analysis_result['network_trends'].items():
                    if 'read' in trends:
                        trend_data['指标'].append(f'网络 {interface_name} 读取')
                        trend_data['趋势'].append(trends['read']['direction'])
                        trend_data['波动性'].append('--')
                    if 'write' in trends:
                        trend_data['指标'].append(f'网络 {interface_name} 写入')
                        trend_data['趋势'].append(trends['write']['direction'])
                        trend_data['波动性'].append('--')

            # 创建趋势分析表
            trend_df = pd.DataFrame(trend_data)
            trend_df.to_excel(writer, sheet_name='性能趋势分析', index=False)

        self.log("图表添加完成")

    def _generate_performance_charts(self, chart_timestamps):
        """
        生成性能图表并保存到文件。

        参数:
            chart_timestamps: 用于图表的格式化时间戳列表
        """
        self.log("生成性能图表...")

        # 创建图表输出目录
        charts_dir = os.path.join(self.output_dir, 'charts')
        if not os.path.exists(charts_dir):
            os.makedirs(charts_dir)

        # 生成CPU使用率图表
        self._generate_cpu_chart(chart_timestamps, charts_dir)

        # 生成内存使用率图表
        self._generate_memory_chart(chart_timestamps, charts_dir)

        # 生成磁盘使用率图表
        self._generate_disk_charts(chart_timestamps, charts_dir)

        # 生成网络使用率图表
        self._generate_network_charts(chart_timestamps, charts_dir)

        self.log(f"性能图表生成完成，保存在: {charts_dir}")

    def _generate_cpu_chart(self, chart_timestamps, charts_dir):
        """生成CPU使用率图表。"""
        self.log("生成CPU使用率图表...")

        # 创建CPU图表
        plt.figure(figsize=(12, 6))

        # 创建数据点索引
        x_indices = list(range(len(chart_timestamps)))

        # 绘制数据线
        plt.plot(x_indices, self.time_series_data['cpu']['busy'], label='CPU Usage' if not use_chinese else 'CPU使用率', marker='o', markersize=3)
        plt.plot(x_indices, self.time_series_data['cpu']['user'], label='User' if not use_chinese else '用户态', marker='^', markersize=3)
        plt.plot(x_indices, self.time_series_data['cpu']['sys'], label='System' if not use_chinese else '系统态', marker='s', markersize=3)
        plt.plot(x_indices, self.time_series_data['cpu']['wait'], label='IO Wait' if not use_chinese else 'IO等待', marker='d', markersize=3)

        # 设置横轴刻度和标签
        plt.xticks(x_indices, chart_timestamps)

        # 设置图表属性
        plt.title('CPU Usage Time Series' if not use_chinese else 'CPU使用率时间序列')
        plt.xlabel('Time' if not use_chinese else '时间')
        plt.ylabel('Usage (%)' if not use_chinese else '使用率 (%)')
        plt.grid(True)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存图表
        cpu_chart_path = os.path.join(charts_dir, 'cpu_usage.png')
        plt.savefig(cpu_chart_path, dpi=300)
        plt.close()

        self.log(f"CPU使用率图表已保存到: {cpu_chart_path}")

    def _generate_memory_chart(self, chart_timestamps, charts_dir):
        """生成内存使用率图表。"""
        self.log("生成内存使用率图表...")

        # 创建内存图表
        plt.figure(figsize=(12, 6))

        # 创建数据点索引
        x_indices = list(range(len(chart_timestamps)))

        # 绘制内存使用率
        plt.plot(x_indices, self.time_series_data['memory']['used_percent'],
                 label='Memory Usage' if not use_chinese else '内存使用率',
                 marker='o', markersize=3, color='red')

        # 设置横轴刻度和标签
        plt.xticks(x_indices, chart_timestamps)

        # 设置图表属性
        plt.title('Memory Usage Time Series' if not use_chinese else '内存使用率时间序列')
        plt.xlabel('Time' if not use_chinese else '时间')
        plt.ylabel('Usage (%)' if not use_chinese else '使用率 (%)')
        plt.grid(True)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存图表
        memory_chart_path = os.path.join(charts_dir, 'memory_usage.png')
        plt.savefig(memory_chart_path, dpi=300)
        plt.close()

        self.log(f"内存使用率图表已保存到: {memory_chart_path}")

        # 创建内存详情图表
        plt.figure(figsize=(12, 6))

        # 创建数据点索引
        x_indices = list(range(len(chart_timestamps)))

        # 绘制内存总量和空闲量
        plt.plot(x_indices, self.time_series_data['memory']['total'],
                 label='Total Memory' if not use_chinese else '内存总量',
                 marker='o', markersize=3, color='blue')
        plt.plot(x_indices, self.time_series_data['memory']['free'],
                 label='Free Memory' if not use_chinese else '内存空闲',
                 marker='^', markersize=3, color='green')

        # 设置横轴刻度和标签
        plt.xticks(x_indices, chart_timestamps)

        # 绘制cached和buffers（如果存在）
        if 'cached' in self.time_series_data['memory']:
            plt.plot(x_indices, self.time_series_data['memory']['cached'],
                     label='Cached' if not use_chinese else '缓存',
                     marker='s', markersize=3, color='purple')
        if 'buffers' in self.time_series_data['memory']:
            plt.plot(x_indices, self.time_series_data['memory']['buffers'],
                     label='Buffers' if not use_chinese else '缓冲区',
                     marker='d', markersize=3, color='orange')

        # 设置图表属性
        plt.title('Memory Details Time Series' if not use_chinese else '内存详情时间序列')
        plt.xlabel('Time' if not use_chinese else '时间')
        plt.ylabel('Memory (MB)' if not use_chinese else '内存 (MB)')
        plt.grid(True)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存图表
        memory_details_chart_path = os.path.join(charts_dir, 'memory_details.png')
        plt.savefig(memory_details_chart_path, dpi=300)
        plt.close()

        self.log(f"内存详情图表已保存到: {memory_details_chart_path}")

    def _generate_disk_charts(self, chart_timestamps, charts_dir):
        """生成磁盘使用率图表。"""
        self.log("生成磁盘使用率图表...")

        # 如果没有磁盘数据，跳过
        if not self.time_series_data['disk']:
            self.log("没有磁盘数据，跳过磁盘图表生成")
            return

        # 创建磁盘使用率图表
        plt.figure(figsize=(12, 6))

        # 创建数据点索引
        x_indices = list(range(len(chart_timestamps)))

        # 选择前5个磁盘显示，避免图表过于复杂
        disk_names = list(self.time_series_data['disk'].keys())[:5]

        for disk_name in disk_names:
            disk_data = self.time_series_data['disk'][disk_name]
            plt.plot(x_indices, disk_data['busy'],
                     label=f'{disk_name} Usage' if not use_chinese else f'{disk_name} 使用率',
                     marker='o', markersize=3)

        # 设置横轴刻度和标签
        plt.xticks(x_indices, chart_timestamps)

        # 设置图表属性
        plt.title('Disk Usage Time Series' if not use_chinese else '磁盘使用率时间序列')
        plt.xlabel('Time' if not use_chinese else '时间')
        plt.ylabel('Usage (%)' if not use_chinese else '使用率 (%)')
        plt.grid(True)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存图表
        disk_usage_chart_path = os.path.join(charts_dir, 'disk_usage.png')
        plt.savefig(disk_usage_chart_path, dpi=300)
        plt.close()

        self.log(f"磁盘使用率图表已保存到: {disk_usage_chart_path}")

        # 创建磁盘IO图表
        plt.figure(figsize=(12, 6))

        # 创建数据点索引
        x_indices = list(range(len(chart_timestamps)))

        for disk_name in disk_names:
            disk_data = self.time_series_data['disk'][disk_name]
            plt.plot(x_indices, disk_data['read'],
                     label=f'{disk_name} Read' if not use_chinese else f'{disk_name} 读取',
                     marker='^', markersize=3, linestyle='--')
            plt.plot(x_indices, disk_data['write'],
                     label=f'{disk_name} Write' if not use_chinese else f'{disk_name} 写入',
                     marker='s', markersize=3, linestyle='-.')

        # 设置横轴刻度和标签
        plt.xticks(x_indices, chart_timestamps)

        # 设置图表属性
        plt.title('Disk IO Time Series' if not use_chinese else '磁盘IO时间序列')
        plt.xlabel('Time' if not use_chinese else '时间')
        plt.ylabel('IO (KB/s)')
        plt.grid(True)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存图表
        disk_io_chart_path = os.path.join(charts_dir, 'disk_io.png')
        plt.savefig(disk_io_chart_path, dpi=300)
        plt.close()

        self.log(f"磁盘IO图表已保存到: {disk_io_chart_path}")

    def _generate_network_charts(self, chart_timestamps, charts_dir):
        """生成网络使用率图表。"""
        self.log("生成网络使用率图表...")

        # 如果没有网络数据，跳过
        if not self.time_series_data['network']:
            self.log("没有网络数据，跳过网络图表生成")
            return

        # 创建网络使用率图表
        plt.figure(figsize=(12, 6))

        # 创建数据点索引
        x_indices = list(range(len(chart_timestamps)))

        # 选择前3个网络接口显示，避免图表过于复杂
        interface_names = list(self.time_series_data['network'].keys())[:3]

        for interface_name in interface_names:
            interface_data = self.time_series_data['network'][interface_name]
            plt.plot(x_indices, interface_data['read'],
                     label=f'{interface_name} Read' if not use_chinese else f'{interface_name} 读取',
                     marker='^', markersize=3, linestyle='--')
            plt.plot(x_indices, interface_data['write'],
                     label=f'{interface_name} Write' if not use_chinese else f'{interface_name} 写入',
                     marker='s', markersize=3, linestyle='-.')

        # 设置横轴刻度和标签
        plt.xticks(x_indices, chart_timestamps)

        # 设置图表属性
        plt.title('Network Traffic Time Series' if not use_chinese else '网络使用率时间序列')
        plt.xlabel('Time' if not use_chinese else '时间')
        plt.ylabel('Traffic (KB/s)' if not use_chinese else '流量 (KB/s)')
        plt.grid(True)
        plt.legend()
        plt.xticks(rotation=45)
        plt.tight_layout()

        # 保存图表
        network_chart_path = os.path.join(charts_dir, 'network_usage.png')
        plt.savefig(network_chart_path, dpi=300)
        plt.close()

        self.log(f"网络使用率图表已保存到: {network_chart_path}")


def analyze_nmon_file(nmon_file_path, output_dir=None, progress_callback=None, log_callback=None):
    """
    分析nmon文件的便捷函数。

    参数:
        nmon_file_path: nmon文件路径
        output_dir: 输出目录，默认为nmon文件所在目录
        progress_callback: 进度回调函数
        log_callback: 日志回调函数

    返回:
        dict: 分析结果
    """
    analyzer = RawNmonAnalyzer(nmon_file_path, output_dir, progress_callback, log_callback)
    return analyzer.analyze()


if __name__ == "__main__":
    # 如果直接运行此脚本，则分析命令行参数中指定的nmon文件
    if len(sys.argv) > 1:
        nmon_file = sys.argv[1]
        output_dir = sys.argv[2] if len(sys.argv) > 2 else None

        print(f"分析nmon文件: {nmon_file}")
        result = analyze_nmon_file(nmon_file, output_dir)
        print(f"分析完成，结果已保存到: {result['output_file']}")
    else:
        print("用法: python raw_nmon_analyzer.py <nmon文件路径> [输出目录]")
