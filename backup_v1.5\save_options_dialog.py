from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QRadioButton, QPushButton, QButtonGroup, QGroupBox,
                             QSpinBox, QFormLayout, QFrame)
from PyQt5.QtCore import Qt, QSettings
from PyQt5.QtGui import QFont, QIcon

class SaveOptionsDialog(QDialog):
    """用于选择保存选项的对话框"""

    def __init__(self, parent=None, file_count=0):
        """
        初始化保存选项对话框

        参数:
            parent: 父窗口
            file_count: 文件数量
        """
        super().__init__(parent)
        self.file_count = file_count
        self.selected_mode = 'auto'  # 默认选项
        self.batch_size = 10  # 默认批次大小

        self.setWindowTitle("保存选项")
        self.setMinimumWidth(400)

        # 应用与主窗口相同的样式
        self.apply_main_window_style()

        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        layout.setSpacing(15)  # 增加元素间的间距
        layout.setContentsMargins(20, 20, 20, 20)  # 增加边距

        # 添加标题
        title_label = QLabel("保存选项")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 添加分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)

        # 添加说明标签
        if self.file_count > 20:
            info_text = f"检测到大量文件 ({self.file_count}个)，请选择保存方式:"
        else:
            info_text = f"选择保存方式 (当前文件数: {self.file_count}个):"

        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("font-size: 12px; margin-bottom: 10px;")
        layout.addWidget(info_label)

        # 创建选项组
        options_group = QGroupBox("选择保存模式")
        # 样式已在apply_main_window_style中设置
        options_layout = QVBoxLayout()
        options_layout.setSpacing(10)  # 增加选项间的间距

        # 创建单选按钮并添加图标
        self.auto_radio = QRadioButton("自动模式")
        self.auto_radio.setIcon(QIcon.fromTheme("system-run", QIcon()))
        self.auto_radio.setStyleSheet("font-weight: bold;")
        auto_desc = QLabel("根据文件数量自动选择最佳方式")
        auto_desc.setStyleSheet(self.desc_style)

        self.summary_radio = QRadioButton("仅保存摘要")
        self.summary_radio.setIcon(QIcon.fromTheme("text-x-generic", QIcon()))
        self.summary_radio.setStyleSheet("font-weight: bold;")
        summary_desc = QLabel("不生成图表，速度最快")
        summary_desc.setStyleSheet(self.desc_style)

        self.full_radio = QRadioButton("完整模式")
        self.full_radio.setIcon(QIcon.fromTheme("insert-image", QIcon()))
        self.full_radio.setStyleSheet("font-weight: bold;")
        full_desc = QLabel("生成所有图表，适用于少量文件")
        full_desc.setStyleSheet(self.desc_style)

        self.batch_radio = QRadioButton("批处理模式")
        self.batch_radio.setIcon(QIcon.fromTheme("view-paged", QIcon()))
        self.batch_radio.setStyleSheet("font-weight: bold;")
        batch_desc = QLabel("分批生成图表，适用于大量文件")
        batch_desc.setStyleSheet(self.desc_style)

        # 设置默认选项
        if self.file_count > 20:
            self.batch_radio.setChecked(True)
            self.selected_mode = 'batch'
        else:
            self.auto_radio.setChecked(True)
            self.selected_mode = 'auto'

        # 添加单选按钮和描述到布局
        auto_layout = QVBoxLayout()
        auto_layout.addWidget(self.auto_radio)
        auto_layout.addWidget(auto_desc)
        options_layout.addLayout(auto_layout)

        # 添加分隔线
        line1 = QFrame()
        line1.setFrameShape(QFrame.HLine)
        line1.setFrameShadow(QFrame.Sunken)
        line1.setStyleSheet("color: #e0e0e0;")
        options_layout.addWidget(line1)

        summary_layout = QVBoxLayout()
        summary_layout.addWidget(self.summary_radio)
        summary_layout.addWidget(summary_desc)
        options_layout.addLayout(summary_layout)

        # 添加分隔线
        line2 = QFrame()
        line2.setFrameShape(QFrame.HLine)
        line2.setFrameShadow(QFrame.Sunken)
        line2.setStyleSheet("color: #e0e0e0;")
        options_layout.addWidget(line2)

        full_layout = QVBoxLayout()
        full_layout.addWidget(self.full_radio)
        full_layout.addWidget(full_desc)
        options_layout.addLayout(full_layout)

        # 添加分隔线
        line3 = QFrame()
        line3.setFrameShape(QFrame.HLine)
        line3.setFrameShadow(QFrame.Sunken)
        line3.setStyleSheet("color: #e0e0e0;")
        options_layout.addWidget(line3)

        batch_layout = QVBoxLayout()
        batch_layout.addWidget(self.batch_radio)
        batch_layout.addWidget(batch_desc)
        options_layout.addLayout(batch_layout)

        # 添加批次大小选择
        batch_size_layout = QHBoxLayout()
        batch_size_layout.addSpacing(25)  # 缩进

        batch_size_label = QLabel("批次大小:")
        batch_size_label.setStyleSheet(self.batch_size_label_style)
        self.batch_size_spinbox = QSpinBox()
        self.batch_size_spinbox.setMinimum(1)
        self.batch_size_spinbox.setMaximum(100)
        self.batch_size_spinbox.setValue(self.batch_size)
        self.batch_size_spinbox.setEnabled(self.batch_radio.isChecked())
        self.batch_size_spinbox.valueChanged.connect(self.on_batch_size_changed)
        # 样式已在apply_main_window_style中设置

        batch_size_layout.addWidget(batch_size_label)
        batch_size_layout.addWidget(self.batch_size_spinbox)
        batch_size_layout.addStretch(1)

        batch_layout.addLayout(batch_size_layout)

        # 创建按钮组
        self.button_group = QButtonGroup()
        self.button_group.addButton(self.auto_radio, 1)
        self.button_group.addButton(self.summary_radio, 2)
        self.button_group.addButton(self.full_radio, 3)
        self.button_group.addButton(self.batch_radio, 4)
        self.button_group.buttonClicked.connect(self.on_button_clicked)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # 我们已经在每个选项下添加了描述，不需要再添加说明文本

        # 添加按钮布局
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 15, 0, 0)  # 增加上边距

        ok_button = QPushButton("确定")
        ok_button.setObjectName("ok_button")  # 设置ID以便在样式表中选择
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.setMinimumWidth(100)

        cancel_button = QPushButton("取消")
        cancel_button.setObjectName("cancel_button")  # 设置ID以便在样式表中选择
        cancel_button.setCursor(Qt.PointingHandCursor)
        cancel_button.setMinimumWidth(100)

        # 使用自定义方法处理确定按钮点击
        ok_button.clicked.connect(self.on_ok_clicked)
        cancel_button.clicked.connect(self.reject)

        button_layout.addStretch(1)
        button_layout.addWidget(ok_button)
        button_layout.addSpacing(10)  # 按钮之间的间距
        button_layout.addWidget(cancel_button)
        button_layout.addStretch(1)

        layout.addLayout(button_layout)

        # 添加提示信息
        tip_label = QLabel("提示: 根据文件数量和系统性能选择适合的保存模式")
        tip_label.setStyleSheet(self.tip_style)
        tip_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(tip_label)

        self.setLayout(layout)

    def on_button_clicked(self, button):
        """处理按钮点击事件"""
        if button == self.auto_radio:
            self.selected_mode = 'auto'
            self.batch_size_spinbox.setEnabled(False)
        elif button == self.summary_radio:
            self.selected_mode = 'summary_only'
            self.batch_size_spinbox.setEnabled(False)
        elif button == self.full_radio:
            self.selected_mode = 'full'
            self.batch_size_spinbox.setEnabled(False)
        elif button == self.batch_radio:
            self.selected_mode = 'batch'
            self.batch_size_spinbox.setEnabled(True)

    def on_batch_size_changed(self, value):
        """处理批次大小改变事件"""
        self.batch_size = value

    def on_ok_clicked(self):
        """处理确定按钮点击事件"""
        # 先记录当前选择的模式
        mode = self.selected_mode
        # 然后接受对话框
        self.accept()

    def get_selected_mode(self):
        """获取选择的保存模式"""
        return self.selected_mode

    def get_batch_size(self):
        """获取选择的批次大小"""
        return self.batch_size

    def apply_main_window_style(self):
        """应用与主窗口相同的样式"""
        # 从设置中读取样式
        settings = QSettings("PerformanceAnalyzer", "Settings")
        style_name = settings.value("ui_style", "modern_blue", type=str)
        font_size = settings.value("font_size", 9, type=int)

        # 设置字体大小
        font = QFont()
        font.setPointSize(font_size)
        self.setFont(font)

        # 根据选择的样式设置样式表
        if style_name == "modern_blue":
            # 现代蓝模式
            self.setStyleSheet("""
                QDialog { background-color: #f0f5fa; color: #333333; }
                QLabel { color: #333333; }
                QGroupBox { border: 1px solid #c0d0e0; border-radius: 5px; margin-top: 10px; font-weight: bold; color: #333333; }
                QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px; }
                QSpinBox { background-color: white; color: #333333; border: 1px solid #c0d0e0; padding: 3px; }
                QSpinBox::up-button, QSpinBox::down-button { width: 16px; border-left: 1px solid #c0d0e0; }
                QFrame[frameShape=\"4\"] { color: #e0e0e0; }
            """)

            # 更新特定标签的样式
            self.desc_style = "color: #666; margin-left: 25px;"
            self.tip_style = "color: #666; font-style: italic; margin-top: 10px;"
            self.batch_size_label_style = "color: #444;"
        elif style_name == "light_classic":
            # 浅色经典模式
            self.setStyleSheet("""
                QDialog { background-color: #f5f5f5; color: #333333; }
                QLabel { color: #333333; }
                QGroupBox { border: 1px solid #d0d0d0; border-radius: 5px; margin-top: 10px; font-weight: bold; color: #333333; }
                QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px; }
                QSpinBox { background-color: white; color: #333333; border: 1px solid #d0d0d0; padding: 3px; }
                QSpinBox::up-button, QSpinBox::down-button { width: 16px; border-left: 1px solid #d0d0d0; }
                QFrame[frameShape=\"4\"] { color: #e0e0e0; }
            """)

            # 更新特定标签的样式
            self.desc_style = "color: #666; margin-left: 25px;"
            self.tip_style = "color: #666; font-style: italic; margin-top: 10px;"
            self.batch_size_label_style = "color: #444;"
        else:  # system_default
            # 系统默认模式
            self.setStyleSheet("")

            # 更新特定标签的样式
            self.desc_style = "color: #666; margin-left: 25px;"
            self.tip_style = "color: #666; font-style: italic; margin-top: 10px;"
            self.batch_size_label_style = "color: #444;"
