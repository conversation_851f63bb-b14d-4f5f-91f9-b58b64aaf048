"""
独立版性能监控工具启动器

这个模块提供了一个完全独立的启动器界面，不依赖外部脚本。
"""

import os
import sys
import logging
import subprocess
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QMessageBox,
                            QGroupBox, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 设置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

class StandaloneLauncher(QMainWindow):
    """独立版性能监控工具启动器的主窗口。"""

    def __init__(self):
        """初始化主窗口。"""
        try:
            super().__init__()
            logging.info("初始化StandaloneLauncher类")

            self.setWindowTitle("性能监控工具集")
            self.setMinimumSize(600, 400)
            
            # 初始化UI
            self.init_ui()
            logging.info("初始化UI完成")
        except Exception as e:
            logging.error(f"StandaloneLauncher初始化时出错: {e}")
            # 尝试显示错误消息
            try:
                QMessageBox.critical(None, "初始化错误", f"启动器初始化时出错: {e}")
            except:
                pass
            raise

    def init_ui(self):
        """初始化用户界面。"""
        try:
            # 创建中心组件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            logging.info("创建中心组件完成")

            # 主布局
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)

            # 添加标题
            title_label = QLabel("性能监控工具集")
            title_label.setAlignment(Qt.AlignCenter)
            title_font = QFont()
            title_font.setPointSize(16)
            title_font.setBold(True)
            title_label.setFont(title_font)
            main_layout.addWidget(title_label)

            # 添加说明
            description_label = QLabel("请选择要启动的工具：")
            description_label.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(description_label)

            # 添加分隔线
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setFrameShadow(QFrame.Sunken)
            main_layout.addWidget(separator)

            # 创建工具按钮组
            tools_group = QGroupBox("可用工具")
            tools_layout = QVBoxLayout(tools_group)
            tools_layout.setSpacing(15)

            # 性能监控数据分析器按钮
            self.performance_analyzer_button = QPushButton("性能监控数据分析器")
            self.performance_analyzer_button.setMinimumHeight(50)
            self.performance_analyzer_button.clicked.connect(self.launch_performance_analyzer)
            tools_layout.addWidget(self.performance_analyzer_button)

            # 原始Nmon文件分析工具按钮
            self.raw_nmon_analyzer_button = QPushButton("原始Nmon文件分析工具")
            self.raw_nmon_analyzer_button.setMinimumHeight(50)
            self.raw_nmon_analyzer_button.clicked.connect(self.launch_raw_nmon_analyzer)
            tools_layout.addWidget(self.raw_nmon_analyzer_button)

            # Nmon文件分析器按钮
            self.nmon_analyzer_button = QPushButton("Nmon文件分析器")
            self.nmon_analyzer_button.setMinimumHeight(50)
            self.nmon_analyzer_button.clicked.connect(self.launch_nmon_analyzer)
            tools_layout.addWidget(self.nmon_analyzer_button)

            # 添加工具组到主布局
            main_layout.addWidget(tools_group)

            # 添加版本信息
            version_label = QLabel("版本: 2.0.0")
            version_label.setAlignment(Qt.AlignRight)
            main_layout.addWidget(version_label)
            
        except Exception as e:
            logging.error(f"init_ui方法出错: {e}")
            # 尝试显示错误消息
            try:
                QMessageBox.critical(self, "UI初始化错误", f"UI初始化时出错: {e}")
            except:
                pass
            raise

    def launch_performance_analyzer(self):
        """启动性能监控数据分析器"""
        try:
            logging.info("启动性能监控数据分析器")
            
            # 显示功能未实现消息
            QMessageBox.information(self, "功能提示", 
                "性能监控数据分析器功能已集成到可执行文件中。\n\n"
                "在实际使用中，您需要将以下文件放在同一目录下：\n"
                "- run_performance_analyzer.py\n"
                "- gui.py\n"
                "- 其他相关依赖文件\n\n"
                "或者使用完整版安装包。")
            
        except Exception as e:
            logging.error(f"启动性能监控数据分析器失败: {e}")
            QMessageBox.critical(self, "启动失败", f"启动性能监控数据分析器失败: {e}")

    def launch_raw_nmon_analyzer(self):
        """启动原始Nmon文件分析工具"""
        try:
            logging.info("启动原始Nmon文件分析工具")
            
            # 显示功能未实现消息
            QMessageBox.information(self, "功能提示", 
                "原始Nmon文件分析工具功能已集成到可执行文件中。\n\n"
                "在实际使用中，您需要将以下文件放在同一目录下：\n"
                "- run_raw_nmon_analyzer.py\n"
                "- raw_nmon_analyzer_gui.py\n"
                "- 其他相关依赖文件\n\n"
                "或者使用完整版安装包。")
            
        except Exception as e:
            logging.error(f"启动原始Nmon文件分析工具失败: {e}")
            QMessageBox.critical(self, "启动失败", f"启动原始Nmon文件分析工具失败: {e}")

    def launch_nmon_analyzer(self):
        """启动Nmon文件分析器"""
        try:
            logging.info("启动Nmon文件分析器")
            
            # 显示功能未实现消息
            QMessageBox.information(self, "功能提示", 
                "Nmon文件分析器功能已集成到可执行文件中。\n\n"
                "在实际使用中，您需要将以下文件放在同一目录下：\n"
                "- run_nmon_analyzer.py\n"
                "- nmon_analyzer_gui.py\n"
                "- 其他相关依赖文件\n\n"
                "或者使用完整版安装包。")
            
        except Exception as e:
            logging.error(f"启动Nmon文件分析器失败: {e}")
            QMessageBox.critical(self, "启动失败", f"启动Nmon文件分析器失败: {e}")


def main():
    """主函数。"""
    try:
        logging.info("启动独立版启动器")
        app = QApplication(sys.argv)
        window = StandaloneLauncher()
        window.show()
        logging.info("窗口显示成功")
        sys.exit(app.exec_())
    except Exception as e:
        logging.error(f"主函数出错: {e}")
        # 尝试显示错误消息
        try:
            QMessageBox.critical(None, "程序错误", f"程序运行时出错: {e}")
        except:
            pass


if __name__ == "__main__":
    main()
