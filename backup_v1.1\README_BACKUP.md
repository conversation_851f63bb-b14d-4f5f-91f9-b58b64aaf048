# 性能监控数据分析器 V1.1 - 源代码备份

这是性能监控数据分析器 V1.1 版本的完整源代码备份。

## 备份内容

此备份包含以下内容：

1. 所有Python源代码文件
2. 内嵌字体文件（fonts目录）
3. 应用图标文件（icons目录）
4. 配置和构建文件
5. 版本信息文件

## 版本特性

V1.1版本包含以下主要特性和改进：

- 内嵌字体系统，提高跨平台兼容性
- 改进的用户界面，包括更好的按钮点击反馈
- 多种保存模式（自动模式、完整模式、批处理模式）
- 多种界面主题（现代蓝、深色模式、浅色经典、系统默认）
- 修复状态栏问题
- 更新README文件，提供全面的文档
- 添加版本信息
- 创建可执行文件分发包

## 恢复备份

如需从此备份恢复源代码，只需将所有文件复制到一个新的工作目录即可。

## 构建可执行文件

要从源代码构建可执行文件，请运行：

```
python -m PyInstaller performance_analyzer.spec
```

构建完成后，可执行文件将位于`dist/性能监控数据分析器_V1.1`目录中。

## 备份日期

备份日期：2025-04-24
