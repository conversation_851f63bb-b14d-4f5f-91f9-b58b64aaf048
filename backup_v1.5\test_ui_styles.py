import sys
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox, QLabel, QComboBox
from PyQt5.QtCore import QSettings
from save_options_dialog import SaveOptionsDialog
from font_settings_dialog import FontSettingsDialog
from settings_dialog import SettingsDialog

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试界面样式")
        
        # 创建布局
        layout = QVBoxLayout()
        
        # 添加标签
        label = QLabel("界面样式测试 - 已移除深色模式")
        layout.addWidget(label)
        
        # 添加样式选择下拉框
        style_label = QLabel("选择样式:")
        layout.addWidget(style_label)
        
        self.style_combo = QComboBox()
        self.style_combo.addItem("现代蓝 (Modern Blue)", "modern_blue")
        self.style_combo.addItem("浅色经典 (Light Classic)", "light_classic")
        self.style_combo.addItem("系统默认 (System Default)", "system_default")
        self.style_combo.currentIndexChanged.connect(self.change_style)
        layout.addWidget(self.style_combo)
        
        # 创建按钮
        self.settings_button = QPushButton("显示设置对话框")
        self.settings_button.clicked.connect(self.show_settings)
        layout.addWidget(self.settings_button)
        
        self.save_options_button = QPushButton("显示保存选项对话框")
        self.save_options_button.clicked.connect(self.show_save_options)
        layout.addWidget(self.save_options_button)
        
        self.font_settings_button = QPushButton("显示字体设置对话框")
        self.font_settings_button.clicked.connect(self.show_font_settings)
        layout.addWidget(self.font_settings_button)
        
        # 创建中心部件
        central_widget = QWidget()
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)
        
        # 设置默认样式
        self.settings = QSettings("PerformanceAnalyzer", "Settings")
        self.settings.setValue("ui_style", "modern_blue")
    
    def change_style(self, index):
        style = self.style_combo.itemData(index)
        self.settings.setValue("ui_style", style)
        QMessageBox.information(self, "样式已更改", f"已将样式更改为: {style}\n请重新打开对话框查看效果")
    
    def show_settings(self):
        dialog = SettingsDialog(self)
        dialog.exec_()
    
    def show_save_options(self):
        dialog = SaveOptionsDialog(file_count=25)
        dialog.exec_()
    
    def show_font_settings(self):
        dialog = FontSettingsDialog()
        dialog.exec_()

def main():
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
