# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(1, 5, 1, 0),
    prodvers=(1, 5, 1, 0),
    # Contains a bitmask that specifies the valid bits 'flags'r
    mask=0x3f,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x40004,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404b0',
        [StringStruct(u'CompanyName', u''),
        StringStruct(u'FileDescription', u'性能监控数据分析器'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'性能监控数据分析器'),
        StringStruct(u'LegalCopyright', u'Copyright (C) 2025'),
        StringStruct(u'OriginalFilename', u'性能监控数据分析器.exe'),
        StringStruct(u'ProductName', u'性能监控数据分析器'),
        StringStruct(u'ProductVersion', u'*******')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
- version_info.txt
- icons\app_icon.py
- fonts/font_settings.json
- fonts/Lora-Regular.ttf
- fonts/OpenSans-Regular.ttf
- fonts/Roboto-Regular.ttf
- fonts/SourceHanSansSC-Regular.otf
- fonts/SourceHanSerifSC-Regular.otf
- icons/app_icon.ico
- icons/app_icon.png
- icons/app_icon.py
