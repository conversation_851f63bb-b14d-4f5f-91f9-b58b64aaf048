import numpy as np

class DataAnalyzer:
    """用于分析从 Excel 文件中提取的数据的类。"""

    @staticmethod
    def calculate_cpu_usage(cpu_data):
        """
        计算 CPU 使用率数据的统计信息。

        参数:
            cpu_data (list): CPU 使用率百分比列表

        返回:
            dict: 包含最小值、最大值、平均值、中位数和标准差的统计信息
        """
        if not cpu_data:
            return {
                'min': 0,
                'max': 0,
                'mean': 0,
                'median': 0,
                'std': 0
            }

        return {
            'min': round(min(cpu_data), 2),
            'max': round(max(cpu_data), 2),
            'mean': round(np.mean(cpu_data), 2),
            'median': round(np.median(cpu_data), 2),
            'std': round(np.std(cpu_data), 2)
        }

    @staticmethod
    def calculate_memory_usage(mem_data):
        """
        计算内存使用率数据的统计信息。

        参数:
            mem_data (list): 内存使用率百分比列表

        返回:
            dict: 包含最小值、最大值、平均值、中位数和标准差的统计信息
        """
        if not mem_data:
            return {
                'min': 0,
                'max': 0,
                'mean': 0,
                'median': 0,
                'std': 0
            }

        return {
            'min': round(min(mem_data), 2),
            'max': round(max(mem_data), 2),
            'mean': round(np.mean(mem_data), 2),
            'median': round(np.median(mem_data), 2),
            'std': round(np.std(mem_data), 2)
        }

    @staticmethod
    def calculate_disk_usage(disk_data):
        """
        Calculate statistics for disk usage data.

        Args:
            disk_data (dict): Dictionary mapping disk names to usage data

        Returns:
            dict: Statistics for each disk and the overall maximum
        """
        if not disk_data:
            return {
                'disks': {},
                'max_disk': '',
                'max_value': 0
            }

        result = {'disks': {}}
        max_mean = 0
        max_disk = ''

        for disk, data in disk_data.items():
            if not data:
                stats = {
                    'min': 0,
                    'max': 0,
                    'mean': 0,
                    'median': 0,
                    'std': 0
                }
            else:
                stats = {
                    'min': round(min(data), 2),
                    'max': round(max(data), 2),
                    'mean': round(np.mean(data), 2),
                    'median': round(np.median(data), 2),
                    'std': round(np.std(data), 2)
                }

            result['disks'][disk] = stats

            if stats['mean'] > max_mean:
                max_mean = stats['mean']
                max_disk = disk

        result['max_disk'] = max_disk
        result['max_value'] = max_mean

        return result

    @staticmethod
    def analyze_results(results):
        """
        Analyze the results from multiple files.

        Args:
            results (list): List of result dictionaries from processed files

        Returns:
            dict: Analysis results
        """
        if not results:
            return {}

        analysis = {}

        # Analyze each file's data
        for result in results:
            file_name = result['File']
            raw_data = result['Raw Data']

            analysis[file_name] = {
                'cpu': DataAnalyzer.calculate_cpu_usage(raw_data['CPU']),
                'memory': DataAnalyzer.calculate_memory_usage(raw_data['Memory']),
                'disk': DataAnalyzer.calculate_disk_usage(raw_data['Disk'])
            }

        return analysis
