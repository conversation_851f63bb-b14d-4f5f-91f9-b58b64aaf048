@echo off
echo Building executable for Performance Analyzer V2.0...
echo.

REM Clean previous build files
echo Cleaning previous build files...
rmdir /s /q build
rmdir /s /q dist
echo.

REM Run PyInstaller with the spec file
echo Running PyInstaller...
python -m PyInstaller backup_v2.0\performance_analyzer_v2.spec
echo.

if %ERRORLEVEL% == 0 (
    echo Build completed successfully!
    echo Executable is located in the dist\性能监控数据分析器_V2.0 directory.
) else (
    echo Build failed with error code %ERRORLEVEL%.
)

echo.
pause
