import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox, QLabel
from PyQt5.QtCore import QSettings
from save_options_dialog import SaveOptionsDialog
from font_settings_dialog import FontSettingsDialog

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试深色模式")
        
        # 设置深色模式
        settings = QSettings("PerformanceAnalyzer", "Settings")
        settings.setValue("ui_style", "dark_mode")
        
        # 创建布局
        layout = QVBoxLayout()
        
        # 添加标签
        label = QLabel("深色模式测试 - 字体应该清晰可见")
        layout.addWidget(label)
        
        # 创建按钮
        self.save_options_button = QPushButton("显示保存选项对话框")
        self.save_options_button.clicked.connect(self.show_save_options)
        layout.addWidget(self.save_options_button)
        
        self.font_settings_button = QPushButton("显示字体设置对话框")
        self.font_settings_button.clicked.connect(self.show_font_settings)
        layout.addWidget(self.font_settings_button)
        
        self.success_button = QPushButton("显示成功弹窗")
        self.success_button.clicked.connect(self.show_success)
        layout.addWidget(self.success_button)
        
        # 创建中心部件
        central_widget = QWidget()
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)
    
    def show_save_options(self):
        dialog = SaveOptionsDialog(file_count=25)
        dialog.exec_()
    
    def show_font_settings(self):
        dialog = FontSettingsDialog()
        dialog.exec_()
    
    def show_success(self):
        QMessageBox.information(self, "成功", "结果已成功保存到 test.xlsx")

def main():
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
