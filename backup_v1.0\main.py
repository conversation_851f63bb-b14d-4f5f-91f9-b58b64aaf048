import sys
import os
import logging
from PyQt5.QtWidgets import QApplication
from gui import MainWindow

# 设置日志记录器，将控制台输出重定向到日志文件
class LogRedirector:
    def __init__(self, log_file=None):
        # 保存原始的stdout
        self.terminal = sys.stdout
        self.log_file = log_file
        self.log_file_handle = None

        # 如果没有提供日志文件路径，只使用控制台输出
        if log_file is None:
            return

        try:
            # 创建日志目录（如果不存在）
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)

            # 初始化日志文件
            self.log_file_handle = open(self.log_file, 'w', encoding='utf-8')
            self.log_file_handle.write("=== 性能监控数据分析器日志 ===\n\n")
            self.log_file_handle.flush()
        except Exception as e:
            print(f"无法创建日志文件: {e}")
            if self.log_file_handle:
                try:
                    self.log_file_handle.close()
                except:
                    pass
            self.log_file_handle = None

    def write(self, message):
        # 写入终端
        try:
            if self.terminal:
                self.terminal.write(message)
        except Exception as e:
            # 如果终端写入失败，不要抛出异常
            pass

        # 写入日志文件
        if self.log_file_handle is not None:
            try:
                self.log_file_handle.write(message)
                self.log_file_handle.flush()
            except Exception:
                # 如果日志文件写入失败，关闭文件句柄
                try:
                    self.log_file_handle.close()
                except:
                    pass
                self.log_file_handle = None

    def flush(self):
        try:
            if self.terminal:
                self.terminal.flush()
        except Exception:
            pass

        if self.log_file_handle is not None:
            try:
                self.log_file_handle.flush()
            except Exception:
                try:
                    self.log_file_handle.close()
                except:
                    pass
                self.log_file_handle = None

    def __del__(self):
        if self.log_file_handle is not None:
            try:
                self.log_file_handle.close()
            except Exception:
                pass

def setup_logging():
    # 首先设置一个基本的日志配置，以防出错
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    # 初始化一个简单的日志重定向器，不使用文件
    # 这样即使后面的代码出错，也不会影响程序运行
    safe_redirector = LogRedirector()
    sys.stdout = safe_redirector
    sys.stderr = safe_redirector

    try:
        # 获取应用程序路径
        if getattr(sys, 'frozen', False):
            # 如果是打包后的环境，使用sys._MEIPASS或当前目录
            try:
                app_path = sys._MEIPASS
            except AttributeError:
                app_path = os.path.dirname(sys.executable)
        else:
            # 如果是开发环境，使用当前文件目录
            app_path = os.path.dirname(os.path.abspath(__file__))

        # 尝试创建日志目录
        log_dir = None
        for potential_dir in [os.path.join(app_path, 'logs'), app_path, os.getcwd(), os.path.expanduser('~')]:
            try:
                if not os.path.exists(potential_dir):
                    os.makedirs(potential_dir)
                # 测试目录是否可写
                test_file = os.path.join(potential_dir, 'test_write.tmp')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                log_dir = potential_dir
                break
            except Exception:
                continue

        # 如果所有目录都不可写，仅使用控制台输出
        if log_dir is None:
            print("无法找到可写的目录来存放日志文件，仅使用控制台输出")
            return

        # 设置日志文件路径
        log_file = os.path.join(log_dir, 'app.log')
        print(f"尝试使用日志文件: {log_file}")

        # 创建日志重定向器
        log_redirector = LogRedirector(log_file)
        sys.stdout = log_redirector
        sys.stderr = log_redirector

        # 配置日志记录器
        try:
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            console_handler = logging.StreamHandler()

            # 设置格式化器
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)

            # 获取根日志记录器并清除现有处理程序
            root_logger = logging.getLogger()
            root_logger.handlers = []

            # 添加新的处理程序
            root_logger.addHandler(file_handler)
            root_logger.addHandler(console_handler)
            root_logger.setLevel(logging.INFO)

            logging.info(f"[日志系统] 日志文件初始化成功: {log_file}")
        except Exception as e:
            print(f"配置日志记录器时出错: {e}")
    except Exception as e:
        print(f"设置日志系统时出错: {e}")

def main():
    """应用程序的主入口点。"""
    # 设置日志
    setup_logging()

    # 创建应用程序
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
