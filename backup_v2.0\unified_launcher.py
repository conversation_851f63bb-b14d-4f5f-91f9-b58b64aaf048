"""
统一性能监控工具启动器

这个模块提供了一个简单的启动器界面，用于启动不同的性能监控工具。
"""

import os
import sys
import subprocess
import logging
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QMessageBox,
                            QGroupBox, QFrame, QAction)
from settings_dialog import SettingsDialog
from PyQt5.QtCore import Qt, QSettings
from PyQt5.QtGui import QFont, QIcon, QColor, QPalette

from logger_utils import log, log_exception, init_logger
from version import __version__, __app_name__


class UnifiedLauncher(QMainWindow):
    """统一性能监控工具启动器的主窗口。"""

    def __init__(self):
        """初始化主窗口。"""
        super().__init__()

        self.setWindowTitle("性能监控工具集")
        self.setMinimumSize(600, 400)
        self.setMaximumSize(800, 500)

        # 设置应用程序样式
        self.set_style()

        # 初始化UI
        self.init_ui()

    def set_style(self):
        """设置应用程序样式。"""
        # 从设置中读取样式
        settings = QSettings("PerformanceAnalyzer", "Settings")
        style_name = settings.value("ui_style", "modern_blue", type=str)
        font_size = settings.value("font_size", 9, type=int)

        # 设置字体大小
        font = QFont()
        font.setPointSize(font_size)
        QApplication.setFont(font)

        # 设置样式
        QApplication.setStyle("Fusion")

        # 根据选择的样式设置调色板
        if style_name == "modern_blue":
            # 现代蓝模式
            palette = QPalette()
            palette.setColor(QPalette.Window, QColor(240, 245, 250))
            palette.setColor(QPalette.WindowText, QColor(35, 35, 35))
            palette.setColor(QPalette.Base, QColor(255, 255, 255))
            palette.setColor(QPalette.AlternateBase, QColor(245, 249, 252))
            palette.setColor(QPalette.ToolTipBase, QColor(255, 255, 255))
            palette.setColor(QPalette.ToolTipText, QColor(35, 35, 35))
            palette.setColor(QPalette.Text, QColor(35, 35, 35))
            palette.setColor(QPalette.Button, QColor(240, 245, 250))
            palette.setColor(QPalette.ButtonText, QColor(35, 35, 35))
            palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
            palette.setColor(QPalette.Link, QColor(42, 130, 218))
            palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
            palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
            QApplication.setPalette(palette)

            # 设置样式表
            self.setStyleSheet("""
                QToolTip { color: #333333; background-color: #f0f5fa; border: 1px solid #2a82da; }
                QGroupBox { background-color: #ffffff; border: 1px solid #c0d0e0; border-radius: 5px; margin-top: 1ex; }
                QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; }
                QPushButton { background-color: #2a82da; color: white; border: none; padding: 10px 20px; border-radius: 3px; font-size: 14px; }
                QPushButton:hover { background-color: #3a92ea; }
                QPushButton:pressed { background-color: #1a72ca; }
            """)

        elif style_name == "light_classic":
            # 浅色经典模式
            palette = QPalette()
            QApplication.setPalette(palette)  # 使用默认调色板

            # 设置样式表
            self.setStyleSheet("""
                QGroupBox { background-color: #f8f8f8; border: 1px solid #c0c0c0; border-radius: 5px; margin-top: 1ex; }
                QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; }
                QPushButton { padding: 8px 16px; border-radius: 3px; font-size: 13px; }
            """)

        else:  # system_default
            # 系统默认模式
            QApplication.setPalette(QApplication.style().standardPalette())
            self.setStyleSheet("""
                QGroupBox { margin-top: 1ex; }
                QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; }
                QPushButton { padding: 8px 16px; font-size: 13px; }
            """)

    def init_ui(self):
        """初始化用户界面。"""
        # 创建菜单栏
        self.create_menu_bar()

        # 创建中心组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # 添加标题
        title_label = QLabel("性能监控工具集")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)

        # 添加说明
        description_label = QLabel("请选择要启动的工具：您可以根据需要选择不同的工具进行性能分析")
        description_label.setAlignment(Qt.AlignCenter)
        description_font = QFont()
        description_font.setPointSize(10)
        description_label.setFont(description_font)
        main_layout.addWidget(description_label)

        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(separator)

        # 创建工具按钮组
        tools_group = QGroupBox("可用工具")
        tools_layout = QVBoxLayout(tools_group)
        tools_layout.setSpacing(15)

        # 性能监控数据分析器按钮
        self.performance_analyzer_button = QPushButton("性能监控数据分析器")
        self.performance_analyzer_button.setMinimumHeight(50)
        self.performance_analyzer_button.setToolTip("启动性能监控数据分析器，用于分析Excel文件中的CPU、内存和磁盘使用率数据")
        self.performance_analyzer_button.clicked.connect(self.launch_performance_analyzer)
        tools_layout.addWidget(self.performance_analyzer_button)

        # 原始Nmon文件分析工具按钮
        self.raw_nmon_analyzer_button = QPushButton("原始Nmon文件分析工具")
        self.raw_nmon_analyzer_button.setMinimumHeight(50)
        self.raw_nmon_analyzer_button.setToolTip("启动原始Nmon文件分析工具，用于详细分析nmon文件并生成图表和趋势分析")
        self.raw_nmon_analyzer_button.clicked.connect(self.launch_raw_nmon_analyzer)
        tools_layout.addWidget(self.raw_nmon_analyzer_button)

        # Nmon文件分析器按钮
        self.nmon_analyzer_button = QPushButton("Nmon文件分析器")
        self.nmon_analyzer_button.setMinimumHeight(50)
        self.nmon_analyzer_button.setToolTip("启动Nmon文件分析器，用于分析nmon文件并生成Excel报告")
        self.nmon_analyzer_button.clicked.connect(self.launch_nmon_analyzer)
        tools_layout.addWidget(self.nmon_analyzer_button)

        # 添加工具组到主布局
        main_layout.addWidget(tools_group)

        # 添加版本信息
        version_label = QLabel(f"版本: {__version__}")
        version_label.setAlignment(Qt.AlignRight)
        main_layout.addWidget(version_label)

    def launch_performance_analyzer(self):
        """启动性能监控数据分析器"""
        try:
            log("启动性能监控数据分析器")
            # 在启动前同步界面设置
            self.sync_ui_settings()

            # 使用子进程启动性能监控数据分析器
            # 创建一个新的Python进程来运行性能监控数据分析器
            # 这样可以避免在同一个进程中运行多个PyQt应用的问题
            subprocess.Popen([sys.executable, "run_performance_analyzer.py"])

            self.show_launch_message("性能监控数据分析器")
        except Exception as e:
            self.show_error_message("启动性能监控数据分析器失败", str(e))

    def launch_raw_nmon_analyzer(self):
        """启动原始Nmon文件分析工具"""
        try:
            log("启动原始Nmon文件分析工具")
            # 在启动前同步界面设置
            self.sync_ui_settings()
            subprocess.Popen([sys.executable, "run_raw_nmon_analyzer.py"])
            self.show_launch_message("原始Nmon文件分析工具")
        except Exception as e:
            self.show_error_message("启动原始Nmon文件分析工具失败", str(e))

    def launch_nmon_analyzer(self):
        """启动Nmon文件分析器"""
        try:
            log("启动Nmon文件分析器")
            # 在启动前同步界面设置
            self.sync_ui_settings()
            subprocess.Popen([sys.executable, "run_nmon_analyzer.py"])
            self.show_launch_message("Nmon文件分析器")
        except Exception as e:
            self.show_error_message("启动Nmon文件分析器失败", str(e))

    def show_launch_message(self, tool_name):
        """显示启动成功消息"""
        QMessageBox.information(self, "启动成功", f"{tool_name}已成功启动。")

    def show_error_message(self, title, error_message):
        """显示错误消息"""
        QMessageBox.critical(self, title, f"错误: {error_message}")
        log(f"{title}: {error_message}", "ERROR")

    def create_menu_bar(self):
        """创建菜单栏"""
        menu_bar = self.menuBar()

        # 文件菜单
        file_menu = menu_bar.addMenu("文件")

        # 退出动作
        exit_action = QAction("退出", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 设置菜单
        settings_menu = menu_bar.addMenu("设置")

        # 界面设置动作
        ui_settings_action = QAction("界面设置", self)
        ui_settings_action.triggered.connect(self.show_settings_dialog)
        settings_menu.addAction(ui_settings_action)

        # 帮助菜单
        help_menu = menu_bar.addMenu("帮助")

        # 关于动作
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)

    def sync_ui_settings(self):
        """同步界面和日志设置到所有工具"""
        try:
            # 获取当前设置
            settings = QSettings("PerformanceAnalyzer", "Settings")
            style_name = settings.value("ui_style", "modern_blue", type=str)
            font_size = settings.value("font_size", 9, type=int)
            log_level = settings.value("log_level", logging.INFO, type=int)

            # 记录同步的设置
            log_level_name = logging.getLevelName(log_level)
            log(f"同步设置: 样式={style_name}, 字体大小={font_size}, 日志级别={log_level_name}")

            # 确保设置已同步到磁盘
            settings.sync()

            # 更新当前启动器的日志级别
            from logger_utils import init_logger
            init_logger(log_level=log_level)
        except Exception as e:
            log(f"同步设置时出错: {str(e)}", "ERROR")

    def show_settings_dialog(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self)
        if dialog.exec_():
            # 应用新的样式和日志设置
            self.set_style()

            # 同步设置到所有工具
            self.sync_ui_settings()

            log("已应用新的设置")

    def show_about_dialog(self):
        """显示关于对话框"""
        about_text = f"""
        <h2>性能监控工具集</h2>
        <p>版本: {__version__}</p>
        <p>这是一个集成了多种性能监控工具的统一界面，包括：</p>
        <ul>
            <li><b>性能监控数据分析器</b> - 用于分析Excel文件中的CPU、内存和磁盘使用率数据</li>
            <li><b>原始Nmon文件分析工具</b> - 用于详细分析nmon文件并生成图表和趋势分析</li>
            <li><b>Nmon文件分析器</b> - 用于分析nmon文件并生成Excel报告</li>
        </ul>
        <p>所有工具共享相同的界面风格和设置，提供一致的用户体验。</p>
        <p>&copy; 2023 性能监控工具团队</p>
        """
        QMessageBox.about(self, "关于性能监控工具集", about_text)

    def closeEvent(self, event):
        """处理窗口关闭事件"""
        log("用户关闭启动器", "INFO")
        event.accept()


def main():
    """主函数。"""
    # 初始化日志
    init_logger(None)

    app = QApplication(sys.argv)
    window = UnifiedLauncher()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
