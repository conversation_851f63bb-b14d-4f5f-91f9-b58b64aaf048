@echo off
echo Building executable for Performance Monitoring Tools v3.5.0...
echo.

REM Clean previous build files
echo Cleaning previous build files...
rmdir /s /q build
rmdir /s /q dist
del /q *.spec
echo.

REM Run PyInstaller with the build script
echo Running build script...
python build_unified_exe.py
echo.

if %ERRORLEVEL% == 0 (
    echo Build completed successfully!
    echo Executable is located in the dist\性能监控工具集_v3.5.0 directory.
) else (
    echo Build failed with error code %ERRORLEVEL%.
)

echo.
pause
