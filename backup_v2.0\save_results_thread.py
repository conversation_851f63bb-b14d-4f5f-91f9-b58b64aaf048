from PyQt5.QtCore import QThread, pyqtSignal
from excel_processor import ExcelProcessor
from chart_generator import ChartGenerator
from chart_optimizer import ChartOptimizer
from batch_processor import BatchProcessor
from logger_utils import log, log_exception
import traceback
import sys
import os

class SaveResultsThread(QThread):
    """用于保存结果的线程，不会阻塞GUI。"""

    progress_signal = pyqtSignal(str)  # 进度信息
    error_signal = pyqtSignal(str)    # 错误信息
    success_signal = pyqtSignal(str)  # 成功信息
    finished_signal = pyqtSignal()    # 完成信号
    percent_signal = pyqtSignal(int)  # 百分比进度

    def __init__(self, results, output_path, save_mode='auto', batch_size=10):
        """
        初始化保存结果线程。

        参数:
            results (list): 要保存的结果列表
            output_path (str): 输出文件路径
            save_mode (str): 保存模式，可选值：'auto', 'summary_only', 'full', 'batch'
            batch_size (int): 批处理模式下的批次大小
        """
        super().__init__()
        self.results = results
        self.output_path = output_path
        self.save_mode = save_mode
        self.batch_size = batch_size
        self.processor = ExcelProcessor()
        self.chart_generator = ChartGenerator()

        # 初始化优化器和批处理器
        self.chart_optimizer = ChartOptimizer(self.chart_generator)
        self.batch_processor = BatchProcessor(self.chart_optimizer)

        # 设置进度回调
        self.chart_optimizer.set_progress_callback(self.update_progress)
        self.batch_processor.set_progress_callback(self.update_progress)

        # 添加取消标志
        self.is_cancelled = False

    def set_cancelled(self, cancelled=True):
        """设置取消标志"""
        self.is_cancelled = cancelled
        # 同时设置批处理器的取消标志
        if hasattr(self.batch_processor, 'set_cancelled'):
            self.batch_processor.set_cancelled(cancelled)
        # 同时设置图表优化器的取消标志
        if hasattr(self.chart_optimizer, 'set_cancelled'):
            self.chart_optimizer.set_cancelled(cancelled)
        log(f"保存线程取消标志已设置为: {cancelled}", "INFO")

    def update_progress(self, message, percent=None):
        """更新进度信息"""
        try:
            # 记录日志，但不使用返回值
            log(message)

            # 确保 message 是字符串
            if not isinstance(message, str):
                message = str(message)

            # 发送信号
            self.progress_signal.emit(message)
            if percent is not None:
                self.percent_signal.emit(percent)
        except Exception as e:
            # 记录错误但不抛出，避免中断线程
            error_msg, _ = log_exception(e, "更新进度时")
            log(f"Error updating progress: {error_msg}", "ERROR")

    def run(self):
        """运行保存结果线程。"""
        result = None
        logs = []

        # 定义检查取消标志的函数
        def check_cancelled():
            if self.is_cancelled:
                self.progress_signal.emit("保存操作已取消，摘要数据可能已保存")
                return True
            return False

        try:
            # 记录线程信息
            thread_id = int(QThread.currentThreadId())
            log_msg = f"保存线程已启动 (ID: {thread_id}, 模式: {self.save_mode})"
            log(log_msg, "INFO")
            self.progress_signal.emit(log_msg)

            # 发送进度信息
            self.progress_signal.emit("开始保存结果...")
            self.percent_signal.emit(0)

            # 设置处理器结果
            self.processor.results = self.results

            # 检查是否取消
            if check_cancelled():
                return

            # 保存摘要
            try:
                self.progress_signal.emit("保存摘要数据...")
                self.percent_signal.emit(5)
                self.processor.save_summary(self.output_path)
                self.progress_signal.emit("摘要数据保存成功")
                self.percent_signal.emit(10)
            except Exception as e:
                error_msg, error_tb = log_exception(e, "保存摘要数据时")
                self.error_signal.emit(error_msg)
                self.progress_signal.emit(error_tb)
                return

            # 检查是否取消
            if self.is_cancelled:
                self.progress_signal.emit("保存操作已取消，但摘要数据已保存")
                return

            # 根据保存模式选择处理方式
            try:
                if self.save_mode == 'summary_only':
                    # 只保存摘要，不生成图表
                    self.progress_signal.emit("按照用户选择，只保存摘要数据，不生成图表")
                    self.percent_signal.emit(100)
                    result = self.output_path
                    logs = ["只保存了摘要数据，不生成图表"]

                elif self.save_mode == 'full':
                    # 完整模式，生成所有图表
                    self.progress_signal.emit("生成并添加所有图表...")
                    result, logs = self.chart_optimizer.add_charts_to_excel_optimized(
                        self.results, self.output_path)

                    # 检查是否取消
                    if check_cancelled():
                        return

                elif self.save_mode == 'batch':
                    # 批处理模式，分批生成图表
                    self.progress_signal.emit(f"使用批处理模式生成图表 (批次大小: {self.batch_size})...")
                    result, logs = self.batch_processor.save_results_in_batches(
                        self.results, self.output_path, batch_size=self.batch_size)

                    # 检查是否取消
                    if check_cancelled():
                        return

                else:  # 'auto' 模式
                    # 根据文件数量自动选择处理方式
                    if len(self.results) > 20:  # 文件数量较多
                        self.progress_signal.emit(f"检测到大量文件 ({len(self.results)}个)，使用批处理模式 (批次大小: {self.batch_size})...")
                        result, logs = self.batch_processor.save_results_in_batches(
                            self.results, self.output_path, batch_size=self.batch_size)
                    else:  # 文件数量较少
                        self.progress_signal.emit("生成并添加图表...")
                        result, logs = self.chart_optimizer.add_charts_to_excel_optimized(
                            self.results, self.output_path)

                    # 检查是否取消
                    if check_cancelled():
                        return
            except Exception as e:
                error_msg, error_tb = log_exception(e, "生成图表时")
                self.error_signal.emit(error_msg)
                self.progress_signal.emit(error_tb)
                # 即使图表生成失败，也返回摘要数据路径
                result = self.output_path
                logs = ["图表生成失败，但摘要数据已保存"]

            # 不发送详细的处理日志，只发送一条摘要信息
            if logs and len(logs) > 0:
                self.progress_signal.emit(f"图表生成完成，共处理 {len(logs)} 条日志信息")
            else:
                self.progress_signal.emit("图表生成完成")

            # 模拟最终处理过程，确保进度条与实际处理同步
            self.progress_signal.emit("正在整理最终结果...")
            self.percent_signal.emit(95)

            # 等待一下确保界面更新
            import time
            time.sleep(0.3)

            self.progress_signal.emit("正在完成所有处理...")
            self.percent_signal.emit(98)
            time.sleep(0.3)

            self.progress_signal.emit("处理完成!")
            self.percent_signal.emit(100)

            # 确保在线程结束前发送成功信号
            if result:
                # 发送成功信息
                success_message = f"结果已成功保存到 {self.output_path}"
                log(success_message, "INFO")

                # 进度信号已经在前面设置为100%，这里只需等待一下确保界面已更新
                import time
                log("等待0.5秒确保进度条已更新到100%", "INFO")
                time.sleep(0.5)

                # 强制处理事件，确保界面已更新
                from PyQt5.QtCore import QCoreApplication
                QCoreApplication.processEvents()

                # 然后发送成功信号
                log("发送成功信号", "INFO")
                self.success_signal.emit(success_message)

                # 不再等待额外时间，让GUI线程立即处理信号
                log("成功信号已发送，继续执行", "INFO")
            else:
                # 发送警告信息
                warning_message = f"图表生成部分失败，但摘要数据已保存到 {self.output_path}"
                log(warning_message, "WARNING")
                self.error_signal.emit(warning_message)

        except Exception as e:
            # 发送错误信息
            error_msg, error_tb = log_exception(e, "保存结果过程中")
            self.error_signal.emit(error_msg)
            self.progress_signal.emit(error_tb)

            # 记录系统信息
            self.progress_signal.emit(f"Python版本: {sys.version}")
            self.progress_signal.emit(f"当前工作目录: {os.getcwd()}")
            self.progress_signal.emit(f"输出文件路径: {self.output_path}")

        finally:
            # 清理资源
            try:
                # 关闭所有matplotlib图表
                import matplotlib.pyplot as plt
                plt.close('all')

                # 强制垃圾回收
                import gc
                gc.collect()

                log("资源清理完成", "INFO")
            except Exception as cleanup_error:
                log(f"清理资源时出错: {cleanup_error}", "ERROR")
                # 记录详细的错误堆栈
                log(traceback.format_exc(), "ERROR")

            # 记录线程结束
            log(f"保存线程已结束 (ID: {thread_id})", "INFO")

            # 直接发送完成信号，不使用延时器
            try:
                log("准备发送完成信号", "INFO")
                self.finished_signal.emit()
                log("完成信号已发送", "INFO")
            except Exception as e:
                log(f"发送完成信号时出错: {e}", "ERROR")
                log(traceback.format_exc(), "ERROR")

    def quit(self):
        """设置取消标志"""
        self.is_cancelled = True
        super().quit()
