"""
组件管理器模块，负责组件的注册、加载和管理。
"""

import os
import sys
import importlib
import logging
from PyQt5.QtCore import QObject, pyqtSignal

from component_base import ComponentBase
from logger_utils import log


class ComponentManager(QObject):
    """
    组件管理器，负责组件的注册、加载和管理。
    """
    
    # 信号定义
    component_registered = pyqtSignal(str)  # 组件注册信号，参数为组件ID
    component_unregistered = pyqtSignal(str)  # 组件注销信号，参数为组件ID
    component_activated = pyqtSignal(str)  # 组件激活信号，参数为组件ID
    component_deactivated = pyqtSignal(str)  # 组件停用信号，参数为组件ID
    
    def __init__(self):
        """初始化组件管理器。"""
        super().__init__()
        self._components = {}  # 组件字典，键为组件ID，值为组件实例
        self._active_component_id = None  # 当前活动组件ID
        
    def register_component(self, component):
        """
        注册组件。
        
        参数:
            component: 组件实例，必须是ComponentBase的子类
            
        返回:
            bool: 是否注册成功
        """
        if not isinstance(component, ComponentBase):
            log(f"组件注册失败：{component} 不是ComponentBase的子类", "ERROR")
            return False
            
        if not component.component_id:
            log(f"组件注册失败：组件ID不能为空", "ERROR")
            return False
            
        if component.component_id in self._components:
            log(f"组件注册失败：组件ID {component.component_id} 已存在", "ERROR")
            return False
            
        self._components[component.component_id] = component
        log(f"组件注册成功：{component.component_name} (ID: {component.component_id})", "INFO")
        self.component_registered.emit(component.component_id)
        return True
        
    def unregister_component(self, component_id):
        """
        注销组件。
        
        参数:
            component_id: 组件ID
            
        返回:
            bool: 是否注销成功
        """
        if component_id not in self._components:
            log(f"组件注销失败：组件ID {component_id} 不存在", "ERROR")
            return False
            
        if self._active_component_id == component_id:
            self.deactivate_component(component_id)
            
        component = self._components.pop(component_id)
        log(f"组件注销成功：{component.component_name} (ID: {component_id})", "INFO")
        self.component_unregistered.emit(component_id)
        return True
        
    def get_component(self, component_id):
        """
        获取组件实例。
        
        参数:
            component_id: 组件ID
            
        返回:
            ComponentBase: 组件实例，如果不存在则返回None
        """
        return self._components.get(component_id)
        
    def get_all_components(self):
        """
        获取所有组件。
        
        返回:
            dict: 组件字典，键为组件ID，值为组件实例
        """
        return self._components.copy()
        
    def get_component_list(self):
        """
        获取组件列表。
        
        返回:
            list: 组件列表，每个元素是一个组件实例
        """
        return list(self._components.values())
        
    def activate_component(self, component_id):
        """
        激活组件。
        
        参数:
            component_id: 组件ID
            
        返回:
            bool: 是否激活成功
        """
        if component_id not in self._components:
            log(f"组件激活失败：组件ID {component_id} 不存在", "ERROR")
            return False
            
        # 如果有活动组件，先停用它
        if self._active_component_id and self._active_component_id != component_id:
            self.deactivate_component(self._active_component_id)
            
        component = self._components[component_id]
        component.activate()
        self._active_component_id = component_id
        log(f"组件激活成功：{component.component_name} (ID: {component_id})", "INFO")
        self.component_activated.emit(component_id)
        return True
        
    def deactivate_component(self, component_id):
        """
        停用组件。
        
        参数:
            component_id: 组件ID
            
        返回:
            bool: 是否停用成功
        """
        if component_id not in self._components:
            log(f"组件停用失败：组件ID {component_id} 不存在", "ERROR")
            return False
            
        if self._active_component_id != component_id:
            log(f"组件停用失败：组件ID {component_id} 不是当前活动组件", "WARNING")
            return False
            
        component = self._components[component_id]
        component.deactivate()
        self._active_component_id = None
        log(f"组件停用成功：{component.component_name} (ID: {component_id})", "INFO")
        self.component_deactivated.emit(component_id)
        return True
        
    def get_active_component(self):
        """
        获取当前活动组件。
        
        返回:
            ComponentBase: 当前活动组件实例，如果没有则返回None
        """
        if not self._active_component_id:
            return None
        return self._components.get(self._active_component_id)
        
    def get_active_component_id(self):
        """
        获取当前活动组件ID。
        
        返回:
            str: 当前活动组件ID，如果没有则返回None
        """
        return self._active_component_id
        
    def load_components_from_directory(self, directory):
        """
        从指定目录加载组件。
        
        参数:
            directory: 组件目录
            
        返回:
            int: 成功加载的组件数量
        """
        if not os.path.isdir(directory):
            log(f"组件加载失败：目录 {directory} 不存在", "ERROR")
            return 0
            
        # 将组件目录添加到Python路径
        if directory not in sys.path:
            sys.path.append(directory)
            
        # 遍历目录中的所有Python文件
        loaded_count = 0
        for filename in os.listdir(directory):
            if filename.endswith('.py') and not filename.startswith('__'):
                module_name = filename[:-3]  # 去掉.py后缀
                try:
                    # 导入模块
                    module = importlib.import_module(module_name)
                    
                    # 查找模块中的组件类
                    for attr_name in dir(module):
                        attr = getattr(module, attr_name)
                        if isinstance(attr, type) and issubclass(attr, ComponentBase) and attr != ComponentBase:
                            # 创建组件实例
                            component = attr()
                            
                            # 注册组件
                            if self.register_component(component):
                                loaded_count += 1
                                
                except Exception as e:
                    log(f"加载组件模块 {module_name} 失败：{str(e)}", "ERROR")
                    import traceback
                    log(traceback.format_exc(), "ERROR")
                    
        log(f"从目录 {directory} 成功加载了 {loaded_count} 个组件", "INFO")
        return loaded_count
        
    def broadcast_event(self, event_type, event_data=None):
        """
        广播事件给所有组件。
        
        参数:
            event_type: 事件类型
            event_data: 事件数据
            
        返回:
            int: 处理了事件的组件数量
        """
        handled_count = 0
        for component in self._components.values():
            if component.handle_event(event_type, event_data):
                handled_count += 1
                
        return handled_count
        
    def save_all_component_states(self):
        """
        保存所有组件的状态。
        
        返回:
            dict: 组件状态字典，键为组件ID，值为组件状态
        """
        states = {}
        for component_id, component in self._components.items():
            try:
                states[component_id] = component.save_state()
            except Exception as e:
                log(f"保存组件 {component.component_name} (ID: {component_id}) 状态失败：{str(e)}", "ERROR")
                
        return states
        
    def restore_all_component_states(self, states):
        """
        恢复所有组件的状态。
        
        参数:
            states: 组件状态字典，键为组件ID，值为组件状态
        """
        for component_id, state in states.items():
            if component_id in self._components:
                try:
                    self._components[component_id].restore_state(state)
                except Exception as e:
                    log(f"恢复组件 {self._components[component_id].component_name} (ID: {component_id}) 状态失败：{str(e)}", "ERROR")
