import os
import pandas as pd
from openpyxl import load_workbook
from logger_utils import log, log_exception, get_logs

class BatchProcessor:
    """用于批处理大量文件的类"""

    def __init__(self, chart_optimizer):
        """
        初始化批处理器

        参数:
            chart_optimizer: ChartOptimizer实例，用于优化图表生成
        """
        self.chart_optimizer = chart_optimizer
        self.progress_callback = None
        self.is_cancelled = False  # 添加取消标志

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback
        self.chart_optimizer.set_progress_callback(callback)

    def set_cancelled(self, cancelled=True):
        """设置取消标志"""
        self.is_cancelled = cancelled
        # 同时设置图表优化器的取消标志
        if hasattr(self.chart_optimizer, 'set_cancelled'):
            self.chart_optimizer.set_cancelled(cancelled)

    def check_cancelled(self):
        """检查是否取消"""
        if self.is_cancelled:
            self.update_progress("操作已取消")
            return True
        return False

    def update_progress(self, message, percent=None):
        """更新进度信息"""
        # 记录日志，但不使用返回值
        log(message)

        # 确保 message 是字符串
        if not isinstance(message, str):
            message = str(message)

        # 调用回调函数
        if self.progress_callback:
            self.progress_callback(message, percent)

    def save_summary_only(self, results, output_path, include_stats=True):
        """
        只保存摘要数据，不生成图表，适用于大量文件的快速处理

        参数:
            results: 结果列表
            output_path: 输出文件路径
            include_stats: 是否包含统计信息（平均值和最大值）

        返回:
            output_path: 保存的文件路径
        """
        if not results:
            raise ValueError("No results to save. Process files first.")

        self.update_progress("创建摘要数据...")

        # 创建摘要DataFrame
        summary_df = pd.DataFrame([
            {
                'File': r['File'],
                'IP Address': r['IP Address'],
                'CPU Usage (%)': r['CPU Usage (%)'],
                'Memory Usage (%)': r['Memory Usage (%)'],
                '磁盘繁忙度 (%)': r.get('Disk Busy (%)', r.get('Max DiskIO Usage (%)', 0)),
                'Max Disk Column': r['Max Disk Column']
            } for r in results
        ])

        # 根据参数决定是否添加统计信息
        if include_stats:
            # 添加统计信息
            stats_df = pd.DataFrame([{
                'File': '平均值',
                'IP Address': '',
                'CPU Usage (%)': summary_df['CPU Usage (%)'].mean(),
                'Memory Usage (%)': summary_df['Memory Usage (%)'].mean(),
                '磁盘繁忙度 (%)': summary_df['磁盘繁忙度 (%)'].mean(),
                'Max Disk Column': ''
            }, {
                'File': '最大值',
                'IP Address': '',
                'CPU Usage (%)': summary_df['CPU Usage (%)'].max(),
                'Memory Usage (%)': summary_df['Memory Usage (%)'].max(),
                '磁盘繁忙度 (%)': summary_df['磁盘繁忙度 (%)'].max(),
                'Max Disk Column': ''
            }])
            # 合并摘要和统计信息
            final_df = pd.concat([summary_df, stats_df], ignore_index=True)
        else:
            # 不添加统计信息，直接使用原始摘要
            final_df = summary_df

        self.update_progress(f"保存摘要数据到 {output_path}...")

        # 保存到Excel
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            final_df.to_excel(writer, sheet_name='Summary', index=False)

            # 获取工作表并设置列宽
            worksheet = writer.sheets['Summary']
            for i, col in enumerate(final_df.columns):
                max_len = max(
                    len(str(col)),
                    final_df[col].astype(str).map(len).max() if len(final_df) > 0 else 0
                )
                worksheet.column_dimensions[chr(65 + i)].width = max_len + 4

        self.update_progress("摘要数据保存完成")
        return output_path

    def add_charts_to_excel_with_retry(self, results, excel_path, max_retries=3, base_progress=0, progress_range=100):
        """
        带有重试机制的图表添加方法

        参数:
            results: 结果列表
            excel_path: Excel文件路径
            max_retries: 最大重试次数
            base_progress: 进度基数，表示当前批次在总进度中的起始百分比
            progress_range: 进度范围，表示当前批次在总进度中占据的百分比范围

        返回:
            (result, logs): 成功返回Excel路径和日志，失败返回(None, logs)
        """
        total_files = len(results)

        for attempt in range(max_retries):
            # 检查是否取消
            if self.check_cancelled():
                return None, ["操作已取消"]

            try:
                # 计算当前尝试在进度范围内的百分比
                attempt_progress = base_progress + (attempt / max_retries) * (progress_range / 2)
                self.update_progress(f"尝试添加图表 (第 {attempt+1}/{max_retries} 次), 处理 {total_files} 个文件...", int(attempt_progress))

                # 添加图表处理进度回调
                def progress_callback(message, percent=None):
                    if percent is not None:
                        # 将内部进度映射到总进度范围的后半部分
                        adjusted_percent = base_progress + (progress_range / 2) + (percent / 100) * (progress_range / 2)
                        self.update_progress(message, int(adjusted_percent))
                    else:
                        self.update_progress(message)

                # 设置图表优化器的进度回调
                original_callback = self.chart_optimizer.progress_callback
                self.chart_optimizer.set_progress_callback(progress_callback)

                # 执行图表生成
                result, logs = self.chart_optimizer.add_charts_to_excel_optimized(results, excel_path)

                # 恢复原始回调
                self.chart_optimizer.set_progress_callback(original_callback)

                if result:
                    # 成功时更新进度到范围的最大值
                    self.update_progress(f"图表生成成功！", int(base_progress + progress_range))
                    return result, logs
                else:
                    self.update_progress(f"第 {attempt+1} 次尝试失败，准备重试...", int(base_progress + (attempt+1) / max_retries * progress_range / 2))
            except Exception as e:
                self.update_progress(f"第 {attempt+1} 次尝试出错: {e}")
                import traceback
                self.update_progress(f"错误详情: {traceback.format_exc()}")

                # 如果是最后一次尝试，尝试只保存摘要
                if attempt == max_retries - 1:
                    self.update_progress("所有重试都失败，尝试只保存摘要数据...", int(base_progress + progress_range * 0.9))
                    try:
                        self.save_summary_only(results, excel_path)
                        self.update_progress("摘要数据保存成功", int(base_progress + progress_range))
                        return excel_path, get_logs() + ["只保存了摘要数据，图表生成失败"]
                    except Exception as e2:
                        self.update_progress(f"保存摘要也失败: {e2}")

        return None, get_logs()

    def save_results_in_batches(self, results, output_path, batch_size=10):
        """
        分批处理大量文件，避免内存溢出

        参数:
            results: 结果列表
            output_path: 输出文件路径
            batch_size: 每批处理的文件数量

        返回:
            (output_path, logs): 保存的文件路径和日志
        """
        if len(results) <= batch_size:
            # 文件数量少，直接处理
            self.update_progress(f"文件数量较少 ({len(results)}个)，直接处理...")
            return self.add_charts_to_excel_with_retry(results, output_path)

        # 文件数量多，分批处理
        self.update_progress(f"文件数量较多 ({len(results)}个)，分批处理...")

        # 先保存摘要，在主摘要中包含统计信息
        self.save_summary_only(results, output_path, include_stats=True)
        self.update_progress("主摘要数据已保存")

        # 分批处理图表
        all_logs = []
        success_count = 0

        # 计算批次数
        num_batches = (len(results) + batch_size - 1) // batch_size

        for i in range(0, len(results), batch_size):
            # 检查是否取消
            if self.check_cancelled():
                return output_path, ["操作已取消，但摘要数据已保存"]

            batch = results[i:i+batch_size]
            batch_num = i // batch_size + 1

            # 计算总体进度百分比 - 保留最后10%的进度给最终处理
            # 每个批次占总进度的90%
            overall_percent = int(20 + (batch_num - 1) / num_batches * 70)
            self.update_progress(f"处理批次 {batch_num}/{num_batches} ({len(batch)}个文件)...", overall_percent)

            # 为每个批次创建单独的Excel文件
            batch_path = output_path.replace('.xlsx', f'_batch{batch_num}.xlsx')

            # 先保存该批次的摘要，不包含统计信息
            self.save_summary_only(batch, batch_path, include_stats=False)

            # 添加图表 - 传递进度范围参数
            # 每个批次在总进度中占据的范围是均匀分配的
            batch_progress_base = 20 + (batch_num - 1) / num_batches * 70
            batch_progress_range = 70 / num_batches

            result, logs = self.add_charts_to_excel_with_retry(
                batch,
                batch_path,
                base_progress=batch_progress_base,
                progress_range=batch_progress_range
            )
            all_logs.extend(logs)

            if result:
                success_count += 1
                self.update_progress(f"批次 {batch_num} 处理成功")
            else:
                self.update_progress(f"批次 {batch_num} 处理失败")

        # 更新进度为90%，保留最后10%给最终处理
        self.update_progress(f"分批处理完成: 共 {num_batches} 个批次, 成功 {success_count} 个", 90)

        # 模拟最终处理过程，确保进度条与实际处理同步
        import time
        self.update_progress("正在整理最终结果...", 92)
        time.sleep(0.5)  # 模拟处理时间

        self.update_progress("正在完成所有文件处理...", 95)
        time.sleep(0.5)  # 模拟处理时间

        self.update_progress("正在清理临时文件...", 98)
        time.sleep(0.5)  # 模拟处理时间

        self.update_progress("处理完成!", 100)
        return output_path, all_logs
