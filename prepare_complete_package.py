"""
准备完整的打包文件

这个脚本会将所有必要的文件复制到一个目录中，准备打包。
"""

import os
import shutil
import glob

def create_directory(dir_path):
    """创建目录，如果不存在"""
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)
        print(f"创建目录: {dir_path}")

def copy_file(src, dst):
    """复制文件，确保目标目录存在"""
    dst_dir = os.path.dirname(dst)
    if not os.path.exists(dst_dir):
        os.makedirs(dst_dir)

    if os.path.exists(src):
        shutil.copy2(src, dst)
        print(f"复制文件: {src} -> {dst}")
    else:
        print(f"警告: 文件不存在 {src}")

def main():
    """主函数"""
    # 创建打包目录
    package_dir = "complete_package"
    create_directory(package_dir)

    # 复制主要Python文件
    python_files = glob.glob("*.py")
    for file in python_files:
        # 排除一些不需要的文件
        if file not in ["prepare_complete_package.py", "backup_source_v1.5.py", "backup_source_v2.0.py"]:
            copy_file(file, os.path.join(package_dir, file))

    # 复制图标目录
    if os.path.exists("icons"):
        for file in glob.glob("icons/*"):
            copy_file(file, os.path.join(package_dir, file))

    # 复制字体目录
    if os.path.exists("fonts"):
        for file in glob.glob("fonts/*"):
            copy_file(file, os.path.join(package_dir, file))

    # 复制其他必要文件
    other_files = ["README.md", "requirements.txt", "version_info.txt"]
    for file in other_files:
        if os.path.exists(file):
            copy_file(file, os.path.join(package_dir, file))

    # 创建完整版启动器
    create_complete_launcher(package_dir)

    # 创建规格文件
    create_spec_file(package_dir)

    print("\n准备完成！所有文件已复制到 complete_package 目录")
    print("现在您可以运行: cd complete_package && python -m PyInstaller complete_launcher.spec")

def create_complete_launcher(package_dir):
    """创建完整版启动器"""
    launcher_code = """'''
完整版性能监控工具启动器

这个模块提供了一个完整的启动器界面，可以直接启动内置的工具。
'''

import os
import sys
import logging
import subprocess
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QMessageBox,
                            QGroupBox, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 设置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

class CompleteLauncher(QMainWindow):
    """Complete launcher for performance monitoring tools."""

    def __init__(self):
        """Initialize the main window."""
        try:
            super().__init__()
            logging.info("初始化CompleteLauncher类")

            self.setWindowTitle("性能监控工具集")
            self.setMinimumSize(600, 400)

            # 初始化UI
            self.init_ui()
            logging.info("初始化UI完成")
        except Exception as e:
            logging.error(f"CompleteLauncher初始化时出错: {e}")
            # 尝试显示错误消息
            try:
                QMessageBox.critical(None, "初始化错误", f"启动器初始化时出错: {e}")
            except:
                pass
            raise

    def init_ui(self):
        """Initialize the user interface."""
        try:
            # 创建中心组件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            logging.info("创建中心组件完成")

            # 主布局
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)

            # 添加标题
            title_label = QLabel("性能监控工具集")
            title_label.setAlignment(Qt.AlignCenter)
            title_font = QFont()
            title_font.setPointSize(16)
            title_font.setBold(True)
            title_label.setFont(title_font)
            main_layout.addWidget(title_label)

            # 添加说明
            description_label = QLabel("请选择要启动的工具：")
            description_label.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(description_label)

            # 添加分隔线
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setFrameShadow(QFrame.Sunken)
            main_layout.addWidget(separator)

            # 创建工具按钮组
            tools_group = QGroupBox("可用工具")
            tools_layout = QVBoxLayout(tools_group)
            tools_layout.setSpacing(15)

            # 性能监控数据分析器按钮
            self.performance_analyzer_button = QPushButton("性能监控数据分析器")
            self.performance_analyzer_button.setMinimumHeight(50)
            self.performance_analyzer_button.clicked.connect(self.launch_performance_analyzer)
            tools_layout.addWidget(self.performance_analyzer_button)

            # 原始Nmon文件分析工具按钮
            self.raw_nmon_analyzer_button = QPushButton("原始Nmon文件分析工具")
            self.raw_nmon_analyzer_button.setMinimumHeight(50)
            self.raw_nmon_analyzer_button.clicked.connect(self.launch_raw_nmon_analyzer)
            tools_layout.addWidget(self.raw_nmon_analyzer_button)

            # Nmon文件分析器按钮
            self.nmon_analyzer_button = QPushButton("Nmon文件分析器")
            self.nmon_analyzer_button.setMinimumHeight(50)
            self.nmon_analyzer_button.clicked.connect(self.launch_nmon_analyzer)
            tools_layout.addWidget(self.nmon_analyzer_button)

            # 添加工具组到主布局
            main_layout.addWidget(tools_group)

            # 添加版本信息
            version_label = QLabel("版本: 2.0.0")
            version_label.setAlignment(Qt.AlignRight)
            main_layout.addWidget(version_label)

        except Exception as e:
            logging.error(f"init_ui方法出错: {e}")
            # 尝试显示错误消息
            try:
                QMessageBox.critical(self, "UI初始化错误", f"UI初始化时出错: {e}")
            except:
                pass
            raise

    def launch_performance_analyzer(self):
        """Launch performance analyzer"""
        try:
            logging.info("启动性能监控数据分析器")

            # 直接启动内置的性能监控数据分析器
            try:
                # 尝试导入并运行
                from gui import MainWindow
                logging.info("成功导入MainWindow类")

                # 创建新的QApplication实例可能会导致问题，所以显示消息
                QMessageBox.information(self, "启动提示",
                    "性能监控数据分析器将在新窗口中启动。\n"
                    "请关闭当前窗口以避免潜在的冲突。")

                # 启动外部进程
                if getattr(sys, 'frozen', False):
                    # 如果是打包后的可执行文件
                    exe_dir = os.path.dirname(sys.executable)
                    script_path = os.path.join(exe_dir, "run_performance_analyzer.exe")
                    if os.path.exists(script_path):
                        subprocess.Popen([script_path])
                    else:
                        subprocess.Popen([sys.executable, "run_performance_analyzer.py"])
                else:
                    # 如果是源代码运行
                    subprocess.Popen([sys.executable, "run_performance_analyzer.py"])

                logging.info("性能监控数据分析器启动成功")

            except ImportError as e:
                logging.error(f"导入MainWindow类失败: {e}")
                QMessageBox.warning(self, "启动警告",
                    "无法导入性能监控数据分析器组件。\n"
                    "请确保所有必要的文件都在同一目录下。")

        except Exception as e:
            logging.error(f"启动性能监控数据分析器失败: {e}")
            QMessageBox.critical(self, "启动失败", f"启动性能监控数据分析器失败: {e}")

    def launch_raw_nmon_analyzer(self):
        """Launch raw nmon analyzer"""
        try:
            logging.info("启动原始Nmon文件分析工具")

            # 直接启动内置的原始Nmon文件分析工具
            try:
                # 尝试导入并运行
                from raw_nmon_analyzer_gui import RawNmonAnalyzerApp
                logging.info("成功导入RawNmonAnalyzerApp类")

                # 创建新的QApplication实例可能会导致问题，所以显示消息
                QMessageBox.information(self, "启动提示",
                    "原始Nmon文件分析工具将在新窗口中启动。\n"
                    "请关闭当前窗口以避免潜在的冲突。")

                # 启动外部进程
                if getattr(sys, 'frozen', False):
                    # 如果是打包后的可执行文件
                    exe_dir = os.path.dirname(sys.executable)
                    script_path = os.path.join(exe_dir, "run_raw_nmon_analyzer.exe")
                    if os.path.exists(script_path):
                        subprocess.Popen([script_path])
                    else:
                        subprocess.Popen([sys.executable, "run_raw_nmon_analyzer.py"])
                else:
                    # 如果是源代码运行
                    subprocess.Popen([sys.executable, "run_raw_nmon_analyzer.py"])

                logging.info("原始Nmon文件分析工具启动成功")

            except ImportError as e:
                logging.error(f"导入RawNmonAnalyzerApp类失败: {e}")
                QMessageBox.warning(self, "启动警告",
                    "无法导入原始Nmon文件分析工具组件。\n"
                    "请确保所有必要的文件都在同一目录下。")

        except Exception as e:
            logging.error(f"启动原始Nmon文件分析工具失败: {e}")
            QMessageBox.critical(self, "启动失败", f"启动原始Nmon文件分析工具失败: {e}")

    def launch_nmon_analyzer(self):
        """Launch nmon analyzer"""
        try:
            logging.info("启动Nmon文件分析器")

            # 直接启动内置的Nmon文件分析器
            try:
                # 尝试导入并运行
                from nmon_analyzer_gui import NmonAnalyzerApp
                logging.info("成功导入NmonAnalyzerApp类")

                # 创建新的QApplication实例可能会导致问题，所以显示消息
                QMessageBox.information(self, "启动提示",
                    "Nmon文件分析器将在新窗口中启动。\n"
                    "请关闭当前窗口以避免潜在的冲突。")

                # 启动外部进程
                if getattr(sys, 'frozen', False):
                    # 如果是打包后的可执行文件
                    exe_dir = os.path.dirname(sys.executable)
                    script_path = os.path.join(exe_dir, "run_nmon_analyzer.exe")
                    if os.path.exists(script_path):
                        subprocess.Popen([script_path])
                    else:
                        subprocess.Popen([sys.executable, "run_nmon_analyzer.py"])
                else:
                    # 如果是源代码运行
                    subprocess.Popen([sys.executable, "run_nmon_analyzer.py"])

                logging.info("Nmon文件分析器启动成功")

            except ImportError as e:
                logging.error(f"导入NmonAnalyzerApp类失败: {e}")
                QMessageBox.warning(self, "启动警告",
                    "无法导入Nmon文件分析器组件。\n"
                    "请确保所有必要的文件都在同一目录下。")

        except Exception as e:
            logging.error(f"启动Nmon文件分析器失败: {e}")
            QMessageBox.critical(self, "启动失败", f"启动Nmon文件分析器失败: {e}")


def main():
    """Main function."""
    try:
        logging.info("启动完整版启动器")
        app = QApplication(sys.argv)
        window = CompleteLauncher()
        window.show()
        logging.info("窗口显示成功")
        sys.exit(app.exec_())
    except Exception as e:
        logging.error(f"主函数出错: {e}")
        # 尝试显示错误消息
        try:
            QMessageBox.critical(None, "程序错误", f"程序运行时出错: {e}")
        except:
            pass


if __name__ == "__main__":
    main()
"""

    with open(os.path.join(package_dir, "complete_launcher.py"), "w", encoding="utf-8") as f:
        f.write(launcher_code)
    print(f"创建完整版启动器: {os.path.join(package_dir, 'complete_launcher.py')}")

    # 创建启动脚本
    run_launcher_code = """'''
完整版性能监控工具启动器 - 启动脚本

这个脚本用于启动完整版的性能监控工具启动器。
'''

import sys
from PyQt5.QtWidgets import QApplication
from complete_launcher import CompleteLauncher

def main():
    '''主函数。'''
    app = QApplication(sys.argv)
    window = CompleteLauncher()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
"""

    with open(os.path.join(package_dir, "run_complete_launcher.py"), "w", encoding="utf-8") as f:
        f.write(run_launcher_code)
    print(f"创建启动脚本: {os.path.join(package_dir, 'run_complete_launcher.py')}")

def create_spec_file(package_dir):
    """创建规格文件"""
    spec_code = """# -*- mode: python ; coding: utf-8 -*-

import os
block_cipher = None

a = Analysis(
    ['run_complete_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('icons', 'icons'),  # Include the icons directory
        ('fonts', 'fonts'),  # Include the fonts directory
        ('README.md', '.'),  # Include the readme file
    ],
    hiddenimports=[
        'pandas',
        'numpy',
        'matplotlib',
        'openpyxl',
        'PyQt5',
        'gui',
        'raw_nmon_analyzer_gui',
        'nmon_analyzer_gui',
        'excel_processor',
        'data_analyzer',
        'chart_generator',
        'save_results_thread',
        'save_options_dialog',
        'settings_dialog',
        'logger_utils',
        'exception_handler',
        'version',
        'ensure_fonts',
        'nmon_parser',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='性能监控工具集_V2.0_完整版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icons/app_icon.ico' if os.path.exists('icons/app_icon.ico') else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='性能监控工具集_V2.0_完整版',
)
"""

    with open(os.path.join(package_dir, "complete_launcher.spec"), "w", encoding="utf-8") as f:
        f.write(spec_code)
    print(f"创建规格文件: {os.path.join(package_dir, 'complete_launcher.spec')}")

if __name__ == "__main__":
    main()
