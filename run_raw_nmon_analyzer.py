"""
原始Nmon文件分析工具 v3.2 - 启动脚本

这个脚本用于启动原始Nmon文件分析工具。
"""

import sys
import os
import logging
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QSettings
from raw_nmon_analyzer_gui import RawNmonAnalyzerApp
from logger_utils import init_logger

# 版本号
VERSION = "3.2"

def main():
    """主函数。"""
    try:
        # 初始化日志
        settings = QSettings("PerformanceAnalyzer", "Settings")
        log_level = settings.value("log_level", logging.INFO, type=int)
        log_dir = settings.value("log_dir", "logs", type=str)

        # 确保日志目录存在
        if not os.path.exists(log_dir):
            try:
                os.makedirs(log_dir)
            except Exception as e:
                print(f"创建日志目录失败: {e}")
                log_dir = "logs"  # 回退到默认目录
                if not os.path.exists(log_dir):
                    os.makedirs(log_dir)

        # 初始化日志
        init_logger(log_level=log_level, log_dir=log_dir)

        # 创建应用
        app = QApplication(sys.argv)
        window = RawNmonAnalyzerApp()
        window.show()
        sys.exit(app.exec_())
    except Exception as e:
        # 捕获并显示任何异常
        error_msg = f"启动应用时出错: {str(e)}\n\n{traceback.format_exc()}"
        print(error_msg)

        # 如果已创建应用，显示错误对话框
        try:
            if 'app' in locals():
                QMessageBox.critical(None, "启动错误", f"启动应用时出错: {str(e)}")
        except:
            pass

        sys.exit(1)

if __name__ == "__main__":
    main()
