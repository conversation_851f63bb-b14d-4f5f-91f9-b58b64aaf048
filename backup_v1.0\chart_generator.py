import os
import matplotlib.pyplot as plt
import pandas as pd
from openpyxl import load_workbook
from openpyxl.drawing.image import Image
from openpyxl.styles import Font
import io
import numpy as np
import matplotlib.dates as mdates
from datetime import datetime
import matplotlib.font_manager as fm
import platform
import matplotlib as mpl
from matplotlib.ticker import FuncFormatter
from cycler import cycler

# 设置中文字体
# 根据不同操作系统选择适合的字体
# 创建一个全局日志列表，用于收集日志信息而不是直接打印到控制台
log_messages = []

def log(message):
    """添加日志消息到全局日志列表"""
    log_messages.append(message)

def get_logs():
    """获取并清空日志列表"""
    global log_messages
    logs = log_messages.copy()
    log_messages = []
    return logs

def set_chinese_font():
    system = platform.system()
    if system == 'Windows':
        # Windows系统常用字体
        font_names = ['Microsoft YaHei', 'SimHei', 'SimSun', 'Arial Unicode MS', 'Microsoft Sans Serif']
    elif system == 'Darwin':
        # macOS系统常用字体
        font_names = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS']
    else:
        # Linux系统常用字体
        font_names = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Droid Sans Fallback', 'Noto Sans CJK SC']

    # 检查字体是否可用
    all_fonts = [f.name for f in fm.fontManager.ttflist]
    log(f"系统字体总数: {len(all_fonts)}")

    available_fonts = [f for f in font_names if f.lower() in [f.name.lower() for f in fm.fontManager.ttflist]]

    # 记录可用字体
    if available_fonts:
        log(f"找到支持中文的字体: {', '.join(available_fonts)}")
    else:
        log("警告: 未找到支持中文的字体")

    if available_fonts:
        # 设置全局字体
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['font.sans-serif'] = [available_fonts[0]] + plt.rcParams['font.sans-serif']
        log(f"使用字体: {available_fonts[0]}")

        # 直接设置matplotlib的字体属性
        mpl.rcParams['font.family'] = 'sans-serif'
        mpl.rcParams['font.sans-serif'] = [available_fonts[0]] + mpl.rcParams['font.sans-serif']

        # 尝试使用字体管理器添加字体
        try:
            font_path = fm.findfont(fm.FontProperties(family=available_fonts[0]))
            log(f"字体路径: {font_path}")
        except Exception as e:
            log(f"警告: 查找字体路径时出错: {str(e)}")
    else:
        # 尝试使用系统默认字体
        log("警告: 未找到支持中文的字体，尝试使用系统默认字体")
        # 在Windows系统上，尝试直接设置SimSun字体
        if system == 'Windows':
            plt.rcParams['font.sans-serif'] = ['SimSun'] + plt.rcParams['font.sans-serif']
            mpl.rcParams['font.sans-serif'] = ['SimSun'] + mpl.rcParams['font.sans-serif']
            log("尝试使用SimSun字体")

    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False
    mpl.rcParams['axes.unicode_minus'] = False

# 设置现代化的图表样式
def set_modern_style():
    # 设置现代化的颜色周期
    # 使用更现代的色彩方案，增强可读性和美观性
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
              '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
    plt.rcParams['axes.prop_cycle'] = cycler(color=colors)

    # 设置图表背景和网格样式
    plt.rcParams['figure.facecolor'] = 'white'
    plt.rcParams['axes.facecolor'] = 'white'
    plt.rcParams['axes.grid'] = True
    plt.rcParams['grid.alpha'] = 0.3
    plt.rcParams['grid.linestyle'] = '--'

    # 设置字体大小
    plt.rcParams['font.size'] = 11
    plt.rcParams['axes.titlesize'] = 14
    plt.rcParams['axes.labelsize'] = 12

    # 设置线条宽度和样式
    plt.rcParams['lines.linewidth'] = 2.0
    # 不使用标记点

    # 设置轴线和刻度样式
    plt.rcParams['axes.spines.top'] = False
    plt.rcParams['axes.spines.right'] = False
    plt.rcParams['xtick.direction'] = 'out'
    plt.rcParams['ytick.direction'] = 'out'
    plt.rcParams['xtick.major.size'] = 5.0
    plt.rcParams['ytick.major.size'] = 5.0

    # 设置图例样式
    plt.rcParams['legend.frameon'] = True
    plt.rcParams['legend.framealpha'] = 0.8
    plt.rcParams['legend.edgecolor'] = 'lightgray'
    plt.rcParams['legend.fancybox'] = True
    plt.rcParams['legend.shadow'] = False

# 初始化时设置字体和样式
set_chinese_font()
set_modern_style()

# 保存图表时处理中文字体
def save_figure_with_chinese(fig, output, format='png'):
    """
    保存图表并确保中文显示正确

    参数:
        fig: matplotlib图表对象
        output: 输出路径或BytesIO对象
        format: 输出格式，默认为png
    """
    try:
        # 在保存前再次确认字体设置
        system = platform.system()
        if system == 'Windows':
            font_prop = fm.FontProperties(family='Microsoft YaHei')
        else:
            # 其他系统尝试使用默认字体
            font_prop = fm.FontProperties()

        # 更新图表中的所有文本元素的字体
        for ax in fig.get_axes():
            for text in ax.get_xticklabels() + ax.get_yticklabels():
                text.set_fontproperties(font_prop)

            # 更新标题和轴标签
            title = ax.get_title()
            xlabel = ax.get_xlabel()
            ylabel = ax.get_ylabel()

            ax.set_title(title, fontproperties=font_prop)
            ax.set_xlabel(xlabel, fontproperties=font_prop)
            ax.set_ylabel(ylabel, fontproperties=font_prop)

        # 保存图表
        fig.savefig(output, format=format, bbox_inches='tight')
        log(f"图表已保存为{format}格式")
    except Exception as e:
        log(f"警告: 保存图表时出错: {e}")
        # 如果出错，回退到原始的保存方式
        try:
            fig.savefig(output, format=format)
            log("使用默认方式保存图表成功")
        except Exception as e2:
            log(f"错误: 保存图表失败: {e2}")

def plot_with_timestamps(timestamps, data, label=None, valid_timestamp_count=None):
    """
    使用时间戳作为x轴绘制数据（如果可能），否则使用索引。

    参数:
        timestamps: 时间戳列表或数组
        data: 要绘制的数据点列表或数组
        label: 图表图例的标签
        valid_timestamp_count: 有效时间戳的行数

    返回:
        None
    """
    # 强制限制数据点数量，防止生成过多刻度
    log(f"原始数据点数: {len(data)}")
    # 检查时间戳和数据的长度
    if len(data) == 0:
        log(f"警告: {label or '未命名数据'} 没有数据可绘制")
        return

    # 强制采样以防止生成过多刻度
    max_points = 500  # 最大点数限制

    # 如果提供了有效时间戳行数，使用它来确定采样策略
    if valid_timestamp_count is not None and valid_timestamp_count > 0:
        log(f"使用有效时间戳行数 {valid_timestamp_count} 进行采样")
        # 根据有效时间戳行数确定采样间隔
        if valid_timestamp_count > max_points:
            sample_interval = max(1, valid_timestamp_count // max_points)
            log(f"有效时间戳行数过多 ({valid_timestamp_count}), 采样间隔为 {sample_interval}")

            # 使用更智能的采样方法，保留数据的特征
            if valid_timestamp_count > 10000:
                # 将数据分成多个块，每块取平均值
                resampled_data = []
                resampled_timestamps = []

                for i in range(0, len(data), sample_interval):
                    chunk = data[i:i+sample_interval]
                    if len(chunk) > 0:
                        resampled_data.append(sum(chunk) / len(chunk))  # 平均值
                        # 对应的时间戳取块的中间值
                        ts_chunk = timestamps[i:i+sample_interval]
                        if len(ts_chunk) > 0:
                            mid_idx = len(ts_chunk) // 2
                            resampled_timestamps.append(ts_chunk[mid_idx])

                data = resampled_data
                timestamps = resampled_timestamps
            else:
                # 对于中等大小的数据集，直接采样
                data = data[::sample_interval]
                timestamps = timestamps[::sample_interval]

            log(f"采样后的数据点数: {len(data)}")
    # 如果没有提供有效时间戳行数，使用数据长度进行采样
    elif len(data) > max_points:
        # 计算采样间隔
        sample_interval = max(1, len(data) // max_points)
        log(f"数据点过多 ({len(data)}), 采样间隔为 {sample_interval}")

        # 使用更智能的采样方法，保留数据的特征
        if len(data) > 10000:
            # 将数据分成多个块，每块取平均值
            resampled_data = []
            resampled_timestamps = []

            for i in range(0, len(data), sample_interval):
                chunk = data[i:i+sample_interval]
                if len(chunk) > 0:
                    resampled_data.append(sum(chunk) / len(chunk))  # 平均值
                    # 对应的时间戳取块的中间值
                    ts_chunk = timestamps[i:i+sample_interval]
                    if len(ts_chunk) > 0:
                        mid_idx = len(ts_chunk) // 2
                        resampled_timestamps.append(ts_chunk[mid_idx])

            data = resampled_data
            timestamps = resampled_timestamps
        else:
            # 对于中等大小的数据集，直接采样
            data = data[::sample_interval]
            timestamps = timestamps[::sample_interval]

        log(f"采样后的数据点数: {len(data)}")

    # 确保时间戳和数据长度一致
    if len(timestamps) > len(data):
        log(f"时间戳数量({len(timestamps)})大于数据点数({len(data)})，截断时间戳")
        timestamps = timestamps[:len(data)]
    elif len(timestamps) < len(data):
        log(f"时间戳数量({len(timestamps)})小于数据点数({len(data)})，截断数据")
        data = data[:len(timestamps)]

    # 确保数据长度一致后再次检查
    if len(timestamps) != len(data):
        log(f"警告: 即使截断后，时间戳数量({len(timestamps)})和数据点数({len(data)})仍然不匹配")
        # 取两者的最小值作为共同长度
        min_len = min(len(timestamps), len(data))
        timestamps = timestamps[:min_len]
        data = data[:min_len]

    # 如果时间戳为空，使用索引
    if len(timestamps) == 0:
        log(f"警告: {label or '未命名数据'} 没有时间戳数据，使用索引")
        plt.plot(range(len(data)), data, label=label, marker='', linestyle='-')
        return

    # 检查时间戳类型
    is_datetime = False
    try:
        if isinstance(timestamps, pd.Series) and pd.api.types.is_datetime64_any_dtype(timestamps):
            is_datetime = True
        elif isinstance(timestamps[0], (pd.Timestamp, np.datetime64)) or hasattr(timestamps[0], 'strftime'):
            is_datetime = True

        # 尝试转换为时间类型（如果还不是）
        if not is_datetime and isinstance(timestamps, pd.Series):
            try:
                timestamps = pd.to_datetime(timestamps, errors='coerce')
                # 检查转换后是否有有效值
                if not timestamps.isna().all():
                    is_datetime = True
                    log(f"成功将 {label or '未命名数据'} 的时间戳转换为日期时间类型")
            except Exception as e:
                log(f"警告: 时间戳转换失败: {e}")
    except Exception as e:
        log(f"警告: 检查时间戳类型时出错: {e}")
        is_datetime = False

    # 如果时间戳类型检查失败，强制使用索引
    if is_datetime:
        try:
            log(f"使用时间戳作为 {label or '未命名数据'} 的x轴")
            # 确保数据从最小时间戳开始
            plt.plot(timestamps, data, label=label, marker='', linestyle='-')

            # 设置x轴范围，确保数据线从横坐标时间范围的起点开始
            try:
                min_time = min(timestamps)
                plt.xlim(left=min_time)
                log(f"设置时间轴起点为 {min_time}")
            except Exception as e:
                log(f"警告: 设置时间轴范围时出错: {e}")
        except Exception as e:
            log(f"警告: 使用时间戳绘图失败: {e}")
            log("回退到使用索引绘图")
            plt.plot(range(len(data)), data, label=label, marker='', linestyle='-')
            # 对于索引绘图，设置x轴从0开始
            plt.xlim(left=0)
    else:
        # 如果时间戳不是日期时间对象或为空，使用索引
        log(f"使用数字索引作为 {label or '未命名数据'} 的x轴")
        plt.plot(range(len(data)), data, label=label, marker='', linestyle='-')
        # 对于索引绘图，设置x轴从0开始
        plt.xlim(left=0)

def format_time_axis(timestamps, valid_timestamp_count=None):
    """
    格式化时间轴仅显示小时:分钟:秒。

    参数:
        timestamps: 时间戳列表或数组
        valid_timestamp_count: 有效时间戳的行数

    返回:
        bool: 如果应用了格式化返回Ture，否则返回False
    """
    try:
        # 增加输出确定异常位置
        log("开始格式化时间轴...")

        # 检查时间戳类型
        is_datetime = False
        if len(timestamps) > 0:
            if isinstance(timestamps, pd.Series) and pd.api.types.is_datetime64_any_dtype(timestamps):
                is_datetime = True
                log("时间戳类型: pandas Series datetime64")
            elif isinstance(timestamps[0], (pd.Timestamp, np.datetime64)) or hasattr(timestamps[0], 'strftime'):
                is_datetime = True
                log(f"时间戳类型: {type(timestamps[0]).__name__}")
            else:
                log(f"时间戳类型: {type(timestamps[0]).__name__} (非日期时间类型)")

        if is_datetime:
            # 确保 x 轴格式化为时间（小时:分钟:秒）
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
            plt.gcf().autofmt_xdate()  # 自动格式化 x 轴日期标签
            log("已设置日期格式化器为 %H:%M:%S")

            # 限制刻度数量，防止超过最大限制
            # 如果提供了有效时间戳行数，使用它来确定刻度数量
            if valid_timestamp_count is not None and valid_timestamp_count > 0:
                data_length = valid_timestamp_count
                log(f"使用有效时间戳行数 {valid_timestamp_count} 进行刻度计算")
            else:
                data_length = len(timestamps)
                log(f"使用时间戳长度 {data_length} 进行刻度计算")

            # 检查时间范围
            if len(timestamps) > 1:
                try:
                    # 安全地访问时间戳索引
                    last_idx = len(timestamps) - 1
                    time_range = timestamps[last_idx] - timestamps[0]
                    log(f"时间范围: {time_range}")
                    # 如果时间范围过大，可能会导致生成过多刻度
                    if hasattr(time_range, 'days') and time_range.days > 7:
                        log(f"警告: 时间范围过大 ({time_range.days} 天), 可能导致刻度问题")
                except Exception as e:
                    log(f"警告: 计算时间范围时出错: {e}")

            # 强制限制刻度数量，使用MaxNLocator
            # 更加保守的刻度数量，无论数据量多大，都不超过10个刻度
            max_ticks = min(10, max(5, data_length // 1000 + 1))  # 非常保守的刻度数量
            log(f"设置最大刻度数量为 {max_ticks}")

            # 直接使用MaxNLocator作为主要策略，避免使用可能生成过多刻度的时间定位器
            plt.gca().xaxis.set_major_locator(plt.MaxNLocator(max_ticks))
            log(f"已设置MaxNLocator({max_ticks})作为主要定位器")

            # 禁用次要刻度，减少刻度数量
            plt.gca().xaxis.set_minor_locator(plt.NullLocator())
            log("已禁用次要刻度")

            return True
        else:
            # 如果不是时间戳，限制数字刻度的数量
            data_length = len(timestamps) if len(timestamps) > 0 else 100
            max_ticks = min(10, data_length // 50 + 1)  # 非常保守的刻度数量
            log(f"非时间戳数据，设置最大刻度数量为 {max_ticks}")
            plt.gca().xaxis.set_major_locator(plt.MaxNLocator(max_ticks))
            return False
    except Exception as e:
        log(f"错误: 格式化时间轴时出错: {e}")
        import traceback
        log(f"错误详情: {traceback.format_exc()}")
        # 如果出错，使用最安全的设置
        log("使用最安全的设置: MaxNLocator(5)")
        plt.gca().xaxis.set_major_locator(plt.MaxNLocator(5))  # 只显示5个刻度
        return False

class ChartGenerator:
    """用于从处理数据生成图表的类。"""

    def __init__(self):
        """初始化图表生成器。"""
        self.dpi = 120  # 提高DPI以获得更清晰的图表
        self.figure_size = (12, 7)  # 稍微增大图表尺寸以获得更好的视觉效果

    def create_cpu_chart(self, results, output_path=None):
        """
        为所有文件创建CPU使用率图表。

        参数:
            results (list): 结果字典列表
            output_path (str, 可选): 保存图表图像的路径

        返回:
            str 或 None: 如果提供了output_path，则返回保存的图像路径
        """
        # 创建带有现代化样式的图表
        fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)

        # 添加浅色背景渐变效果
        ax.set_facecolor('#f8f9fa')  # 设置浅色背景
        fig.patch.set_facecolor('white')  # 设置图表背景为白色

        # 获取IP地址作为标题前缀
        ip_prefix = ""
        if results and 'IP Address' in results[0]:
            ip = results[0]['IP Address']
            if ip and ip != '未知':
                ip_prefix = f"[{ip}] "

        # 使用不同的线型样式
        line_styles = ['-', '--', '-.', ':']

        for i, result in enumerate(results):
            file_name = result['File']
            cpu_data = result['Raw Data']['CPU']
            timestamps = result['Raw Data'].get('Timestamps', {}).get('CPU', [])

            # 获取时间戳行数信息
            timestamp_counts = result['Raw Data'].get('TimestampCounts', {})
            cpu_timestamp_count = timestamp_counts.get('CPU', 0)

            # 使用文件名作为图例标签
            label = file_name
            # 如果有IP地址且不是未知，添加到标签中
            if 'IP Address' in result and result['IP Address'] and result['IP Address'] != '未知':
                label = f"{result['IP Address']} - {file_name}"

            # 选择线型样式
            line_style = line_styles[i % len(line_styles)]

            # 传递时间戳行数信息和样式参数
            if len(timestamps) > 0 and len(cpu_data) > 0:
                # 确保时间戳和数据长度一致
                if len(timestamps) > len(cpu_data):
                    log(f"警告: 时间戳数量({len(timestamps)})大于CPU数据点数({len(cpu_data)})，截断时间戳")
                    timestamps = timestamps[:len(cpu_data)]
                elif len(timestamps) < len(cpu_data):
                    log(f"警告: 时间戳数量({len(timestamps)})小于CPU数据点数({len(cpu_data)})，截断CPU数据")
                    cpu_data = cpu_data[:len(timestamps)]

                # 确保数据长度一致后再次检查
                if len(timestamps) != len(cpu_data):
                    log(f"警告: 即使截断后，时间戳数量({len(timestamps)})和CPU数据点数({len(cpu_data)})仍然不匹配")
                    # 取两者的最小值作为共同长度
                    min_len = min(len(timestamps), len(cpu_data))
                    timestamps = timestamps[:min_len]
                    cpu_data = cpu_data[:min_len]

                if hasattr(timestamps[0], 'strftime'):
                    plt.plot(timestamps, cpu_data, label=label, linestyle=line_style,
                             linewidth=2.0, alpha=0.9)
                else:
                    plt.plot(range(len(cpu_data)), cpu_data, label=label, linestyle=line_style,
                             linewidth=2.0, alpha=0.9)

        # Format time axis if timestamps are datetime objects
        if results and 'Timestamps' in results[0]['Raw Data'] and 'CPU' in results[0]['Raw Data']['Timestamps']:
            # 传递时间戳行数信息
            timestamp_counts = results[0]['Raw Data'].get('TimestampCounts', {})
            cpu_timestamp_count = timestamp_counts.get('CPU', 0)
            format_time_axis(results[0]['Raw Data']['Timestamps']['CPU'], valid_timestamp_count=cpu_timestamp_count)

        # 设置标题和轴标签，使用更现代的字体样式
        plt.title(f'{ip_prefix}CPU使用率 (%)', fontweight='bold', pad=15)
        plt.xlabel('时间', fontweight='bold', labelpad=10)
        plt.ylabel('CPU使用率 (%)', fontweight='bold', labelpad=10)

        # 设置纵坐标范围并添加水平参考线
        plt.ylim(0, 100)  # 设置纵坐标范围为0-100%

        # 添加水平参考线，稍微改变颜色和样式以增强可读性
        plt.axhline(y=25, color='#17a2b8', linestyle='--', alpha=0.3, linewidth=1)
        plt.axhline(y=50, color='#ffc107', linestyle='--', alpha=0.5, linewidth=1)
        plt.axhline(y=75, color='#dc3545', linestyle='--', alpha=0.3, linewidth=1)

        # 设置x轴范围，确保数据线从横坐标时间范围的起点开始
        # 检查是否有时间戳数据
        if results and 'Timestamps' in results[0]['Raw Data'] and 'CPU' in results[0]['Raw Data']['Timestamps']:
            timestamps = results[0]['Raw Data']['Timestamps']['CPU']
            if len(timestamps) > 0 and hasattr(timestamps[0], 'strftime'):
                # 对于时间戳数据，设置起点为第一个时间戳
                try:
                    min_time = min(timestamps)
                    max_time = max(timestamps)
                    plt.xlim(left=min_time)
                    print(f"设置时间轴范围从 {min_time} 到 {max_time}")
                except Exception as e:
                    print(f"设置时间轴范围时出错: {e}")
                    # 如果出错，不设置范围
            else:
                # 如果不是时间戳数据，则设置x轴从0开始
                plt.xlim(left=0)
                print("非时间戳数据，设置x轴从0开始")
        else:
            # 如果没有时间戳数据，则设置x轴从0开始
            plt.xlim(left=0)
            print("没有时间戳数据，设置x轴从0开始")

        # 设置更美观的图例
        legend = plt.legend(loc='upper right', framealpha=0.9, fancybox=True, shadow=True,
                           fontsize=10, ncol=min(2, len(results)))
        legend.get_frame().set_edgecolor('lightgray')

        # 添加网格线以提高可读性
        plt.grid(True, linestyle='--', alpha=0.7, color='#cccccc')

        # 添加文本注释，显示数据时间范围
        if results and 'Timestamps' in results[0]['Raw Data'] and 'CPU' in results[0]['Raw Data']['Timestamps']:
            timestamps = results[0]['Raw Data']['Timestamps']['CPU']
            if len(timestamps) > 1 and hasattr(timestamps[0], 'strftime'):
                try:
                    start_time = timestamps[0].strftime('%Y-%m-%d %H:%M:%S')
                    # 安全地访问最后一个时间戳
                    last_idx = len(timestamps) - 1
                    end_time = timestamps[last_idx].strftime('%Y-%m-%d %H:%M:%S')
                    plt.figtext(0.5, 0.01, f'数据时间范围: {start_time} 至 {end_time}',
                               ha='center', fontsize=9, style='italic', color='#666666')
                except Exception as e:
                    print(f"格式化时间范围文本时出错: {e}")

        plt.tight_layout(pad=2.0)  # 增加填充以留出更多空间

        if output_path:
            save_figure_with_chinese(plt.gcf(), output_path)
            plt.close()
            return output_path
        else:
            img_data = io.BytesIO()
            save_figure_with_chinese(plt.gcf(), img_data)
            plt.close()
            img_data.seek(0)
            return img_data

    def create_memory_chart(self, results, output_path=None):
        """
        为所有文件创建内存使用率图表。

        参数:
            results (list): 结果字典列表
            output_path (str, 可选): 保存图表图像的路径

        返回:
            str 或 None: 如果提供了output_path，则返回保存的图像路径
        """
        # 创建带有现代化样式的图表
        fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)

        # 添加浅色背景渐变效果 - 使用不同的背景色区分CPU和内存图表
        ax.set_facecolor('#f0f7ff')  # 设置浅蓝色背景区分内存图表
        fig.patch.set_facecolor('white')  # 设置图表背景为白色

        # 获取IP地址作为标题前缀
        ip_prefix = ""
        if results and 'IP Address' in results[0]:
            ip = results[0]['IP Address']
            if ip and ip != '未知':
                ip_prefix = f"[{ip}] "

        # 使用不同的线型样式
        line_styles = ['-', '--', '-.', ':']

        for i, result in enumerate(results):
            file_name = result['File']
            mem_data = result['Raw Data']['Memory']
            timestamps = result['Raw Data'].get('Timestamps', {}).get('MEM', [])

            # 获取时间戳行数信息
            timestamp_counts = result['Raw Data'].get('TimestampCounts', {})
            mem_timestamp_count = timestamp_counts.get('MEM', 0)

            # 使用文件名作为图例标签
            label = file_name
            # 如果有IP地址且不是未知，添加到标签中
            if 'IP Address' in result and result['IP Address'] and result['IP Address'] != '未知':
                label = f"{result['IP Address']} - {file_name}"

            # 选择线型样式
            line_style = line_styles[i % len(line_styles)]

            # 传递时间戳行数信息和样式参数
            if len(timestamps) > 0 and len(mem_data) > 0:
                # 确保时间戳和数据长度一致
                if len(timestamps) > len(mem_data):
                    log(f"警告: 时间戳数量({len(timestamps)})大于内存数据点数({len(mem_data)})，截断时间戳")
                    timestamps = timestamps[:len(mem_data)]
                elif len(timestamps) < len(mem_data):
                    log(f"警告: 时间戳数量({len(timestamps)})小于内存数据点数({len(mem_data)})，截断内存数据")
                    mem_data = mem_data[:len(timestamps)]

                # 确保数据长度一致后再次检查
                if len(timestamps) != len(mem_data):
                    log(f"警告: 即使截断后，时间戳数量({len(timestamps)})和内存数据点数({len(mem_data)})仍然不匹配")
                    # 取两者的最小值作为共同长度
                    min_len = min(len(timestamps), len(mem_data))
                    timestamps = timestamps[:min_len]
                    mem_data = mem_data[:min_len]

                if hasattr(timestamps[0], 'strftime'):
                    plt.plot(timestamps, mem_data, label=label, linestyle=line_style,
                             linewidth=2.0, alpha=0.9)
                else:
                    plt.plot(range(len(mem_data)), mem_data, label=label, linestyle=line_style,
                             linewidth=2.0, alpha=0.9)

        # Format time axis if timestamps are datetime objects
        if results and 'Timestamps' in results[0]['Raw Data'] and 'MEM' in results[0]['Raw Data']['Timestamps']:
            # 传递时间戳行数信息
            timestamp_counts = results[0]['Raw Data'].get('TimestampCounts', {})
            mem_timestamp_count = timestamp_counts.get('MEM', 0)
            format_time_axis(results[0]['Raw Data']['Timestamps']['MEM'], valid_timestamp_count=mem_timestamp_count)

        # 设置标题和轴标签，使用更现代的字体样式
        plt.title(f'{ip_prefix}内存使用率 (%)', fontweight='bold', pad=15)
        plt.xlabel('时间', fontweight='bold', labelpad=10)
        plt.ylabel('内存使用率 (%)', fontweight='bold', labelpad=10)

        # 设置纵坐标范围并添加水平参考线
        plt.ylim(0, 100)  # 设置纵坐标范围为0-100%

        # 添加水平参考线，使用蓝色系与内存主题相匹配
        plt.axhline(y=25, color='#007bff', linestyle='--', alpha=0.3, linewidth=1)
        plt.axhline(y=50, color='#0056b3', linestyle='--', alpha=0.5, linewidth=1)
        plt.axhline(y=75, color='#003580', linestyle='--', alpha=0.3, linewidth=1)

        # 设置x轴范围，确保数据线从横坐标时间范围的起点开始
        # 检查是否有时间戳数据
        if results and 'Timestamps' in results[0]['Raw Data'] and 'MEM' in results[0]['Raw Data']['Timestamps']:
            timestamps = results[0]['Raw Data']['Timestamps']['MEM']
            if len(timestamps) > 0 and hasattr(timestamps[0], 'strftime'):
                # 对于时间戳数据，设置起点为第一个时间戳
                try:
                    min_time = min(timestamps)
                    max_time = max(timestamps)
                    plt.xlim(left=min_time)
                    print(f"设置内存图表时间轴范围从 {min_time} 到 {max_time}")
                except Exception as e:
                    print(f"设置内存图表时间轴范围时出错: {e}")
                    # 如果出错，不设置范围
            else:
                # 如果不是时间戳数据，则设置x轴从0开始
                plt.xlim(left=0)
                print("非时间戳数据，设置内存图表x轴从0开始")
        else:
            # 如果没有时间戳数据，则设置x轴从0开始
            plt.xlim(left=0)
            print("没有时间戳数据，设置内存图表x轴从0开始")

        # 设置更美观的图例
        legend = plt.legend(loc='upper right', framealpha=0.9, fancybox=True, shadow=True,
                           fontsize=10, ncol=min(2, len(results)))
        legend.get_frame().set_edgecolor('lightgray')

        # 添加网格线以提高可读性
        plt.grid(True, linestyle='--', alpha=0.7, color='#cccccc')

        # 添加文本注释，显示数据时间范围
        if results and 'Timestamps' in results[0]['Raw Data'] and 'MEM' in results[0]['Raw Data']['Timestamps']:
            timestamps = results[0]['Raw Data']['Timestamps']['MEM']
            if len(timestamps) > 1 and hasattr(timestamps[0], 'strftime'):
                try:
                    start_time = timestamps[0].strftime('%Y-%m-%d %H:%M:%S')
                    # 安全地访问最后一个时间戳
                    last_idx = len(timestamps) - 1
                    end_time = timestamps[last_idx].strftime('%Y-%m-%d %H:%M:%S')
                    plt.figtext(0.5, 0.01, f'数据时间范围: {start_time} 至 {end_time}',
                               ha='center', fontsize=9, style='italic', color='#666666')
                except Exception as e:
                    print(f"格式化内存图表时间范围文本时出错: {e}")

        plt.tight_layout(pad=2.0)  # 增加填充以留出更多空间

        if output_path:
            save_figure_with_chinese(plt.gcf(), output_path)
            plt.close()
            return output_path
        else:
            img_data = io.BytesIO()
            save_figure_with_chinese(plt.gcf(), img_data)
            plt.close()
            img_data.seek(0)
            return img_data

    def create_disk_chart(self, results, output_path=None):
        """
        创建磁盘繁忙度图表，与CPU和内存图表保持一致的样式。

        参数:
            results (list): 结果字典列表
            output_path (str, 可选): 保存图表图像的路径

        返回:
            str 或 None: 如果提供了output_path，则返回保存的图像路径
        """
        # 创建带有现代化样式的图表
        fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)

        # 添加浅色背景渐变效果 - 使用不同的背景色区分磁盘图表
        ax.set_facecolor('#f5f5dc')  # 设置浅粉色背景区分磁盘图表
        fig.patch.set_facecolor('white')  # 设置图表背景为白色

        # 获取IP地址作为标题前缀
        ip_prefix = ""
        if results and 'IP Address' in results[0]:
            ip = results[0]['IP Address']
            if ip and ip != '未知':
                ip_prefix = f"[{ip}] "

        # 使用时间序列绘制磁盘使用率图表，与CPU和内存图表保持一致
        for result in results:
            file_name = result['File']
            # 获取磁盘数据
            disk_data = []
            disk_col = result.get('Max Disk Column', '')

            if disk_col and disk_col in result['Raw Data']['Disk']:
                disk_data = result['Raw Data']['Disk'][disk_col]
            else:
                # 如果没有指定列或该列不存在，尝试使用第一个可用的磁盘列
                for col, data in result['Raw Data']['Disk'].items():
                    if col.lower() != 'totals' and data:
                        disk_data = data
                        disk_col = col
                        break

            # 获取时间戳
            timestamps = result['Raw Data'].get('Timestamps', {}).get('DISK', [])

            # 获取时间戳行数信息
            timestamp_counts = result['Raw Data'].get('TimestampCounts', {})
            disk_timestamp_count = timestamp_counts.get('DISK', 0)

            # 绘制磁盘使用率图表
            if disk_data:
                # 使用文件名和磁盘列作为图例标签
                label = file_name
                if disk_col:
                    # 如果有IP地址且不是未知，添加到标签中
                    if 'IP Address' in result and result['IP Address'] and result['IP Address'] != '未知':
                        label = f"{result['IP Address']} - {file_name} ({disk_col})"
                    else:
                        label = f"{file_name} ({disk_col})"

                # 传递时间戳行数信息
                plot_with_timestamps(timestamps, disk_data, label=label, valid_timestamp_count=disk_timestamp_count)

        # 格式化时间轴（如果有时间戳）
        if results and 'Timestamps' in results[0]['Raw Data'] and 'DISK' in results[0]['Raw Data']['Timestamps']:
            # 传递时间戳行数信息
            timestamp_counts = results[0]['Raw Data'].get('TimestampCounts', {})
            disk_timestamp_count = timestamp_counts.get('DISK', 0)
            format_time_axis(results[0]['Raw Data']['Timestamps']['DISK'], valid_timestamp_count=disk_timestamp_count)

        plt.title(f'{ip_prefix}磁盘繁忙度 (%)', fontweight='bold', pad=15)
        plt.xlabel('时间', fontweight='bold', labelpad=10)
        plt.ylabel('磁盘繁忙度 (%)', fontweight='bold', labelpad=10)
        plt.ylim(0, 100)  # 设置纵坐标范围为0-100%

        # 设置x轴范围，确保数据线从横坐标时间范围的起点开始
        # 检查是否有时间戳数据
        if results and 'Timestamps' in results[0]['Raw Data'] and 'DISK' in results[0]['Raw Data']['Timestamps']:
            timestamps = results[0]['Raw Data']['Timestamps']['DISK']
            if len(timestamps) > 0 and hasattr(timestamps[0], 'strftime'):
                # 对于时间戳数据，设置起点为第一个时间戳
                try:
                    min_time = min(timestamps)
                    max_time = max(timestamps)
                    plt.xlim(left=min_time)
                    print(f"设置磁盘图表时间轴范围从 {min_time} 到 {max_time}")
                except Exception as e:
                    print(f"设置磁盘图表时间轴范围时出错: {e}")
                    # 如果出错，不设置范围
            else:
                # 如果不是时间戳数据，则设置x轴从0开始
                plt.xlim(left=0)
                print("非时间戳数据，设置磁盘图表x轴从0开始")
        else:
            # 如果没有时间戳数据，则设置x轴从0开始
            plt.xlim(left=0)
            print("没有时间戳数据，设置磁盘图表x轴从0开始")

        # 添加水平参考线，使用绿色系与磁盘主题相匹配
        plt.axhline(y=25, color='#2e8b57', linestyle='--', alpha=0.3, linewidth=1)
        plt.axhline(y=50, color='#3cb371', linestyle='--', alpha=0.5, linewidth=1)
        plt.axhline(y=75, color='#66cdaa', linestyle='--', alpha=0.3, linewidth=1)

        # 设置更美观的图例
        legend = plt.legend(loc='upper right', framealpha=0.9, fancybox=True, shadow=True,
                           fontsize=10, ncol=min(2, len(results)))
        legend.get_frame().set_edgecolor('lightgray')

        # 添加网格线以提高可读性
        plt.grid(True, linestyle='--', alpha=0.7, color='#cccccc')

        # 添加文本注释，显示数据时间范围
        if results and 'Timestamps' in results[0]['Raw Data'] and 'DISK' in results[0]['Raw Data']['Timestamps']:
            timestamps = results[0]['Raw Data']['Timestamps']['DISK']
            if len(timestamps) > 1 and hasattr(timestamps[0], 'strftime'):
                try:
                    start_time = timestamps[0].strftime('%Y-%m-%d %H:%M:%S')
                    # 安全地访问最后一个时间戳
                    last_idx = len(timestamps) - 1
                    end_time = timestamps[last_idx].strftime('%Y-%m-%d %H:%M:%S')
                    plt.figtext(0.5, 0.01, f'数据时间范围: {start_time} 至 {end_time}',
                               ha='center', fontsize=9, style='italic', color='#666666')
                except Exception as e:
                    print(f"格式化磁盘图表时间范围文本时出错: {e}")

        plt.tight_layout(pad=2.0)  # 增加填充以留出更多空间

        if output_path:
            save_figure_with_chinese(plt.gcf(), output_path)
            plt.close()
            return output_path
        else:
            img_data = io.BytesIO()
            save_figure_with_chinese(plt.gcf(), img_data)
            plt.close()
            img_data.seek(0)
            return img_data

    def add_charts_to_excel(self, results, excel_path):
        """
        将图表添加到现有的Excel文件中。
        当处理多个文件时，每个文件创建一个独立的sheet。

        参数:
            results (list): 结果字典列表
            excel_path (str): Excel文件的路径

        返回:
            str: 更新后的Excel文件路径
        """
        try:
            # 加载Excel工作簿
            log(f"打开Excel文件: {excel_path}")
            wb = load_workbook(excel_path)

            # 删除旧的单独图表工作表（如果存在）
            for old_sheet_name in ['CPU_Chart', 'Memory_Chart', 'Disk_Chart', 'Charts']:
                if old_sheet_name in wb.sheetnames:
                    log(f"删除旧工作表: {old_sheet_name}")
                    del wb[old_sheet_name]

            # 处理每个文件的结果
            for i, result in enumerate(results):
                # 为每个文件创建一个独立的sheet
                file_name = result['File']
                # 将文件名转换为有效的sheet名（去除特殊字符）
                sheet_name = f"Chart_{i+1}_{file_name[:20]}"
                # 替换无效的sheet名字符
                sheet_name = ''.join(c if c.isalnum() or c in '_- ' else '_' for c in sheet_name)
                # 确保名称不超过31个字符（Excel限制）
                sheet_name = sheet_name[:31]

                log(f"为文件 {file_name} 创建工作表: {sheet_name}")

                # 创建新的工作表
                if sheet_name in wb.sheetnames:
                    # 如果已存在同名工作表，删除它
                    del wb[sheet_name]
                ws = wb.create_sheet(sheet_name)

                # 设置列宽和行高，以便图表有足够的空间
                for col in range(1, 20):  # A到T列
                    ws.column_dimensions[chr(64 + col)].width = 15  # 设置列宽为15个字符

                for row in range(1, 60):  # 1到60行
                    ws.row_dimensions[row].height = 20  # 设置行高

                # 设置文件信息标题
                ws['A1'] = f'文件: {file_name}'
                ws['A1'].font = Font(bold=True, size=14)
                ws['A2'] = f'IP地址: {result["IP Address"]}'
                ws['A2'].font = Font(bold=True, size=12)

                # 为当前文件生成图表
                log(f"生成 {file_name} 的CPU图表...")
                cpu_img = self.create_cpu_chart([result])

                log(f"生成 {file_name} 的内存图表...")
                mem_img = self.create_memory_chart([result])

                log(f"生成 {file_name} 的磁盘图表...")
                disk_img = self.create_disk_chart([result])

                # 设置图表标题
                ws['A4'] = 'CPU使用率图表'
                ws['A4'].font = Font(bold=True, size=12)
                ws['A30'] = '内存使用率图表'
                ws['A30'].font = Font(bold=True, size=12)
                ws['A56'] = '磁盘繁忙度图表'
                ws['A56'].font = Font(bold=True, size=12)

                # 调整图表大小和位置
                # 添加CPU图表
                log(f"添加 {file_name} 的CPU图表...")
                img = Image(cpu_img)
                # 调整图表大小，保持纵横比
                img.width = 800  # 像素
                img.height = 450  # 像素
                ws.add_image(img, 'A5')  # 从第5行开始

                # 添加内存图表
                log(f"添加 {file_name} 的内存图表...")
                img = Image(mem_img)
                img.width = 800  # 像素
                img.height = 450  # 像素
                ws.add_image(img, 'A31')  # 从第31行开始

                # 添加磁盘图表
                log(f"添加 {file_name} 的磁盘图表...")
                img = Image(disk_img)
                img.width = 800  # 像素
                img.height = 450  # 像素
                ws.add_image(img, 'A57')  # 从第57行开始

            # 保存工作簿
            log(f"保存Excel文件: {excel_path}")
            wb.save(excel_path)
            log("图表添加完成")

            # 返回日志信息和Excel文件路径
            logs = get_logs()
            return excel_path, logs

        except Exception as e:
            error_msg = f"添加图表时出错: {e}"
            log(f"错误: {error_msg}")
            import traceback
            log(f"错误详情: {traceback.format_exc()}")

            # 返回错误信息和日志
            logs = get_logs()
            return None, logs
