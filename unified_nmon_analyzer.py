"""
统一Nmon文件分析器

这个模块整合了普通Nmon文件分析器和原始Nmon文件分析器的功能，
消除了功能重复，提供了统一的界面。
"""

import sys
import os
import logging
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QFileDialog,
                            QProgressBar, QMessageBox, QTabWidget, QTextEdit,
                            QGroupBox, QCheckBox, QComboBox, QSplitter, QFrame)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSettings, QUrl
from PyQt5.QtGui import QFont, QIcon, QDesktopServices

from raw_nmon_analyzer import RawNmonAnalyzer
from nmon_parser import NmonParser
from logger_utils import log, log_exception, init_logger


class AnalysisThread(QThread):
    """分析线程类。"""
    
    progress_signal = pyqtSignal(int)
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    
    def __init__(self, nmon_file_path, output_dir=None, include_charts=True, include_trend_analysis=True):
        """
        初始化分析线程。
        
        参数:
            nmon_file_path: nmon文件路径
            output_dir: 输出目录，默认为nmon文件所在目录
            include_charts: 是否包含性能图表
            include_trend_analysis: 是否包含趋势分析
        """
        super().__init__()
        self.nmon_file_path = nmon_file_path
        self.output_dir = output_dir or os.path.dirname(nmon_file_path)
        self.include_charts = include_charts
        self.include_trend_analysis = include_trend_analysis
        
    def run(self):
        """运行分析线程。"""
        try:
            self.log_signal.emit(f"开始分析nmon文件: {os.path.basename(self.nmon_file_path)}")
            
            # 创建分析器
            analyzer = RawNmonAnalyzer(
                self.nmon_file_path, 
                self.output_dir,
                progress_callback=self.update_progress,
                log_callback=self.log_message
            )
            
            # 设置分析选项
            analyzer.include_charts = self.include_charts
            analyzer.include_trend_analysis = self.include_trend_analysis
            
            # 执行分析
            result = analyzer.analyze()
            
            self.progress_signal.emit(100)
            self.log_signal.emit(f"nmon文件分析完成，结果已保存到: {result['output_file']}")
            
            # 发送完成信号
            self.finished_signal.emit(result)
            
        except Exception as e:
            self.error_signal.emit(f"分析nmon文件时出错: {str(e)}")
            import traceback
            self.log_signal.emit(traceback.format_exc())
            
    def update_progress(self, percent):
        """更新进度。"""
        self.progress_signal.emit(percent)
        
    def log_message(self, message):
        """记录日志消息。"""
        self.log_signal.emit(message)


class UnifiedNmonAnalyzerApp(QMainWindow):
    """统一Nmon文件分析器应用类。"""
    
    def __init__(self):
        """初始化应用。"""
        super().__init__()
        
        self.nmon_file_path = ""
        self.output_dir = ""
        self.analysis_thread = None
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面。"""
        # 设置窗口标题和大小
        self.setWindowTitle("统一Nmon文件分析器")
        self.resize(800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建文件选择区域
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)
        
        # 创建nmon文件选择区域
        nmon_file_layout = QHBoxLayout()
        nmon_file_label = QLabel("nmon文件:")
        self.nmon_file_path_label = QLabel("未选择")
        self.nmon_file_path_label.setStyleSheet("color: gray;")
        nmon_file_button = QPushButton("浏览...")
        nmon_file_button.clicked.connect(self.select_nmon_file)
        nmon_file_layout.addWidget(nmon_file_label)
        nmon_file_layout.addWidget(self.nmon_file_path_label, 1)
        nmon_file_layout.addWidget(nmon_file_button)
        file_layout.addLayout(nmon_file_layout)
        
        # 创建输出目录选择区域
        output_dir_layout = QHBoxLayout()
        output_dir_label = QLabel("输出目录:")
        self.output_dir_label = QLabel("与nmon文件相同")
        self.output_dir_label.setStyleSheet("color: gray;")
        output_dir_button = QPushButton("浏览...")
        output_dir_button.clicked.connect(self.select_output_dir)
        output_dir_layout.addWidget(output_dir_label)
        output_dir_layout.addWidget(self.output_dir_label, 1)
        output_dir_layout.addWidget(output_dir_button)
        file_layout.addLayout(output_dir_layout)
        
        main_layout.addWidget(file_group)
        
        # 创建选项区域
        options_group = QGroupBox("分析选项")
        options_layout = QVBoxLayout(options_group)
        
        # 创建图表选项
        self.include_charts_checkbox = QCheckBox("包含性能图表")
        self.include_charts_checkbox.setChecked(True)
        options_layout.addWidget(self.include_charts_checkbox)
        
        # 创建趋势分析选项
        self.include_trend_analysis_checkbox = QCheckBox("包含趋势分析")
        self.include_trend_analysis_checkbox.setChecked(True)
        options_layout.addWidget(self.include_trend_analysis_checkbox)
        
        main_layout.addWidget(options_group)
        
        # 创建分析按钮
        analyze_layout = QHBoxLayout()
        self.analyze_button = QPushButton("分析nmon文件")
        self.analyze_button.setStyleSheet("font-weight: bold;")
        self.analyze_button.clicked.connect(self.analyze_nmon_file)
        analyze_layout.addStretch()
        analyze_layout.addWidget(self.analyze_button)
        analyze_layout.addStretch()
        main_layout.addLayout(analyze_layout)
        
        # 创建进度条
        progress_layout = QHBoxLayout()
        progress_label = QLabel("进度:")
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(progress_label)
        progress_layout.addWidget(self.progress_bar, 1)
        main_layout.addLayout(progress_layout)
        
        # 创建日志区域
        log_group = QGroupBox("日志")
        log_layout = QVBoxLayout(log_group)
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        main_layout.addWidget(log_group, 1)
        
        # 设置窗口居中
        self.center()
        
    def center(self):
        """将窗口居中显示。"""
        frame_geometry = self.frameGeometry()
        screen_center = QApplication.desktop().availableGeometry().center()
        frame_geometry.moveCenter(screen_center)
        self.move(frame_geometry.topLeft())
        
    def select_nmon_file(self):
        """选择nmon文件。"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "选择nmon文件", 
            "", 
            "nmon文件 (*.nmon);;所有文件 (*.*)"
        )
        
        if file_path:
            self.nmon_file_path = file_path
            self.nmon_file_path_label.setText(os.path.basename(file_path))
            self.nmon_file_path_label.setStyleSheet("color: black;")
            
            # 如果未设置输出目录，则默认为nmon文件所在目录
            if not self.output_dir:
                self.output_dir = os.path.dirname(file_path)
                self.output_dir_label.setText("与nmon文件相同")
                self.output_dir_label.setStyleSheet("color: gray;")
                
    def select_output_dir(self):
        """选择输出目录。"""
        dir_path = QFileDialog.getExistingDirectory(
            self, 
            "选择输出目录", 
            self.output_dir or ""
        )
        
        if dir_path:
            self.output_dir = dir_path
            self.output_dir_label.setText(dir_path)
            self.output_dir_label.setStyleSheet("color: black;")
        
    def analyze_nmon_file(self):
        """分析nmon文件。"""
        if not self.nmon_file_path:
            QMessageBox.warning(self, "警告", "请先选择nmon文件")
            return
            
        # 禁用分析按钮
        self.analyze_button.setEnabled(False)
        
        # 重置进度条
        self.progress_bar.setValue(0)
        
        # 获取分析选项
        include_charts = self.include_charts_checkbox.isChecked()
        include_trend_analysis = self.include_trend_analysis_checkbox.isChecked()
        
        # 创建并启动分析线程
        self.analysis_thread = AnalysisThread(
            self.nmon_file_path, 
            self.output_dir,
            include_charts=include_charts,
            include_trend_analysis=include_trend_analysis
        )
        self.analysis_thread.progress_signal.connect(self.update_progress)
        self.analysis_thread.log_signal.connect(self.log_message)
        self.analysis_thread.finished_signal.connect(self.analysis_finished)
        self.analysis_thread.error_signal.connect(self.analysis_error)
        self.analysis_thread.start()
        
    def update_progress(self, value):
        """更新进度条。"""
        self.progress_bar.setValue(value)
        
    def log_message(self, message):
        """添加日志消息。"""
        self.log_text.append(message)
        
    def analysis_finished(self, result):
        """分析完成处理。"""
        # 启用分析按钮
        self.analyze_button.setEnabled(True)
        
        # 显示完成消息
        QMessageBox.information(
            self, 
            "分析完成", 
            f"nmon文件分析完成，结果已保存到:\n{result['output_file']}"
        )
        
        # 询问是否打开结果文件
        reply = QMessageBox.question(
            self,
            "打开结果",
            "是否打开分析结果文件？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.Yes:
            # 打开结果文件
            QDesktopServices.openUrl(QUrl.fromLocalFile(result['output_file']))
        
    def analysis_error(self, error_message):
        """分析错误处理。"""
        # 启用分析按钮
        self.analyze_button.setEnabled(True)
        
        # 显示错误消息
        QMessageBox.critical(self, "分析错误", error_message)


def main():
    """主函数。"""
    # 初始化日志
    settings = QSettings("PerformanceAnalyzer", "Settings")
    log_level = settings.value("log_level", logging.INFO, type=int)
    init_logger(log_level=log_level)
    
    app = QApplication(sys.argv)
    window = UnifiedNmonAnalyzerApp()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
