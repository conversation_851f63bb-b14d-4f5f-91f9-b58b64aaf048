"""
Performance monitoring data analyzer component with advanced visualization.
"""

import os
import sys
import random
import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
import numpy as np

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QFileDialog, QMessageBox, QComboBox, QCheckBox,
                            QGroupBox, QTabWidget, QWidget, QSplitter,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QScrollArea, QFrame, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSlot, QSettings, QSize
from PyQt5.QtGui import QFont, QColor, QPalette

from component_base import ComponentBase
from logger_utils import log

class MatplotlibCanvas(FigureCanvas):
    """Matplotlib canvas for plotting."""

    def __init__(self, parent=None, width=5, height=4, dpi=100):
        """Initialize the canvas."""
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = self.fig.add_subplot(111)

        super(MatplotlibCanvas, self).__init__(self.fig)
        self.setParent(parent)

        # Set background color
        self.fig.patch.set_facecolor('#f8f9fa')
        self.axes.set_facecolor('#f8f9fa')

        # Set figure size policy
        FigureCanvas.setSizePolicy(self,
                                  QSizePolicy.Expanding,
                                  QSizePolicy.Expanding)
        FigureCanvas.updateGeometry(self)

class AdvancedAnalyzerComponent(ComponentBase):
    """
    Advanced performance monitoring data analyzer component.
    """

    def __init__(self, parent=None):
        """
        Initialize the advanced analyzer component.

        Args:
            parent: Parent widget
        """
        super().__init__(parent)

        # Set component properties
        self.component_id = "advanced_analyzer"
        self.component_name = "高级分析器"
        self.component_description = "高级性能监控数据分析器"
        self.component_version = "1.0.0"

        # Data storage
        self.performance_data = None

        # Initialize UI
        self.init_ui()

    def init_ui(self):
        """Initialize the UI."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)

        # Header
        header_layout = QVBoxLayout()
        title_label = QLabel("高级性能监控数据分析器")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont("Arial", 16, QFont.Bold)
        title_label.setFont(title_font)

        subtitle_label = QLabel("交互式数据可视化与分析工具")
        subtitle_label.setAlignment(Qt.AlignCenter)

        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)

        # Add horizontal line
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        header_layout.addWidget(line)

        main_layout.addLayout(header_layout)

        # Main content - split into sidebar and content area
        content_layout = QHBoxLayout()

        # Sidebar
        sidebar = QWidget()
        sidebar.setMaximumWidth(250)
        sidebar_layout = QVBoxLayout(sidebar)

        # Data source group
        data_source_group = QGroupBox("数据源")
        data_source_layout = QVBoxLayout()

        self.load_data_btn = QPushButton("加载数据")
        self.load_data_btn.clicked.connect(self.load_data)
        data_source_layout.addWidget(self.load_data_btn)

        self.data_info_label = QLabel("未加载数据")
        self.data_info_label.setWordWrap(True)
        self.data_info_label.setStyleSheet("background-color: #f0f0f0; padding: 5px; border-radius: 3px;")
        data_source_layout.addWidget(self.data_info_label)

        data_source_group.setLayout(data_source_layout)
        sidebar_layout.addWidget(data_source_group)

        # Analysis options group
        options_group = QGroupBox("分析选项")
        options_layout = QVBoxLayout()

        # CPU option
        self.show_cpu_option = QCheckBox("CPU 使用率")
        self.show_cpu_option.setChecked(True)
        self.show_cpu_option.stateChanged.connect(self.update_chart_visibility)
        options_layout.addWidget(self.show_cpu_option)

        # Memory option
        self.show_memory_option = QCheckBox("内存使用率")
        self.show_memory_option.setChecked(True)
        self.show_memory_option.stateChanged.connect(self.update_chart_visibility)
        options_layout.addWidget(self.show_memory_option)

        # Disk option
        self.show_disk_option = QCheckBox("磁盘使用率")
        self.show_disk_option.setChecked(True)
        self.show_disk_option.stateChanged.connect(self.update_chart_visibility)
        options_layout.addWidget(self.show_disk_option)

        # Show averages
        self.show_averages_option = QCheckBox("显示平均值")
        self.show_averages_option.stateChanged.connect(self.update_chart_options)
        options_layout.addWidget(self.show_averages_option)

        # Show thresholds
        self.show_thresholds_option = QCheckBox("显示阈值线")
        self.show_thresholds_option.stateChanged.connect(self.update_chart_options)
        options_layout.addWidget(self.show_thresholds_option)

        # Time range
        time_range_layout = QHBoxLayout()
        time_range_layout.addWidget(QLabel("时间范围:"))
        self.time_range_combo = QComboBox()
        self.time_range_combo.addItems(["全部", "最近一小时", "最近一天", "最近一周", "自定义"])
        self.time_range_combo.currentIndexChanged.connect(self.handle_time_range_change)
        time_range_layout.addWidget(self.time_range_combo)
        options_layout.addLayout(time_range_layout)

        # Apply button
        self.apply_options_btn = QPushButton("应用选项")
        self.apply_options_btn.clicked.connect(self.apply_options)
        options_layout.addWidget(self.apply_options_btn)

        options_group.setLayout(options_layout)
        sidebar_layout.addWidget(options_group)

        # Export group
        export_group = QGroupBox("导出")
        export_layout = QVBoxLayout()

        self.export_data_btn = QPushButton("导出数据")
        self.export_data_btn.clicked.connect(self.export_data)
        export_layout.addWidget(self.export_data_btn)

        self.export_chart_btn = QPushButton("导出图表")
        self.export_chart_btn.clicked.connect(self.export_chart)
        export_layout.addWidget(self.export_chart_btn)

        export_group.setLayout(export_layout)
        sidebar_layout.addWidget(export_group)

        # Add stretch to push everything to the top
        sidebar_layout.addStretch()

        # Content area with charts and summary
        content_area = QWidget()
        content_layout = QVBoxLayout(content_area)

        # Charts container
        charts_container = QWidget()
        charts_layout = QVBoxLayout(charts_container)

        # CPU chart
        self.cpu_chart_container = QGroupBox("CPU 使用率")
        cpu_chart_layout = QVBoxLayout()
        self.cpu_chart = MatplotlibCanvas(self, width=5, height=3, dpi=100)
        cpu_chart_layout.addWidget(self.cpu_chart)
        self.cpu_chart_container.setLayout(cpu_chart_layout)
        charts_layout.addWidget(self.cpu_chart_container)

        # Memory chart
        self.memory_chart_container = QGroupBox("内存使用率")
        memory_chart_layout = QVBoxLayout()
        self.memory_chart = MatplotlibCanvas(self, width=5, height=3, dpi=100)
        memory_chart_layout.addWidget(self.memory_chart)
        self.memory_chart_container.setLayout(memory_chart_layout)
        charts_layout.addWidget(self.memory_chart_container)

        # Disk chart
        self.disk_chart_container = QGroupBox("磁盘使用率")
        disk_chart_layout = QVBoxLayout()
        self.disk_chart = MatplotlibCanvas(self, width=5, height=3, dpi=100)
        disk_chart_layout.addWidget(self.disk_chart)
        self.disk_chart_container.setLayout(disk_chart_layout)
        charts_layout.addWidget(self.disk_chart_container)

        # Summary table
        summary_group = QGroupBox("性能摘要")
        summary_layout = QVBoxLayout()
        self.summary_table = QTableWidget()
        self.summary_table.setColumnCount(4)
        self.summary_table.setHorizontalHeaderLabels(["指标", "平均值", "最大值", "最小值"])
        self.summary_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        summary_layout.addWidget(self.summary_table)
        summary_group.setLayout(summary_layout)

        # Add charts and summary to content layout
        charts_layout.addWidget(summary_group)

        # Create a scroll area for the charts
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(charts_container)
        content_layout.addWidget(scroll_area)

        # Add sidebar and content to main content layout
        content_layout.addWidget(sidebar)
        content_layout.addWidget(content_area, 1)  # Content area takes more space

        main_layout.addLayout(content_layout)

        # Initialize placeholder charts
        self.init_placeholder_charts()

    def init_placeholder_charts(self):
        """Initialize placeholder charts."""
        # CPU chart
        self.cpu_chart.axes.clear()
        self.cpu_chart.axes.set_title("加载数据后显示图表")
        self.cpu_chart.axes.set_xlabel("时间")
        self.cpu_chart.axes.set_ylabel("CPU 使用率 (%)")
        self.cpu_chart.axes.grid(True, linestyle='--', alpha=0.7)
        self.cpu_chart.draw()

        # Memory chart
        self.memory_chart.axes.clear()
        self.memory_chart.axes.set_title("加载数据后显示图表")
        self.memory_chart.axes.set_xlabel("时间")
        self.memory_chart.axes.set_ylabel("内存使用率 (%)")
        self.memory_chart.axes.grid(True, linestyle='--', alpha=0.7)
        self.memory_chart.draw()

        # Disk chart
        self.disk_chart.axes.clear()
        self.disk_chart.axes.set_title("加载数据后显示图表")
        self.disk_chart.axes.set_xlabel("时间")
        self.disk_chart.axes.set_ylabel("磁盘使用率 (%)")
        self.disk_chart.axes.grid(True, linestyle='--', alpha=0.7)
        self.disk_chart.draw()

    def load_data(self):
        """Load data from file."""
        try:
            # In a real implementation, this would load data from a file
            # For now, we'll generate sample data
            log("加载数据...", "INFO")

            # Generate sample data
            self.performance_data = {
                'cpu': self.generate_sample_data(100, 0, 100),
                'memory': self.generate_sample_data(100, 20, 80),
                'disk': self.generate_sample_data(100, 0, 90),
                'timestamps': self.generate_timestamps(100)
            }

            # Update data info
            self.data_info_label.setText(
                f"数据源: sample_data.xlsx\n"
                f"加载时间: {self.get_current_time()}\n"
                f"数据点: 100"
            )

            # Update charts and summary
            self.update_charts()
            self.update_summary_table()

            # Show success message
            QMessageBox.information(self, "成功", "数据加载成功！")

        except Exception as e:
            log(f"加载数据时出错: {str(e)}", "ERROR")
            QMessageBox.critical(self, "错误", f"加载数据时出错: {str(e)}")

    def generate_sample_data(self, count, min_val, max_val):
        """Generate sample data for testing."""
        return [min_val + random.random() * (max_val - min_val) for _ in range(count)]

    def generate_timestamps(self, count):
        """Generate timestamps for testing."""
        from datetime import datetime, timedelta
        now = datetime.now()
        return [(now - timedelta(minutes=count-i)).strftime('%H:%M:%S') for i in range(count)]

    def get_current_time(self):
        """Get current time as string."""
        from datetime import datetime
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    def update_charts(self):
        """Update charts with current data."""
        if not self.performance_data:
            return

        # Update CPU chart
        self.cpu_chart.axes.clear()
        self.cpu_chart.axes.plot(self.performance_data['timestamps'], self.performance_data['cpu'], 'b-', label='CPU')

        # Add average line if option is checked
        if self.show_averages_option.isChecked():
            avg = sum(self.performance_data['cpu']) / len(self.performance_data['cpu'])
            self.cpu_chart.axes.axhline(y=avg, color='r', linestyle='--', label=f'平均值: {avg:.2f}%')

        # Add threshold line if option is checked
        if self.show_thresholds_option.isChecked():
            self.cpu_chart.axes.axhline(y=80, color='orange', linestyle='-.', label='阈值: 80%')

        self.cpu_chart.axes.set_title("CPU 使用率")
        self.cpu_chart.axes.set_xlabel("时间")
        self.cpu_chart.axes.set_ylabel("CPU 使用率 (%)")
        self.cpu_chart.axes.grid(True, linestyle='--', alpha=0.7)
        self.cpu_chart.axes.legend()

        # Only show a subset of x-axis labels to avoid overcrowding
        if len(self.performance_data['timestamps']) > 10:
            step = len(self.performance_data['timestamps']) // 10
            self.cpu_chart.axes.set_xticks(range(0, len(self.performance_data['timestamps']), step))
            self.cpu_chart.axes.set_xticklabels([self.performance_data['timestamps'][i] for i in range(0, len(self.performance_data['timestamps']), step)])

        self.cpu_chart.draw()

        # Update Memory chart
        self.memory_chart.axes.clear()
        self.memory_chart.axes.plot(self.performance_data['timestamps'], self.performance_data['memory'], 'g-', label='内存')

        # Add average line if option is checked
        if self.show_averages_option.isChecked():
            avg = sum(self.performance_data['memory']) / len(self.performance_data['memory'])
            self.memory_chart.axes.axhline(y=avg, color='r', linestyle='--', label=f'平均值: {avg:.2f}%')

        # Add threshold line if option is checked
        if self.show_thresholds_option.isChecked():
            self.memory_chart.axes.axhline(y=75, color='orange', linestyle='-.', label='阈值: 75%')

        self.memory_chart.axes.set_title("内存使用率")
        self.memory_chart.axes.set_xlabel("时间")
        self.memory_chart.axes.set_ylabel("内存使用率 (%)")
        self.memory_chart.axes.grid(True, linestyle='--', alpha=0.7)
        self.memory_chart.axes.legend()

        # Only show a subset of x-axis labels to avoid overcrowding
        if len(self.performance_data['timestamps']) > 10:
            step = len(self.performance_data['timestamps']) // 10
            self.memory_chart.axes.set_xticks(range(0, len(self.performance_data['timestamps']), step))
            self.memory_chart.axes.set_xticklabels([self.performance_data['timestamps'][i] for i in range(0, len(self.performance_data['timestamps']), step)])

        self.memory_chart.draw()

        # Update Disk chart
        self.disk_chart.axes.clear()
        self.disk_chart.axes.plot(self.performance_data['timestamps'], self.performance_data['disk'], 'r-', label='磁盘')

        # Add average line if option is checked
        if self.show_averages_option.isChecked():
            avg = sum(self.performance_data['disk']) / len(self.performance_data['disk'])
            self.disk_chart.axes.axhline(y=avg, color='b', linestyle='--', label=f'平均值: {avg:.2f}%')

        # Add threshold line if option is checked
        if self.show_thresholds_option.isChecked():
            self.disk_chart.axes.axhline(y=85, color='orange', linestyle='-.', label='阈值: 85%')

        self.disk_chart.axes.set_title("磁盘使用率")
        self.disk_chart.axes.set_xlabel("时间")
        self.disk_chart.axes.set_ylabel("磁盘使用率 (%)")
        self.disk_chart.axes.grid(True, linestyle='--', alpha=0.7)
        self.disk_chart.axes.legend()

        # Only show a subset of x-axis labels to avoid overcrowding
        if len(self.performance_data['timestamps']) > 10:
            step = len(self.performance_data['timestamps']) // 10
            self.disk_chart.axes.set_xticks(range(0, len(self.performance_data['timestamps']), step))
            self.disk_chart.axes.set_xticklabels([self.performance_data['timestamps'][i] for i in range(0, len(self.performance_data['timestamps']), step)])

        self.disk_chart.draw()

    def update_summary_table(self):
        """Update summary table with current data."""
        if not self.performance_data:
            return

        # Clear table
        self.summary_table.setRowCount(0)

        # Add CPU row
        self.summary_table.insertRow(0)
        self.summary_table.setItem(0, 0, QTableWidgetItem("CPU 使用率"))
        self.summary_table.setItem(0, 1, QTableWidgetItem(f"{sum(self.performance_data['cpu']) / len(self.performance_data['cpu']):.2f}%"))
        self.summary_table.setItem(0, 2, QTableWidgetItem(f"{max(self.performance_data['cpu']):.2f}%"))
        self.summary_table.setItem(0, 3, QTableWidgetItem(f"{min(self.performance_data['cpu']):.2f}%"))

        # Add Memory row
        self.summary_table.insertRow(1)
        self.summary_table.setItem(1, 0, QTableWidgetItem("内存使用率"))
        self.summary_table.setItem(1, 1, QTableWidgetItem(f"{sum(self.performance_data['memory']) / len(self.performance_data['memory']):.2f}%"))
        self.summary_table.setItem(1, 2, QTableWidgetItem(f"{max(self.performance_data['memory']):.2f}%"))
        self.summary_table.setItem(1, 3, QTableWidgetItem(f"{min(self.performance_data['memory']):.2f}%"))

        # Add Disk row
        self.summary_table.insertRow(2)
        self.summary_table.setItem(2, 0, QTableWidgetItem("磁盘使用率"))
        self.summary_table.setItem(2, 1, QTableWidgetItem(f"{sum(self.performance_data['disk']) / len(self.performance_data['disk']):.2f}%"))
        self.summary_table.setItem(2, 2, QTableWidgetItem(f"{max(self.performance_data['disk']):.2f}%"))
        self.summary_table.setItem(2, 3, QTableWidgetItem(f"{min(self.performance_data['disk']):.2f}%"))

    def update_chart_visibility(self):
        """Update chart visibility based on checkboxes."""
        self.cpu_chart_container.setVisible(self.show_cpu_option.isChecked())
        self.memory_chart_container.setVisible(self.show_memory_option.isChecked())
        self.disk_chart_container.setVisible(self.show_disk_option.isChecked())

    def update_chart_options(self):
        """Update chart options based on checkboxes."""
        if self.performance_data:
            self.update_charts()

    def handle_time_range_change(self, index):
        """Handle time range change."""
        if index == 4:  # Custom
            QMessageBox.information(self, "提示", "自定义时间范围功能尚未实现")
        elif self.performance_data:
            # In a real implementation, this would filter the data based on the time range
            self.update_charts()

    def apply_options(self):
        """Apply options to charts."""
        if not self.performance_data:
            QMessageBox.warning(self, "警告", "请先加载数据！")
            return

        self.update_chart_visibility()
        self.update_chart_options()
        QMessageBox.information(self, "成功", "选项已应用！")

    def export_data(self):
        """Export data to CSV."""
        if not self.performance_data:
            QMessageBox.warning(self, "警告", "请先加载数据！")
            return

        QMessageBox.information(self, "提示", "数据导出功能尚未实现")

    def export_chart(self):
        """Export chart as image."""
        if not self.performance_data:
            QMessageBox.warning(self, "警告", "请先加载数据！")
            return

        QMessageBox.information(self, "提示", "图表导出功能尚未实现")

    def activate(self):
        """Activate the component."""
        super().activate()
        log(f"高级分析器组件已激活", "INFO")

    def deactivate(self):
        """Deactivate the component."""
        super().deactivate()
        log(f"高级分析器组件已停用", "INFO")

    def get_menu_items(self):
        """
        Get menu items for this component.

        Returns:
            list: List of menu items
        """
        return [
            {
                "text": "高级分析器",
                "callback": self.activate
            }
        ]
