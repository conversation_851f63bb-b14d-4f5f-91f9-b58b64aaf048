# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'pandas',
        'numpy',
        'matplotlib',
        'openpyxl',
        'PyQt5',
        'email',
        'zipfile',
        'inspect',
        'select',
        'socket',
        'selectors',
        'multiprocessing',
        'calendar',
        'plistlib',
        'PIL',  # 添加PIL模块，因为matplotlib需要它
        'PIL._imaging',  # 确保包含PIL的核心模块
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter', 'scipy',
        # 只排除确定不需要的 PyQt5 模块
        'PyQt5.QtWebEngineWidgets', 'PyQt5.QtWebEngine', 'PyQt5.QtWebEngineCore',
        'PyQt5.QtPositioning', 'PyQt5.QtLocation',
        'PyQt5.QtBluetooth', 'PyQt5.QtNfc',
        # 第三方库中不需要的模块
        'IPython', 'Cython', 'pytest', 'sphinx', 'notebook', 'jupyter',
        'nose', 'numba', 'numexpr',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='性能监控数据分析器_v1.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=['vcruntime140.dll', 'python311.dll', 'VCRUNTIME140.dll', 'VCRUNTIME140_1.dll', 'Qt5Core.dll', 'Qt5Gui.dll', 'Qt5Widgets.dll'],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version='version_info.txt',
)
