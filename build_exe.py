"""
构建可执行文件脚本

这个脚本用于将原始Nmon文件分析工具打包成可执行文件。
"""

import os
import sys
import shutil
from datetime import datetime

try:
    import PyInstaller.__main__
except ImportError:
    print("错误: 未安装PyInstaller，请先安装: pip install pyinstaller")
    sys.exit(1)

# 版本号
VERSION = "3.2"

# 构建目录
BUILD_DIR = "build"
DIST_DIR = "dist"
OUTPUT_DIR = os.path.join(DIST_DIR, f"原始Nmon文件分析工具_v{VERSION}")

# 清理旧的构建文件
def clean_build():
    """清理旧的构建文件"""
    print("清理旧的构建文件...")
    
    # 删除build目录
    if os.path.exists(BUILD_DIR):
        shutil.rmtree(BUILD_DIR)
        print(f"已删除 {BUILD_DIR} 目录")
    
    # 删除dist目录
    if os.path.exists(DIST_DIR):
        shutil.rmtree(DIST_DIR)
        print(f"已删除 {DIST_DIR} 目录")
    
    # 删除spec文件
    for file in os.listdir("."):
        if file.endswith(".spec"):
            os.remove(file)
            print(f"已删除 {file}")

# 构建可执行文件
def build_exe():
    """构建可执行文件"""
    print(f"开始构建原始Nmon文件分析工具 v{VERSION}...")
    
    # 构建参数
    args = [
        "run_raw_nmon_analyzer.py",  # 主脚本
        "--name=原始Nmon文件分析工具",  # 可执行文件名称
        "--onefile",  # 单文件模式
        "--windowed",  # 窗口模式，不显示控制台
        "--icon=icon.ico",  # 图标文件
        f"--version={VERSION}",  # 版本号
        "--clean",  # 清理临时文件
        "--noconfirm",  # 不确认覆盖
    ]
    
    # 添加隐藏导入
    hidden_imports = [
        "--hidden-import=pandas",
        "--hidden-import=numpy",
        "--hidden-import=openpyxl",
        "--hidden-import=matplotlib",
    ]
    args.extend(hidden_imports)
    
    # 添加数据文件
    datas = [
        "--add-data=icon.ico;.",
        "--add-data=README.md;.",
    ]
    args.extend(datas)
    
    # 运行PyInstaller
    PyInstaller.__main__.run(args)
    
    print("构建完成!")

# 创建发布包
def create_release():
    """创建发布包"""
    print("创建发布包...")
    
    # 创建输出目录
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
    
    # 复制可执行文件
    exe_file = os.path.join(DIST_DIR, "原始Nmon文件分析工具.exe")
    if os.path.exists(exe_file):
        shutil.copy(exe_file, OUTPUT_DIR)
        print(f"已复制可执行文件到 {OUTPUT_DIR}")
    else:
        print(f"错误: 找不到可执行文件 {exe_file}")
        return False
    
    # 创建logs目录
    logs_dir = os.path.join(OUTPUT_DIR, "logs")
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
        print(f"已创建logs目录: {logs_dir}")
    
    # 创建README.txt
    readme_content = f"""原始Nmon文件分析工具 v{VERSION}

发布日期: {datetime.now().strftime('%Y-%m-%d')}

使用说明:
1. 双击"原始Nmon文件分析工具.exe"启动程序
2. 选择nmon文件或包含nmon文件的目录进行分析
3. 设置分析选项
4. 点击"分析Nmon文件"按钮开始分析
5. 分析完成后，可以查看结果并导出Excel文件

日志文件位于logs目录中。

版本历史:
v3.2 - 添加日志目录设置功能，优化日志记录，减少系统缓存使用
v3.1 - 修复KeyError: 'analysis_result'错误
v3.0 - 统一界面风格，添加批处理功能
v2.0 - 添加趋势分析功能
v1.5 - 优化图表显示
v1.0 - 初始版本
"""
    
    readme_file = os.path.join(OUTPUT_DIR, "README.txt")
    with open(readme_file, "w", encoding="utf-8") as f:
        f.write(readme_content)
    print(f"已创建README文件: {readme_file}")
    
    # 创建压缩包
    zip_file = f"{OUTPUT_DIR}.zip"
    shutil.make_archive(OUTPUT_DIR, 'zip', DIST_DIR, os.path.basename(OUTPUT_DIR))
    print(f"已创建发布压缩包: {zip_file}")
    
    return True

def main():
    """主函数"""
    # 检查图标文件
    if not os.path.exists("icon.ico"):
        print("警告: 找不到图标文件icon.ico，将使用默认图标")
    
    # 清理旧的构建文件
    clean_build()
    
    # 构建可执行文件
    build_exe()
    
    # 创建发布包
    if create_release():
        print(f"原始Nmon文件分析工具 v{VERSION} 构建成功!")
        print(f"可执行文件位于: {OUTPUT_DIR}")
        print(f"压缩包位于: {OUTPUT_DIR}.zip")
    else:
        print("构建失败!")

if __name__ == "__main__":
    main()
