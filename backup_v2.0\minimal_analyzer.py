"""
最小化版本的性能监控数据分析器

这个脚本提供了一个简化版的性能监控数据分析器，用于解决布局问题。
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QFileDialog,
                            QMessageBox, QGroupBox)
from PyQt5.QtCore import Qt, QSettings
from PyQt5.QtGui import QFont

class MinimalAnalyzer(QMainWindow):
    """最小化版本的性能监控数据分析器"""
    
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("性能监控数据分析器")
        self.setMinimumSize(800, 600)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 添加标题
        title_label = QLabel("性能监控数据分析器")
        title_font = QFont("Arial", 16, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 添加文件选择部分
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout()
        
        # 文件选择按钮
        select_file_button = QPushButton("选择Excel文件")
        select_file_button.clicked.connect(self.select_file)
        file_layout.addWidget(select_file_button)
        
        # 文件路径标签
        self.file_path_label = QLabel("未选择文件")
        file_layout.addWidget(self.file_path_label)
        
        file_group.setLayout(file_layout)
        main_layout.addWidget(file_group)
        
        # 添加处理按钮
        process_button = QPushButton("处理文件")
        process_button.clicked.connect(self.process_file)
        main_layout.addWidget(process_button)
        
        # 添加状态标签
        self.status_label = QLabel("就绪")
        main_layout.addWidget(self.status_label)
        
        # 添加弹性空间
        main_layout.addStretch(1)
    
    def select_file(self):
        """选择Excel文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Excel文件", "", "Excel文件 (*.xlsx *.xls)"
        )
        
        if file_path:
            self.file_path_label.setText(file_path)
            self.status_label.setText(f"已选择文件: {os.path.basename(file_path)}")
    
    def process_file(self):
        """处理选定的文件"""
        file_path = self.file_path_label.text()
        
        if file_path == "未选择文件":
            QMessageBox.warning(self, "警告", "请先选择文件")
            return
        
        # 显示成功消息
        QMessageBox.information(self, "成功", f"文件 {os.path.basename(file_path)} 处理成功！")
        self.status_label.setText("处理完成")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = MinimalAnalyzer()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
