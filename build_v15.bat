@echo off
echo Building executable for Performance Analyzer V1.5...
echo.

REM Clean previous build files
echo Cleaning previous build files...
rmdir /s /q build
rmdir /s /q dist
echo.

REM Run PyInstaller with the spec file
echo Running PyInstaller...
python -m PyInstaller performance_analyzer.spec
echo.

if %ERRORLEVEL% == 0 (
    echo Build completed successfully!
    echo Executable is located in the dist\性能监控数据分析器_V1.5 directory.
) else (
    echo Build failed with error code %ERRORLEVEL%.
)

echo.
pause
