import os
import matplotlib.pyplot as plt
from concurrent.futures import ThreadPoolExecutor
import multiprocessing
import tempfile
import shutil
from openpyxl.drawing.image import Image
from openpyxl import load_workbook
from openpyxl.styles import Font
from logger_utils import log, log_exception, get_logs

class ChartOptimizer:
    """用于优化图表生成和保存过程的类"""

    def __init__(self, chart_generator):
        """
        初始化图表优化器

        参数:
            chart_generator: ChartGenerator实例，用于生成图表
        """
        self.chart_generator = chart_generator
        self.progress_callback = None
        self.is_cancelled = False  # 添加取消标志

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    def set_cancelled(self, cancelled=True):
        """设置取消标志"""
        self.is_cancelled = cancelled
        log(f"图表优化器取消标志已设置为: {cancelled}", "INFO")

    def check_cancelled(self):
        """检查是否取消"""
        if self.is_cancelled:
            self.update_progress("图表生成已取消")
            return True
        return False

    def update_progress(self, message, percent=None):
        """更新进度信息"""
        # 如果有消息，记录到日志
        if message is not None:
            # 记录日志，但不使用返回值
            log(message)

            # 确保 message 是字符串
            if not isinstance(message, str):
                message = str(message)

        # 调用回调函数 - 如果消息为None，只发送进度百分比
        if self.progress_callback:
            self.progress_callback(message, percent)

    def optimize_memory_usage(self):
        """优化内存使用，减少内存泄漏风险"""
        # 关闭所有matplotlib图表
        plt.close('all')

        # 强制垃圾回收
        import gc
        gc.collect()

    def parallel_generate_charts(self, results, temp_dir):
        """
        并行生成所有图表

        参数:
            results: 结果列表
            temp_dir: 临时目录路径

        返回:
            chart_files: 图表文件信息列表
        """
        # 检查是否取消
        if self.check_cancelled():
            return []

        chart_files = []

        def generate_charts_for_file(idx, result):
            """为单个文件生成所有图表"""
            import matplotlib
            # 使用Agg后端，避免线程间的资源竞争
            matplotlib.use('Agg')
            import matplotlib.pyplot as plt

            # 强制关闭所有图表，确保没有残留状态
            plt.close('all')

            # 强制清理matplotlib缓存
            plt.clf()
            plt.cla()

            # 强制垃圾回收
            import gc
            gc.collect()

            # 检查是否取消
            if self.is_cancelled:
                log(f"图表生成已取消，跳过文件 {result.get('File', 'unknown')}")
                return None

            file_name = result['File']
            # 只在后台记录详细信息
            log(f"开始为文件 {file_name} 生成图表...")

            try:
                # 生成CPU图表 - 确保只传递单个结果对象而不是列表
                cpu_path = os.path.join(temp_dir, f"cpu_{idx}.png")
                cpu_path = self.chart_generator.create_cpu_chart(result, cpu_path)
                log(f"生成CPU图表返回路径: {cpu_path}")

                # 强制关闭图表，避免状态混淆
                plt.close('all')

                # 生成内存图表 - 确保只传递单个结果对象而不是列表
                mem_path = os.path.join(temp_dir, f"mem_{idx}.png")
                mem_path = self.chart_generator.create_memory_chart(result, mem_path)
                log(f"生成内存图表返回路径: {mem_path}")

                # 强制关闭图表，避免状态混淆
                plt.close('all')

                # 生成磁盘图表 - 确保只传递单个结果对象而不是列表
                disk_path = os.path.join(temp_dir, f"disk_{idx}.png")
                disk_path = self.chart_generator.create_disk_chart(result, disk_path)
                log(f"生成磁盘图表返回路径: {disk_path}")

                # 强制关闭图表，避免状态混淆
                plt.close('all')

                return {
                    'idx': idx,
                    'file_name': file_name,
                    'cpu_path': cpu_path,
                    'mem_path': mem_path,
                    'disk_path': disk_path,
                    'ip_address': result.get('IP Address', '未知')
                }
            except Exception as e:
                log(f"生成图表时出错: {e}", "ERROR")
                # 即使出错也返回部分结果
                return {
                    'idx': idx,
                    'file_name': file_name,
                    'cpu_path': '',
                    'mem_path': '',
                    'disk_path': '',
                    'ip_address': result.get('IP Address', '未知'),
                    'error': str(e)
                }

        # 全部使用串行处理，避免多线程生成图表时的状态混淆问题
        log(f"使用串行处理生成所有 {len(results)} 个文件的图表")
        # 只向UI发送一次进度更新
        self.update_progress("正在生成图表...", 20)

        # 串行处理所有文件
        for i, result in enumerate(results):
            # 在处理每个文件前强制清理图表状态
            import matplotlib
            matplotlib.use('Agg')  # 确保使用非交互式后端
            import matplotlib.pyplot as plt
            plt.close('all')

            # 强制垃圾回收
            import gc
            gc.collect()

            try:
                log(f"开始处理文件 {i+1}/{len(results)}: {result['File']}")
                chart_info = generate_charts_for_file(i, result)
                chart_files.append(chart_info)

                # 只在后台记录进度
                progress = int((i + 1) / len(results) * 100)
                log(f"图表生成进度: {i+1}/{len(results)}")

                # 每完成一个文件发送一次进度更新
                self.update_progress(f"已处理 {i+1}/{len(results)} 个文件", 20 + int((i + 1) / len(results) * 70))

                # 每处理一个文件后优化内存使用
                self.optimize_memory_usage()
            except Exception as e:
                log(f"生成图表时出错: {e}", "ERROR")

        return chart_files

    def add_charts_to_excel_optimized(self, results, excel_path):
        """
        优化后的图表添加方法，使用并行处理

        参数:
            results: 结果列表
            excel_path: Excel文件路径

        返回:
            (excel_path, logs): 成功返回Excel路径和日志，失败返回(None, logs)
        """
        try:
            # 加载Excel工作簿 - 只在后台记录日志，不发送到UI
            log(f"打开Excel文件: {excel_path}")
            wb = load_workbook(excel_path)

            # 删除旧的工作表
            for old_sheet_name in ['CPU_Chart', 'Memory_Chart', 'Disk_Chart', 'Charts']:
                if old_sheet_name in wb.sheetnames:
                    log(f"删除旧工作表: {old_sheet_name}")
                    del wb[old_sheet_name]

            # 保存工作簿以释放文件锁
            wb.save(excel_path)

            # 创建临时目录存储图表
            # 从设置中获取自定义临时目录
            try:
                from PyQt5.QtCore import QSettings
                settings = QSettings("PerformanceAnalyzer", "Settings")
                custom_temp_dir = settings.value("temp_directory", "", type=str)

                if custom_temp_dir and os.path.isdir(custom_temp_dir):
                    # 在自定义目录中创建临时子目录
                    import uuid
                    temp_subdir = f"chart_temp_{uuid.uuid4().hex[:8]}"
                    temp_dir = os.path.join(custom_temp_dir, temp_subdir)
                    os.makedirs(temp_dir, exist_ok=True)
                    log(f"在自定义目录中创建临时目录: {temp_dir}")
                else:
                    # 使用默认的临时目录
                    temp_dir = tempfile.mkdtemp()
                    log(f"创建默认临时目录: {temp_dir}")
            except Exception as e:
                # 如果出错，使用默认的临时目录
                temp_dir = tempfile.mkdtemp()
                log(f"获取自定义临时目录失败，使用默认目录: {temp_dir}. 错误: {e}", "WARNING")

            # 只向UI发送一条摘要信息
            self.update_progress("正在生成图表...", 10)

            # 检查是否取消
            if self.check_cancelled():
                return None, ["操作已取消"]

            try:
                # 并行生成所有图表
                chart_files = self.parallel_generate_charts(results, temp_dir)

                # 重新加载工作簿并添加图表
                wb = load_workbook(excel_path)

                # 添加图表到Excel - 只向UI发送一次进度更新
                self.update_progress("正在将图表添加到Excel...", 50)
                for i, chart_info in enumerate(chart_files):
                    # 检查是否取消
                    if self.check_cancelled():
                        return None, ["操作已取消，但Excel文件已部分生成"]
                    # 只在后台记录详细进度
                    log(f"添加图表进度: {i+1}/{len(chart_files)}")
                    # 每完成四分之一的图表发送一次进度更新
                    if i % max(1, len(chart_files)//4) == 0 or i == len(chart_files)-1:
                        progress = int(50 + (i + 1) / len(chart_files) * 50)  # 50%-100%
                        self.update_progress(None, progress)

                    # 为每个文件创建工作表
                    sheet_name = f"Chart_{chart_info['idx']+1}_{chart_info['file_name'][:20]}"
                    sheet_name = ''.join(c if c.isalnum() or c in '_- ' else '_' for c in sheet_name)
                    sheet_name = sheet_name[:31]

                    if sheet_name in wb.sheetnames:
                        del wb[sheet_name]
                    ws = wb.create_sheet(sheet_name)

                    # 设置列宽和行高
                    for col in range(1, 20):
                        ws.column_dimensions[chr(64 + col)].width = 15

                    for row in range(1, 60):
                        ws.row_dimensions[row].height = 20

                    # 设置文件信息标题
                    ws['A1'] = f'文件: {chart_info["file_name"]}'
                    ws['A1'].font = Font(bold=True, size=14)
                    ws['A2'] = f'IP地址: {chart_info["ip_address"]}'
                    ws['A2'].font = Font(bold=True, size=12)

                    # 设置图表标题
                    ws['A4'] = 'CPU使用率图表'
                    ws['A4'].font = Font(bold=True, size=12)
                    ws['A30'] = '内存使用率图表'
                    ws['A30'].font = Font(bold=True, size=12)
                    ws['A56'] = '磁盘繁忙度图表'
                    ws['A56'].font = Font(bold=True, size=12)

                    # 添加图表
                    try:
                        # 添加CPU图表
                        img = Image(chart_info['cpu_path'])
                        img.width = 800
                        img.height = 450
                        ws.add_image(img, 'A5')

                        # 添加内存图表
                        img = Image(chart_info['mem_path'])
                        img.width = 800
                        img.height = 450
                        ws.add_image(img, 'A31')

                        # 添加磁盘图表
                        img = Image(chart_info['disk_path'])
                        img.width = 800
                        img.height = 450
                        ws.add_image(img, 'A57')
                    except Exception as e:
                        self.update_progress(f"添加图表到Excel时出错: {e}")

                # 保存工作簿 - 只在后台记录
                log(f"保存Excel文件: {excel_path}")
                wb.save(excel_path)
                # 发送100%完成信号
                self.update_progress("图表添加完成", 100)

                # 优化内存使用
                self.optimize_memory_usage()

                # 返回精简的日志信息和Excel文件路径
                # 只返回最后10条日志，减少传输量
                logs = get_logs()[-10:] if len(get_logs()) > 10 else get_logs()
                return excel_path, logs

            finally:
                # 清理临时文件 - 只在后台记录
                try:
                    shutil.rmtree(temp_dir)
                    log("清理临时文件完成")
                except Exception as e:
                    log(f"清理临时文件时出错: {e}", "ERROR")

        except Exception as e:
            # 只向UI发送简洁的错误信息
            self.update_progress(f"添加图表到Excel时出错: {e}")
            # 在后台记录详细错误
            import traceback
            log(f"错误详情: {traceback.format_exc()}", "ERROR")
            # 只返回最后10条日志，减少传输量
            logs = get_logs()[-10:] if len(get_logs()) > 10 else get_logs()
            return None, logs
