# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(2, 0, 0, 0),
    prodvers=(2, 0, 0, 0),
    # Contains a bitmask that specifies the valid bits 'flags'r
    mask=0x3f,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x40004,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404b0',
        [StringStruct(u'CompanyName', u''),
        StringStruct(u'FileDescription', u'性能监控数据分析器'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'性能监控数据分析器'),
        StringStruct(u'LegalCopyright', u'Copyright (C) 2025'),
        StringStruct(u'OriginalFilename', u'性能监控数据分析器.exe'),
        StringStruct(u'ProductName', u'性能监控数据分析器'),
        StringStruct(u'ProductVersion', u'*******')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
- version_info.txt
- web_analyzer_component.py
- backup_v2.0\backup_source_v1.5.py
- backup_v2.0\batch_processor.py
- backup_v2.0\build_exe.bat
- backup_v2.0\build_v15.bat
- backup_v2.0\build_v151_onefile.bat
- backup_v2.0\chart_generator.py
- backup_v2.0\chart_optimizer.py
- backup_v2.0\component_base.py
- backup_v2.0\component_manager.py
- backup_v2.0\data_analyzer.py
- backup_v2.0\ensure_fonts.py
- backup_v2.0\excel_generator.py
- backup_v2.0\excel_processor.py
- backup_v2.0\exception_handler.py
- backup_v2.0\font_settings_dialog.py
- backup_v2.0\gui.py
- backup_v2.0\logger_utils.py
- backup_v2.0\main.py
- backup_v2.0\minimal_analyzer.py
- backup_v2.0\nmon_analyzer_gui.py
- backup_v2.0\nmon_parser.py
- backup_v2.0\nmon_to_excel.py
- backup_v2.0\optimized_plotter.py
- backup_v2.0\performance_analyzer.spec
- backup_v2.0\performance_analyzer_onefile.spec
- backup_v2.0\raw_nmon_analyzer.py
- backup_v2.0\raw_nmon_analyzer_gui.py
- backup_v2.0\README.md
- backup_v2.0\README_UNIFIED_TOOLS.md
- backup_v2.0\requirements.txt
- backup_v2.0\run_nmon_analyzer.py
- backup_v2.0\run_nmon_processor.py
- backup_v2.0\run_performance_analyzer.py
- backup_v2.0\run_raw_nmon_analyzer.py
- backup_v2.0\run_unified_tool.py
- backup_v2.0\save_options_dialog.py
- backup_v2.0\save_results_thread.py
- backup_v2.0\settings_dialog.py
- backup_v2.0\simple_performance_analyzer.py
- backup_v2.0\test_advanced_analyzer.py
- backup_v2.0\test_nmon_file.py
- backup_v2.0\test_nmon_processor.py
- backup_v2.0\test_nmon_timestamp.py
- backup_v2.0\test_save_dialog.py
- backup_v2.0\unified_gui.py
- backup_v2.0\unified_launcher.py
- backup_v2.0\version.py
- backup_v2.0\version_info.txt
- backup_v2.0\web_analyzer_component.py
- fonts\font_settings.json
- icons\app_icon.py
- icons/app_icon.ico
- icons/app_icon.png
- icons/app_icon.py
- fonts/font_settings.json
- fonts/Lora-Regular.ttf
- fonts/OpenSans-Regular.ttf
- fonts/Roboto-Regular.ttf
- fonts/SourceHanSansSC-Regular.otf
- fonts/SourceHanSerifSC-Regular.otf
