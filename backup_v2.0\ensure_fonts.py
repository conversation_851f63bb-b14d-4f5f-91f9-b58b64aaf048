import os
import requests
import sys
import json
from logger_utils import log

# Define font information
FONT_INFO = {
    # Chinese fonts
    'SourceHanSansSC-Regular': {
        'url': 'https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/SimplifiedChinese/SourceHanSansSC-Regular.otf',
        'filename': 'SourceHanSansSC-Regular.otf',
        'display_name': '思源黑体 (Source Han Sans)',
        'type': 'chinese',
        'style': 'sans-serif'
    },
    'SourceHanSerifSC-Regular': {
        'url': 'https://github.com/adobe-fonts/source-han-serif/raw/release/OTF/SimplifiedChinese/SourceHanSerifSC-Regular.otf',
        'filename': 'SourceHanSerifSC-Regular.otf',
        'display_name': '思源宋体 (Source Han Serif)',
        'type': 'chinese',
        'style': 'serif'
    },
    # English fonts
    'OpenSans-Regular': {
        'url': 'https://fonts.gstatic.com/s/opensans/v35/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4gaVc.ttf',
        'filename': 'OpenSans-Regular.ttf',
        'display_name': 'Open Sans',
        'type': 'english',
        'style': 'sans-serif'
    },
    'Roboto-Regular': {
        'url': 'https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxP.ttf',
        'filename': 'Roboto-Regular.ttf',
        'display_name': 'Roboto',
        'type': 'english',
        'style': 'sans-serif'
    },
    'Lora-Regular': {
        'url': 'https://fonts.gstatic.com/s/lora/v32/0QI6MX1D_JOuGQbT0gvTJPa787weuxJBkqg.ttf',
        'filename': 'Lora-Regular.ttf',
        'display_name': 'Lora',
        'type': 'english',
        'style': 'serif'
    }
}

# Default font selections
DEFAULT_FONTS = {
    'chinese': 'SourceHanSansSC-Regular',
    'english': 'OpenSans-Regular'
}

def get_font_dir():
    """Get the font directory path"""
    return os.path.join(os.path.dirname(os.path.abspath(__file__)), 'fonts')

def ensure_font_dir():
    """Ensure the font directory exists"""
    font_dir = get_font_dir()
    if not os.path.exists(font_dir):
        try:
            os.makedirs(font_dir)
            print(f"Created fonts directory: {font_dir}")
            log(f"Created fonts directory: {font_dir}", "INFO")
            return True
        except Exception as e:
            print(f"Error creating fonts directory: {str(e)}")
            log(f"Error creating fonts directory: {str(e)}", "ERROR")
            return False
    return True

def download_font(font_key):
    """Download a specific font"""
    if font_key not in FONT_INFO:
        log(f"Unknown font key: {font_key}", "ERROR")
        return False

    font_info = FONT_INFO[font_key]
    font_dir = get_font_dir()
    font_path = os.path.join(font_dir, font_info['filename'])

    # Check if the font already exists
    if os.path.exists(font_path):
        log(f"Font already exists: {font_path}", "INFO")
        return True

    # Try to download the font
    print(f"Downloading font: {font_info['display_name']}...")
    log(f"Downloading font: {font_info['display_name']}", "INFO")

    try:
        # Download the font file
        print(f"Downloading from: {font_info['url']}")
        response = requests.get(font_info['url'], timeout=30)
        response.raise_for_status()  # Raise an exception for HTTP errors

        # Save the font file
        with open(font_path, 'wb') as f:
            f.write(response.content)

        print(f"Font downloaded successfully to {font_path}")
        log(f"Font downloaded successfully to {font_path}", "INFO")
        return True

    except Exception as e:
        print(f"Error downloading font: {str(e)}")
        log(f"Error downloading font {font_key}: {str(e)}", "ERROR")
        return False

def ensure_fonts():
    """Ensure that all required fonts are available"""
    print("Checking for required fonts...")
    log("Checking for required fonts", "INFO")

    # Ensure font directory exists
    if not ensure_font_dir():
        return False

    # Download default fonts first
    success = True
    for font_type, font_key in DEFAULT_FONTS.items():
        if not download_font(font_key):
            log(f"Failed to download default {font_type} font: {font_key}", "ERROR")
            success = False

    return success

def get_available_fonts():
    """Get a list of available fonts"""
    font_dir = get_font_dir()
    available_fonts = {}

    for font_key, font_info in FONT_INFO.items():
        font_path = os.path.join(font_dir, font_info['filename'])
        if os.path.exists(font_path):
            available_fonts[font_key] = {
                'path': font_path,
                'display_name': font_info['display_name'],
                'type': font_info['type'],
                'style': font_info['style']
            }

    return available_fonts

def get_font_settings_path():
    """Get the path to the font settings file"""
    return os.path.join(get_font_dir(), 'font_settings.json')

def save_font_settings(chinese_font, english_font):
    """Save font settings to a file"""
    settings = {
        'chinese_font': chinese_font,
        'english_font': english_font
    }

    try:
        with open(get_font_settings_path(), 'w', encoding='utf-8') as f:
            json.dump(settings, f, indent=2)
        log(f"Font settings saved: Chinese={chinese_font}, English={english_font}", "INFO")
        return True
    except Exception as e:
        log(f"Error saving font settings: {str(e)}", "ERROR")
        return False

def load_font_settings():
    """Load font settings from file"""
    settings_path = get_font_settings_path()

    if not os.path.exists(settings_path):
        # Use defaults if settings file doesn't exist
        return DEFAULT_FONTS['chinese'], DEFAULT_FONTS['english']

    try:
        with open(settings_path, 'r', encoding='utf-8') as f:
            settings = json.load(f)

        chinese_font = settings.get('chinese_font', DEFAULT_FONTS['chinese'])
        english_font = settings.get('english_font', DEFAULT_FONTS['english'])

        # Validate that the fonts exist
        font_dir = get_font_dir()
        if chinese_font in FONT_INFO and os.path.exists(os.path.join(font_dir, FONT_INFO[chinese_font]['filename'])):
            pass  # Chinese font is valid
        else:
            chinese_font = DEFAULT_FONTS['chinese']
            log(f"Invalid Chinese font in settings, using default: {chinese_font}", "WARNING")

        if english_font in FONT_INFO and os.path.exists(os.path.join(font_dir, FONT_INFO[english_font]['filename'])):
            pass  # English font is valid
        else:
            english_font = DEFAULT_FONTS['english']
            log(f"Invalid English font in settings, using default: {english_font}", "WARNING")

        return chinese_font, english_font

    except Exception as e:
        log(f"Error loading font settings: {str(e)}", "ERROR")
        return DEFAULT_FONTS['chinese'], DEFAULT_FONTS['english']

# For backward compatibility
def ensure_chinese_font():
    """Ensure that the Chinese font is available (for backward compatibility)"""
    return ensure_fonts()

if __name__ == "__main__":
    if ensure_chinese_font():
        print("Chinese font is available.")
        sys.exit(0)
    else:
        print("Chinese font is not available. Will use system fonts.")
        sys.exit(1)
