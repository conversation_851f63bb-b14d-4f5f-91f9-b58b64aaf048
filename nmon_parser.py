"""
Nmon文件解析器

这个模块用于解析nmon原始文件，提取性能数据。
"""

import os
import re
from datetime import datetime
from collections import defaultdict


class NmonParser:
    """Nmon文件解析器类。"""

    def __init__(self, nmon_file_path):
        """
        初始化Nmon文件解析器。

        参数:
            nmon_file_path: nmon文件路径
        """
        self.nmon_file_path = nmon_file_path
        self.progress_callback = None
        self.log_callback = None

        # 解析结果
        self.hostname = ""
        self.os_version = ""
        self.date = ""
        self.time = ""
        self.interval_seconds = 0

        # 时间戳数据
        self.timestamps = {}  # 格式: {timestamp_id: timestamp_value}
        self.timestamp_map = {}  # 格式: {t_index: timestamp_id}

        # 性能数据
        self.cpu_data = {}  # 格式: {timestamp_id: {'user': value, 'sys': value, 'wait': value, 'idle': value, 'busy': value}}
        self.memory_data = {}  # 格式: {timestamp_id: {'total': value, 'free': value}}
        self.disk_data = {}  # 格式: {timestamp_id: {disk_name: {'busy': value, 'read': value, 'write': value}}}
        self.network_data = {}  # 格式: {timestamp_id: {interface_name: {'read': value, 'write': value}}}

        # 系统信息数据
        self.system_info = {}  # 格式: {category: {key: value}}

    def set_callbacks(self, progress_callback=None, log_callback=None):
        """
        设置回调函数。

        参数:
            progress_callback: 进度回调函数
            log_callback: 日志回调函数
        """
        self.progress_callback = progress_callback
        self.log_callback = log_callback

    def log(self, message):
        """记录日志消息。"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def update_progress(self, percent):
        """更新进度。"""
        if self.progress_callback:
            self.progress_callback(percent)

    def parse(self):
        """
        解析nmon文件。

        返回:
            dict: 解析结果
        """
        self.log(f"开始解析nmon文件: {self.nmon_file_path}")

        # 读取nmon文件
        with open(self.nmon_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        total_lines = len(lines)
        self.log(f"nmon文件共有 {total_lines} 行")

        # 首先解析时间戳行（ZZZZ行）
        self._parse_timestamps(lines)

        # 解析其他数据行
        self._parse_data_lines(lines)

        # 解析BBBP行（系统信息）
        self._parse_bbbp_lines(lines)

        # 返回解析结果
        return {
            'hostname': self.hostname,
            'os_version': self.os_version,
            'date': self.date,
            'time': self.time,
            'interval_seconds': self.interval_seconds,
            'timestamps': self.timestamps,
            'cpu_data': self.cpu_data,
            'memory_data': self.memory_data,
            'disk_data': self.disk_data,
            'network_data': self.network_data,
            'system_info': self.system_info
        }

    def _parse_timestamps(self, lines):
        """
        解析时间戳行（ZZZZ行）。

        参数:
            lines: nmon文件的所有行
        """
        self.log("解析时间戳行...")

        timestamp_id = 0
        for line in lines:
            line = line.strip()
            if line.startswith('ZZZZ'):
                parts = line.split(',')
                if len(parts) >= 4:
                    t_index = parts[1]  # 例如: T0001
                    time_str = parts[2]  # 例如: 18:03:04
                    date_str = parts[3]  # 例如: 16-APR-2025

                    # 构建完整时间戳
                    try:
                        timestamp = datetime.strptime(f"{date_str} {time_str}", "%d-%b-%Y %H:%M:%S")

                        # 存储时间戳
                        self.timestamps[timestamp_id] = {
                            't_index': t_index,
                            'time': time_str,
                            'date': date_str,
                            'timestamp': timestamp
                        }

                        # 建立T序号到时间戳ID的映射
                        self.timestamp_map[t_index] = timestamp_id

                        timestamp_id += 1
                    except ValueError as e:
                        self.log(f"解析时间戳出错: {e}, 行: {line}")

        self.log(f"解析到 {len(self.timestamps)} 个时间戳")

    def _parse_data_lines(self, lines):
        """
        解析数据行。

        参数:
            lines: nmon文件的所有行
        """
        self.log("解析数据行...")

        # 解析基本信息
        for line in lines:
            line = line.strip()
            if line.startswith('AAA,host,'):
                self.hostname = line.split(',')[2]
            elif line.startswith('AAA,OS,'):
                self.os_version = line.split(',')[2]
            elif line.startswith('AAA,date,'):
                self.date = line.split(',')[2]
            elif line.startswith('AAA,time,'):
                self.time = line.split(',')[2]
            elif line.startswith('AAA,interval,'):
                try:
                    self.interval_seconds = int(line.split(',')[2])
                except (ValueError, IndexError):
                    self.interval_seconds = 60  # 默认为60秒

        # 解析CPU数据
        cpu_pattern = re.compile(r'^CPU_ALL')
        for line in lines:
            line = line.strip()
            if cpu_pattern.match(line):
                parts = line.split(',')
                if len(parts) >= 6:
                    t_index = parts[1]  # 例如: T0001
                    if t_index in self.timestamp_map:
                        timestamp_id = self.timestamp_map[t_index]

                        # 解析CPU使用率
                        try:
                            user = float(parts[2])
                            sys = float(parts[3])
                            wait = float(parts[4])
                            idle = float(parts[5])
                            busy = user + sys + wait

                            # 存储CPU数据
                            self.cpu_data[timestamp_id] = {
                                'user': user,
                                'sys': sys,
                                'wait': wait,
                                'idle': idle,
                                'busy': busy
                            }
                        except (ValueError, IndexError) as e:
                            self.log(f"解析CPU数据出错: {e}, 行: {line}")

        # 解析内存数据
        mem_pattern = re.compile(r'^MEM,')
        mem_header = None  # 用于存储内存字段的头部

        # 首先查找内存字段的头部行
        for line in lines:
            line = line.strip()
            if line.startswith('MEM,Memory MB'):
                mem_header = line.split(',')
                self.log(f"找到内存字段头部: {mem_header}")
                break

        # 如果没有找到头部，使用默认字段名
        if not mem_header:
            self.log("未找到内存字段头部行，使用默认字段名")
            mem_header = ['MEM', 'timestamp', 'memtotal', 'hightotal', 'lowtotal', 'swaptotal', 'memfree', 'highfree', 'lowfree', 'swapfree', 'memshared', 'cached', 'active', 'bigfree', 'buffers', 'swapcached', 'inactive']

        # 解析内存数据行
        for line in lines:
            line = line.strip()
            if mem_pattern.match(line) and not line.startswith('MEM,Memory MB'):
                parts = line.split(',')
                if len(parts) >= 3:  # 至少有MEM,时间戳,一个值
                    t_index = parts[1]  # 例如: T0001
                    if t_index in self.timestamp_map:
                        timestamp_id = self.timestamp_map[t_index]

                        # 解析内存使用情况
                        try:
                            # 初始化内存数据字典
                            memory_data_dict = {}

                            # 遍历所有内存字段
                            for i in range(2, min(len(parts), len(mem_header))):
                                field_name = mem_header[i] if i < len(mem_header) else f"field_{i}"
                                try:
                                    field_value = float(parts[i])
                                    memory_data_dict[field_name] = field_value
                                except (ValueError, IndexError):
                                    memory_data_dict[field_name] = 0.0

                            # 存储内存数据
                            self.memory_data[timestamp_id] = memory_data_dict

                            # 确保基本字段存在，以兼容现有代码
                            if 'memtotal' in memory_data_dict:
                                self.memory_data[timestamp_id]['total'] = memory_data_dict['memtotal']
                            elif len(parts) > 2:
                                self.memory_data[timestamp_id]['total'] = float(parts[2])

                            if 'memfree' in memory_data_dict:
                                self.memory_data[timestamp_id]['free'] = memory_data_dict['memfree']
                            elif len(parts) > 6:
                                self.memory_data[timestamp_id]['free'] = float(parts[6])

                        except Exception as e:
                            self.log(f"解析内存数据出错: {e}, 行: {line}")
                    else:
                        self.log(f"时间戳索引不存在: {t_index}")
                else:
                    self.log(f"内存数据行格式不正确: {line}")



        # 解析磁盘数据
        disk_busy_pattern = re.compile(r'^DISKBUSY,')
        disk_read_pattern = re.compile(r'^DISKREAD,')
        disk_write_pattern = re.compile(r'^DISKWRITE,')

        # 查找磁盘字段的头部行
        disk_busy_header = None
        disk_read_header = None
        disk_write_header = None

        for line in lines:
            line = line.strip()
            if line.startswith('DISKBUSY,Disk %Busy'):
                disk_busy_header = line.split(',')
                self.log(f"找到磁盘忙碌度字段头部: {disk_busy_header}")
            elif line.startswith('DISKREAD,Disk Read KB/s'):
                disk_read_header = line.split(',')
                self.log(f"找到磁盘读取字段头部: {disk_read_header}")
            elif line.startswith('DISKWRITE,Disk Write KB/s'):
                disk_write_header = line.split(',')
                self.log(f"找到磁盘写入字段头部: {disk_write_header}")

        # 如果没有找到头部，使用默认字段名
        if not disk_busy_header:
            self.log("未找到磁盘忙碌度字段头部行，使用默认字段名")
            disk_busy_header = ['DISKBUSY', 'timestamp', 'vda', 'vda1', 'vda2', 'vda3', 'vdb', 'sr0', 'dm-0', 'dm-1', 'dm-2', 'dm-3', 'dm-4']

        if not disk_read_header:
            self.log("未找到磁盘读取字段头部行，使用默认字段名")
            disk_read_header = ['DISKREAD', 'timestamp', 'vda', 'vda1', 'vda2', 'vda3', 'vdb', 'sr0', 'dm-0', 'dm-1', 'dm-2', 'dm-3', 'dm-4']

        if not disk_write_header:
            self.log("未找到磁盘写入字段头部行，使用默认字段名")
            disk_write_header = ['DISKWRITE', 'timestamp', 'vda', 'vda1', 'vda2', 'vda3', 'vdb', 'sr0', 'dm-0', 'dm-1', 'dm-2', 'dm-3', 'dm-4']

        # 解析磁盘忙碌度
        for line in lines:
            line = line.strip()
            if disk_busy_pattern.match(line) and not line.startswith('DISKBUSY,Disk %Busy'):
                parts = line.split(',')
                if len(parts) >= 3:  # 至少有DISKBUSY,时间戳,一个值
                    t_index = parts[1]  # 例如: T0001
                    if t_index in self.timestamp_map:
                        timestamp_id = self.timestamp_map[t_index]

                        # 初始化该时间戳的磁盘数据
                        if timestamp_id not in self.disk_data:
                            self.disk_data[timestamp_id] = {}

                        # 解析每个磁盘的忙碌度
                        try:
                            # 假设格式是: DISKBUSY,T0011,2.0,0.0,0.0,2.0,62.9,0.0,2.0,0.0,0.0,0.0,62.9
                            # 分别对应: vda,vda1,vda2,vda3,vdb,sr0,dm-0,dm-1,dm-2,dm-3,dm-4

                            for i in range(2, min(len(parts), len(disk_busy_header))):
                                try:
                                    disk_name = disk_busy_header[i] if i < len(disk_busy_header) else f"disk_{i-2}"
                                    busy_value = float(parts[i])

                                    # 初始化该磁盘的数据
                                    if disk_name not in self.disk_data[timestamp_id]:
                                        self.disk_data[timestamp_id][disk_name] = {
                                            'busy': 0,
                                            'read': 0,
                                            'write': 0
                                        }

                                    # 存储磁盘忙碌度
                                    self.disk_data[timestamp_id][disk_name]['busy'] = busy_value
                                except (ValueError, IndexError) as e:
                                    self.log(f"解析磁盘忙碌度出错: {e}, 磁盘: {disk_name if 'disk_name' in locals() else 'unknown'}, 行: {line}")
                        except Exception as e:
                            self.log(f"解析磁盘忙碌度整行出错: {e}, 行: {line}")
                    else:
                        self.log(f"时间戳索引不存在: {t_index}")
                else:
                    self.log(f"磁盘忙碌度行格式不正确: {line}")

        # 解析磁盘读取
        for line in lines:
            line = line.strip()
            if disk_read_pattern.match(line) and not line.startswith('DISKREAD,Disk Read KB/s'):
                parts = line.split(',')
                if len(parts) >= 3:  # 至少有DISKREAD,时间戳,一个值
                    t_index = parts[1]  # 例如: T0001
                    if t_index in self.timestamp_map:
                        timestamp_id = self.timestamp_map[t_index]

                        # 初始化该时间戳的磁盘数据
                        if timestamp_id not in self.disk_data:
                            self.disk_data[timestamp_id] = {}

                        # 解析每个磁盘的读取速率
                        try:
                            # 假设格式是: DISKREAD,T0011,17073.0,0.0,0.0,17073.0,415227.9,0.0,17073.0,0.0,0.0,0.0,415419.7
                            # 分别对应: vda,vda1,vda2,vda3,vdb,sr0,dm-0,dm-1,dm-2,dm-3,dm-4

                            for i in range(2, min(len(parts), len(disk_read_header))):
                                try:
                                    disk_name = disk_read_header[i] if i < len(disk_read_header) else f"disk_{i-2}"
                                    read_value = float(parts[i])

                                    # 初始化该磁盘的数据
                                    if disk_name not in self.disk_data[timestamp_id]:
                                        self.disk_data[timestamp_id][disk_name] = {
                                            'busy': 0,
                                            'read': 0,
                                            'write': 0
                                        }

                                    # 存储磁盘读取速率
                                    self.disk_data[timestamp_id][disk_name]['read'] = read_value
                                except (ValueError, IndexError) as e:
                                    self.log(f"解析磁盘读取速率出错: {e}, 磁盘: {disk_name if 'disk_name' in locals() else 'unknown'}, 行: {line}")
                        except Exception as e:
                            self.log(f"解析磁盘读取速率整行出错: {e}, 行: {line}")
                    else:
                        self.log(f"时间戳索引不存在: {t_index}")
                else:
                    self.log(f"磁盘读取速率行格式不正确: {line}")

        # 解析磁盘写入
        for line in lines:
            line = line.strip()
            if disk_write_pattern.match(line) and not line.startswith('DISKWRITE,Disk Write KB/s'):
                parts = line.split(',')
                if len(parts) >= 3:  # 至少有DISKWRITE,时间戳,一个值
                    t_index = parts[1]  # 例如: T0001
                    if t_index in self.timestamp_map:
                        timestamp_id = self.timestamp_map[t_index]

                        # 初始化该时间戳的磁盘数据
                        if timestamp_id not in self.disk_data:
                            self.disk_data[timestamp_id] = {}

                        # 解析每个磁盘的写入速率
                        try:
                            # 假设格式是: DISKWRITE,T0011,0.0,0.0,0.0,0.0,265763.0,0.0,0.0,0.0,0.0,0.0,271454.0
                            # 分别对应: vda,vda1,vda2,vda3,vdb,sr0,dm-0,dm-1,dm-2,dm-3,dm-4

                            for i in range(2, min(len(parts), len(disk_write_header))):
                                try:
                                    disk_name = disk_write_header[i] if i < len(disk_write_header) else f"disk_{i-2}"
                                    write_value = float(parts[i])

                                    # 初始化该磁盘的数据
                                    if disk_name not in self.disk_data[timestamp_id]:
                                        self.disk_data[timestamp_id][disk_name] = {
                                            'busy': 0,
                                            'read': 0,
                                            'write': 0
                                        }

                                    # 存储磁盘写入速率
                                    self.disk_data[timestamp_id][disk_name]['write'] = write_value
                                except (ValueError, IndexError) as e:
                                    self.log(f"解析磁盘写入速率出错: {e}, 磁盘: {disk_name if 'disk_name' in locals() else 'unknown'}, 行: {line}")
                        except Exception as e:
                            self.log(f"解析磁盘写入速率整行出错: {e}, 行: {line}")
                    else:
                        self.log(f"时间戳索引不存在: {t_index}")
                else:
                    self.log(f"磁盘写入速率行格式不正确: {line}")

        # 解析网络数据
        net_pattern = re.compile(r'^NET,')
        net_header = None

        # 首先查找网络字段的头部行
        for line in lines:
            line = line.strip()
            if line.startswith('NET,Network I/O'):
                net_header = line.split(',')
                self.log(f"找到网络字段头部: {net_header}")
                break

        # 如果没有找到头部，使用默认字段名
        if not net_header:
            self.log("未找到网络字段头部行，使用默认字段名")
            net_header = ['NET', 'timestamp', 'lo-read-KB/s', 'enp1s0-read-KB/s', 'enp8s0-read-KB/s', 'lo-write-KB/s', 'enp1s0-write-KB/s', 'enp8s0-write-KB/s']

        # 解析网络数据行
        for line in lines:
            line = line.strip()
            if net_pattern.match(line) and not line.startswith('NET,Network I/O'):
                parts = line.split(',')
                if len(parts) >= 3:  # 至少有NET,时间戳,一个值
                    t_index = parts[1]  # 例如: T0001
                    if t_index in self.timestamp_map:
                        timestamp_id = self.timestamp_map[t_index]

                        # 初始化该时间戳的网络数据
                        if timestamp_id not in self.network_data:
                            self.network_data[timestamp_id] = {}

                        # 解析网络接口数据
                        try:
                            # 处理读取数据
                            if len(parts) > 2:
                                # 假设格式是: NET,T0011,0.0,31.0,0.0,0.0,29.7,0.0
                                # 分别对应: lo-read, enp1s0-read, enp8s0-read, lo-write, enp1s0-write, enp8s0-write

                                # 处理lo接口
                                if len(parts) > 2:
                                    lo_read = float(parts[2])
                                    self.network_data[timestamp_id]['lo'] = self.network_data[timestamp_id].get('lo', {})
                                    self.network_data[timestamp_id]['lo']['read'] = lo_read

                                # 处理enp1s0接口
                                if len(parts) > 3:
                                    enp1s0_read = float(parts[3])
                                    self.network_data[timestamp_id]['enp1s0'] = self.network_data[timestamp_id].get('enp1s0', {})
                                    self.network_data[timestamp_id]['enp1s0']['read'] = enp1s0_read

                                # 处理enp8s0接口
                                if len(parts) > 4:
                                    enp8s0_read = float(parts[4])
                                    self.network_data[timestamp_id]['enp8s0'] = self.network_data[timestamp_id].get('enp8s0', {})
                                    self.network_data[timestamp_id]['enp8s0']['read'] = enp8s0_read

                                # 处理lo写入
                                if len(parts) > 5:
                                    lo_write = float(parts[5])
                                    self.network_data[timestamp_id]['lo'] = self.network_data[timestamp_id].get('lo', {})
                                    self.network_data[timestamp_id]['lo']['write'] = lo_write

                                # 处理enp1s0写入
                                if len(parts) > 6:
                                    enp1s0_write = float(parts[6])
                                    self.network_data[timestamp_id]['enp1s0'] = self.network_data[timestamp_id].get('enp1s0', {})
                                    self.network_data[timestamp_id]['enp1s0']['write'] = enp1s0_write

                                # 处理enp8s0写入
                                if len(parts) > 7:
                                    enp8s0_write = float(parts[7])
                                    self.network_data[timestamp_id]['enp8s0'] = self.network_data[timestamp_id].get('enp8s0', {})
                                    self.network_data[timestamp_id]['enp8s0']['write'] = enp8s0_write
                        except (ValueError, IndexError) as e:
                            self.log(f"解析网络数据出错: {e}, 行: {line}")
                    else:
                        self.log(f"时间戳索引不存在: {t_index}")
                else:
                    self.log(f"网络数据行格式不正确: {line}")


        self.log(f"解析完成: CPU数据 {len(self.cpu_data)} 条, 内存数据 {len(self.memory_data)} 条, 磁盘数据 {len(self.disk_data)} 条, 网络数据 {len(self.network_data)} 条")

    def _parse_bbbp_lines(self, lines):
        """
        解析BBBP行（系统信息）。

        参数:
            lines: nmon文件的所有行
        """
        self.log("解析BBBP行（系统信息）...")

        # 初始化系统信息字典
        self.system_info = {
            'basic_info': {
                '主机名': self.hostname,
                '操作系统': self.os_version,
                '日期': self.date,
                '时间': self.time,
                '采样间隔(秒)': self.interval_seconds,
                '采样点数量': len(self.timestamps)
            },
            'os_info': {},
            'cpu_info': {},
            'disk_info': {},
            'memory_info': {},
            'network_info': {},
            'other_info': {}
        }

        current_category = None
        current_command = None

        # 正则表达式匹配BBBP行
        # 匹配两种格式：
        # 1. BBBP,000,/etc/release
        # 2. BBBP,001,/etc/release,"Kylin Linux Advanced Server release V10 (Halberd)"
        bbbp_pattern = re.compile(r'^BBBP,\d+,([^,]*)(?:,"(.*)")?$')

        # 记录所有BBBP命令
        bbbp_commands = set()

        for line in lines:
            line = line.strip()
            if line.startswith('BBBP'):
                try:
                    match = bbbp_pattern.match(line)
                    if match:
                        command = match.group(1)
                        value = match.group(2) if match.group(2) else ""

                        # 记录解析结果
                        self.log(f"BBBP行解析: 命令={command}, 值={value if value else '[无值]'}")

                        # 如果是新命令，更新当前命令和分类
                        if not value:
                            current_command = command

                            # 记录命令
                            bbbp_commands.add(command)

                            # 根据命令确定分类
                            if '/etc/release' in command or 'lsb_release' in command or 'cat /etc/os-release' in command:
                                current_category = 'os_info'
                            elif 'lscpu' in command or '/proc/cpuinfo' in command or 'cat /proc/cpuinfo' in command:
                                current_category = 'cpu_info'
                            elif 'fdisk' in command or 'lsblk' in command or 'df' in command or 'mount' in command:
                                current_category = 'disk_info'
                            elif 'free' in command or 'vmstat' in command or '/proc/meminfo' in command or 'cat /proc/meminfo' in command:
                                current_category = 'memory_info'
                            elif 'ifconfig' in command or 'netstat' in command or 'ip addr' in command or 'route' in command:
                                current_category = 'network_info'
                            else:
                                current_category = 'other_info'

                            self.log(f"设置当前分类: {current_category}, 当前命令: {current_command}")
                        else:
                            # 如果有值，则存储到当前分类中
                            if current_category and current_command:
                                self.log(f"存储到分类 {current_category} 中")
                            else:
                                # 如果没有当前分类或命令，则使用默认分类
                                current_category = 'other_info'
                                current_command = command
                                self.log(f"使用默认分类: {current_category}, 命令: {current_command}")
                except Exception as e:
                    self.log(f"BBBP行解析出错: {e}, 行: {line}")
                    continue

                # 如果有值和当前分类/命令，则存储数据
                if match and value and current_category and current_command:
                    # 处理特殊情况
                    try:
                        if current_command == '/etc/release' and 'DISTRIB_ID' in value:
                            parts = value.split('=')
                            if len(parts) > 1:
                                self.system_info[current_category]['distrib_id'] = parts[1].strip('"')
                        elif current_command == '/etc/release' and 'DISTRIB_RELEASE' in value:
                            parts = value.split('=')
                            if len(parts) > 1:
                                self.system_info[current_category]['distrib_release'] = parts[1].strip('"')
                        elif current_command == '/etc/release' and 'PRETTY_NAME' in value:
                            parts = value.split('=')
                            if len(parts) > 1:
                                self.system_info[current_category]['pretty_name'] = parts[1].strip('"')
                        elif current_command == 'lscpu' and '型号名称' in value:
                            parts = value.split(':')
                            if len(parts) > 1:
                                self.system_info[current_category]['model_name'] = parts[1].strip()
                            else:
                                self.system_info[current_category]['model_name'] = value
                        elif current_command == 'lscpu' and 'CPU' in value and 'MHz' in value:
                            parts = value.split(':')
                            if len(parts) > 1:
                                self.system_info[current_category]['cpu_mhz'] = parts[1].strip()
                            else:
                                self.system_info[current_category]['cpu_mhz'] = value
                        elif current_command == 'lsblk' and 'NAME' in value:
                            # 跳过头部行
                            pass
                        elif current_command == 'lsblk' and ('disk' in value or 'part' in value or 'lvm' in value):
                            # 存储磁盘信息
                            parts = value.split()
                            if len(parts) >= 2:
                                disk_name = parts[0]
                                disk_info = ' '.join(parts[1:])
                                self.system_info[current_category][disk_name] = disk_info
                        else:
                            # 存储其他信息
                            key = f"{current_command}_{len(self.system_info[current_category])}"
                            self.system_info[current_category][key] = value
                    except Exception as e:
                        self.log(f"处理BBBP行时出错: {e}, 命令: {current_command}, 值: {value}")
                        # 存储原始值
                        key = f"{current_command}_{len(self.system_info[current_category])}"
                        self.system_info[current_category][key] = value

        # 记录解析结果
        self.log(f"BBBP命令总数: {len(bbbp_commands)}")
        self.log(f"BBBP命令列表: {sorted(bbbp_commands)}")

        # 记录各分类的条目数
        for category, items in self.system_info.items():
            self.log(f"系统信息分类 '{category}' 包含 {len(items)} 条目")

        # 处理特殊情况，如果没有操作系统信息，使用基本信息中的操作系统
        if not self.system_info['os_info'] and '操作系统' in self.system_info['basic_info']:
            self.system_info['os_info']['os_version'] = self.system_info['basic_info']['操作系统']

        self.log(f"解析BBBP行完成，获取到 {sum(len(category) for category in self.system_info.values())} 条系统信息")


if __name__ == "__main__":
    # 如果直接运行此脚本，则解析命令行参数中指定的nmon文件
    import sys

    if len(sys.argv) > 1:
        nmon_file = sys.argv[1]

        print(f"解析nmon文件: {nmon_file}")
        parser = NmonParser(nmon_file)
        result = parser.parse()

        # 打印解析结果摘要
        print(f"\n基本信息:")
        print(f"主机名: {result['hostname']}")
        print(f"操作系统: {result['os_version']}")
        print(f"日期: {result['date']}")
        print(f"时间: {result['time']}")
        print(f"采样间隔(秒): {result['interval_seconds']}")

        print(f"\n时间戳数量: {len(result['timestamps'])}")
        print(f"CPU数据数量: {len(result['cpu_data'])}")
        print(f"内存数据数量: {len(result['memory_data'])}")
        print(f"磁盘数据数量: {len(result['disk_data'])}")
        print(f"网络数据数量: {len(result['network_data'])}")

        print("\n解析完成")
    else:
        print("用法: python nmon_parser.py <nmon文件路径>")
