import logging
import sys
import os
import traceback
import shutil
from datetime import datetime, timedelta

# 内部日志记录器
logger = logging.getLogger('app')

# 日志配置
MAX_LOG_CACHE_SIZE = 1000  # 内存中最大缓存的日志条数
MAX_LOG_FILE_SIZE = 10 * 1024 * 1024  # 10MB，单个日志文件最大大小
MAX_LOG_FILES = 30  # 保留的最大日志文件数量

# 初始化日志记录器
def init_logger(log_file=None, log_level=None, log_dir=None):
    """
    初始化日志记录器

    参数:
        log_file: 日志文件路径，如果为None，则使用默认路径
        log_level: 日志级别，如果为None，则从设置中读取
        log_dir: 日志目录，如果为None，则从设置中读取或使用默认目录
    """
    global logger

    # 如果没有提供日志级别，从设置中读取
    if log_level is None:
        try:
            from PyQt5.QtCore import QSettings
            settings = QSettings("PerformanceAnalyzer", "Settings")
            log_level = settings.value("log_level", logging.INFO, type=int)
        except Exception:
            # 如果无法从设置中读取，使用默认级别
            log_level = logging.INFO

    # 如果没有提供日志目录，从设置中读取
    if log_dir is None:
        try:
            from PyQt5.QtCore import QSettings
            settings = QSettings("PerformanceAnalyzer", "Settings")
            log_dir = settings.value("log_dir", "logs", type=str)
        except Exception:
            # 如果无法从设置中读取，使用默认目录
            log_dir = "logs"

    # 确保日志目录存在
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir)
        except Exception as e:
            print(f"创建日志目录失败: {e}，将使用当前目录")
            log_dir = "."

    # 如果没有提供日志文件路径，使用默认路径
    if log_file is None:
        # 创建日志文件名（包含日期和时间）
        log_file = os.path.join(log_dir, f"app_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    # 清理旧日志文件
    clean_old_logs(log_dir)

    # 配置日志格式
    log_format = "%(asctime)s [%(levelname)s] %(message)s"
    date_format = "%Y-%m-%d %H:%M:%S"
    formatter = logging.Formatter(log_format, date_format)

    # 清除现有处理程序
    if logger.handlers:
        for handler in logger.handlers:
            logger.removeHandler(handler)

    # 添加文件处理程序，使用RotatingFileHandler来限制文件大小
    try:
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=MAX_LOG_FILE_SIZE,
            backupCount=5,  # 保留5个备份文件
            encoding='utf-8'
        )
    except Exception:
        # 如果RotatingFileHandler不可用，回退到普通FileHandler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')

    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # 设置日志级别
    logger.setLevel(log_level)

    # 记录日志初始化信息
    level_name = logging.getLevelName(log_level)
    logger.info(f"日志系统初始化成功，日志文件: {log_file}，日志级别: {level_name}")

    return log_file

def clean_old_logs(log_dir):
    """
    清理旧的日志文件，保留最近的MAX_LOG_FILES个文件

    参数:
        log_dir: 日志目录
    """
    try:
        # 获取目录中的所有日志文件
        log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]

        # 如果文件数量超过限制
        if len(log_files) > MAX_LOG_FILES:
            # 按修改时间排序
            log_files = [(os.path.join(log_dir, f), os.path.getmtime(os.path.join(log_dir, f)))
                         for f in log_files]
            log_files.sort(key=lambda x: x[1])  # 按时间排序

            # 删除最旧的文件，直到数量符合限制
            for file_path, _ in log_files[:(len(log_files) - MAX_LOG_FILES)]:
                try:
                    os.remove(file_path)
                    print(f"已删除旧日志文件: {file_path}")
                except Exception as e:
                    print(f"删除日志文件失败: {e}")
    except Exception as e:
        print(f"清理旧日志文件时出错: {e}")

# 内存中的日志缓存，用于GUI显示
log_messages = []

def log(message, level="INFO"):
    """
    记录日志消息

    参数:
        message: 日志消息
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    # 添加到内存缓存，并限制缓存大小
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_messages.append((timestamp, message, level))

    # 如果缓存超过最大限制，删除旧的日志
    if len(log_messages) > MAX_LOG_CACHE_SIZE:
        # 删除最旧的20%的日志
        del log_messages[:int(MAX_LOG_CACHE_SIZE * 0.2)]

    # 确保日志记录器已初始化
    if not logger.handlers:
        init_logger(None)

    # 记录到日志文件
    try:
        if level == "DEBUG":
            logger.debug(message)
        elif level == "INFO":
            logger.info(message)
        elif level == "WARNING":
            logger.warning(message)
        elif level == "ERROR":
            logger.error(message)
        elif level == "CRITICAL":
            logger.critical(message)
        else:
            logger.info(message)
    except Exception as e:
        # 如果日志记录失败，不输出到控制台
        # 仅在内存中记录日志记录失败的信息
        log_messages.append((timestamp, f"日志记录失败: {e}", "ERROR"))

    return f"[{timestamp}] {message}"

def get_logs():
    """获取内存中的日志消息"""
    return log_messages.copy()

def clear_logs():
    """清空内存中的日志消息"""
    global log_messages
    log_messages = []

# 清理日志文件
def clean_logs(days=30, log_dir=None):
    """
    清理旧的日志文件

    参数:
        days: 保留最近多少天的日志
        log_dir: 日志目录，如果为None，则从设置中读取
    """
    # 如果没有提供日志目录，从设置中读取
    if log_dir is None:
        try:
            from PyQt5.QtCore import QSettings
            settings = QSettings("PerformanceAnalyzer", "Settings")
            log_dir = settings.value("log_dir", "logs", type=str)
        except Exception:
            # 如果无法从设置中读取，使用默认目录
            log_dir = "logs"

    if not os.path.exists(log_dir):
        return

    try:
        # 计算截止日期
        cutoff_date = datetime.now() - timedelta(days=days)
        cutoff_timestamp = cutoff_date.timestamp()

        # 获取目录中的所有日志文件
        log_files = [os.path.join(log_dir, f) for f in os.listdir(log_dir) if f.endswith('.log')]

        # 删除超过指定天数的日志文件
        for log_file in log_files:
            try:
                file_mtime = os.path.getmtime(log_file)
                if file_mtime < cutoff_timestamp:
                    os.remove(log_file)
                    log(f"已删除旧日志文件: {log_file}", "INFO")
            except Exception as e:
                log(f"删除日志文件失败: {log_file}, 错误: {e}", "ERROR")
    except Exception as e:
        log(f"清理日志文件时出错: {e}", "ERROR")

def log_exception(e, context=""):
    """
    记录异常信息

    参数:
        e: 异常对象
        context: 上下文信息
    """
    error_message = f"{context}错误: {str(e)}"
    error_traceback = traceback.format_exc()

    # 确保日志记录器已初始化
    if not logger.handlers:
        init_logger(None)

    # 记录到日志文件
    try:
        logger.error(error_message)
        logger.error(f"异常详情: {error_traceback}")
    except Exception as log_error:
        # 如果日志记录失败，不输出到控制台
        # 仅在内存中记录日志记录失败的信息
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_messages.append((timestamp, f"日志记录异常失败: {log_error}", "ERROR"))

    # 添加到内存缓存
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_messages.append((timestamp, error_message, "ERROR"))
    log_messages.append((timestamp, f"异常详情: {error_traceback}", "ERROR"))

    return error_message, error_traceback
