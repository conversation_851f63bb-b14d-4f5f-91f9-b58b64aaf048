import logging
import os
import traceback
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler

# 日志配置
class LogConfig:
    """日志配置类"""
    MAX_LOG_CACHE_SIZE = 500  # 减少内存缓存大小
    MAX_LOG_FILE_SIZE = 5 * 1024 * 1024  # 5MB，减少单个日志文件大小
    MAX_LOG_FILES = 10  # 减少保留的日志文件数量
    DEFAULT_LOG_DIR = "logs"
    DEFAULT_LOG_LEVEL = logging.INFO

# 全局日志记录器
_logger = None
_log_file_path = None

def get_settings():
    """安全获取设置"""
    try:
        from PyQt5.QtCore import QSettings
        return QSettings("PerformanceAnalyzer", "Settings")
    except ImportError:
        return None

def init_logger(log_level=None, log_dir=None):
    """
    简化的日志记录器初始化

    参数:
        log_level: 日志级别
        log_dir: 日志目录

    返回:
        str: 日志文件路径
    """
    global _logger, _log_file_path

    # 获取配置
    settings = get_settings()

    if log_level is None:
        log_level = settings.value("log_level", LogConfig.DEFAULT_LOG_LEVEL, type=int) if settings else LogConfig.DEFAULT_LOG_LEVEL

    if log_dir is None:
        log_dir = settings.value("log_dir", LogConfig.DEFAULT_LOG_DIR, type=str) if settings else LogConfig.DEFAULT_LOG_DIR

    # 确保日志目录存在
    os.makedirs(log_dir, exist_ok=True)

    # 生成日志文件路径
    timestamp = datetime.now().strftime('%Y%m%d')
    _log_file_path = os.path.join(log_dir, f"app_{timestamp}.log")

    # 创建日志记录器
    _logger = logging.getLogger('PerformanceAnalyzer')
    _logger.setLevel(log_level)

    # 清除现有处理程序
    _logger.handlers.clear()

    # 配置文件处理程序
    file_handler = RotatingFileHandler(
        _log_file_path,
        maxBytes=LogConfig.MAX_LOG_FILE_SIZE,
        backupCount=3,
        encoding='utf-8'
    )

    # 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(formatter)
    _logger.addHandler(file_handler)

    # 清理旧日志
    _cleanup_old_logs(log_dir)

    # 记录初始化信息
    _logger.info(f"日志系统初始化完成 - 文件: {_log_file_path}, 级别: {logging.getLevelName(log_level)}")

    return _log_file_path

def _cleanup_old_logs(log_dir):
    """清理旧日志文件"""
    try:
        log_files = [f for f in os.listdir(log_dir) if f.startswith('app_') and f.endswith('.log')]
        if len(log_files) > LogConfig.MAX_LOG_FILES:
            # 按修改时间排序，删除最旧的文件
            log_files.sort(key=lambda f: os.path.getmtime(os.path.join(log_dir, f)))
            for old_file in log_files[:-LogConfig.MAX_LOG_FILES]:
                os.remove(os.path.join(log_dir, old_file))
    except Exception:
        pass  # 静默处理清理错误



# 内存日志缓存
_log_cache = []

def _ensure_logger():
    """确保日志记录器已初始化"""
    global _logger
    if _logger is None:
        init_logger()

def log(message, level="INFO"):
    """
    简化的日志记录函数

    参数:
        message: 日志消息
        level: 日志级别
    """
    _ensure_logger()

    # 添加到内存缓存
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    _log_cache.append((timestamp, message, level))

    # 限制缓存大小
    if len(_log_cache) > LogConfig.MAX_LOG_CACHE_SIZE:
        _log_cache.pop(0)  # 移除最旧的日志

    # 记录到文件
    level_map = {
        "DEBUG": _logger.debug,
        "INFO": _logger.info,
        "WARNING": _logger.warning,
        "ERROR": _logger.error,
        "CRITICAL": _logger.critical
    }

    log_func = level_map.get(level.upper(), _logger.info)
    try:
        log_func(message)
    except Exception:
        pass  # 静默处理日志记录错误

    return f"[{timestamp}] {message}"

def get_logs():
    """获取内存中的日志消息"""
    return _log_cache.copy()

def clear_logs():
    """清空内存中的日志消息"""
    global _log_cache
    _log_cache.clear()

def clean_logs(days=30, log_dir=None):
    """
    清理旧的日志文件

    参数:
        days: 保留最近多少天的日志
        log_dir: 日志目录
    """
    settings = get_settings()
    if log_dir is None:
        log_dir = settings.value("log_dir", LogConfig.DEFAULT_LOG_DIR, type=str) if settings else LogConfig.DEFAULT_LOG_DIR

    if not os.path.exists(log_dir):
        return

    try:
        cutoff_date = datetime.now() - timedelta(days=days)
        cutoff_timestamp = cutoff_date.timestamp()

        for filename in os.listdir(log_dir):
            if filename.endswith('.log'):
                file_path = os.path.join(log_dir, filename)
                try:
                    if os.path.getmtime(file_path) < cutoff_timestamp:
                        os.remove(file_path)
                        log(f"已删除旧日志文件: {filename}", "INFO")
                except Exception as e:
                    log(f"删除日志文件失败: {filename}, 错误: {e}", "ERROR")
    except Exception as e:
        log(f"清理日志文件时出错: {e}", "ERROR")

def log_exception(e, context=""):
    """
    记录异常信息

    参数:
        e: 异常对象
        context: 上下文信息
    """
    _ensure_logger()

    error_message = f"{context}错误: {str(e)}"
    error_traceback = traceback.format_exc()

    # 记录错误信息
    log(error_message, "ERROR")
    log(f"异常详情: {error_traceback}", "ERROR")

    return error_message, error_traceback
