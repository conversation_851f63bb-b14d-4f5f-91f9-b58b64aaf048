"""
Excel生成器

这个模块用于将nmon解析结果生成Excel文件。
"""

import os
import pandas as pd
from datetime import datetime


class ExcelGenerator:
    """Excel生成器类。"""

    def __init__(self, data):
        """
        初始化Excel生成器。

        参数:
            data: nmon解析结果数据
        """
        self.data = data

    def generate(self, output_file):
        """
        生成Excel文件。

        参数:
            output_file: 输出文件路径

        返回:
            str: 输出文件路径
        """
        # 创建Excel写入器
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 创建摘要工作表
            self._create_summary_sheet(writer)

            # 创建CPU工作表
            if 'cpu_details' in self.data:
                self._create_cpu_sheet(writer)

            # 创建内存工作表
            if 'memory_details' in self.data:
                self._create_memory_sheet(writer)

            # 创建磁盘工作表
            if 'disk_details' in self.data:
                self._create_disk_sheet(writer)

            # 创建网络工作表
            if 'network_details' in self.data:
                self._create_network_sheet(writer)

            # 创建系统信息工作表
            if 'system_details' in self.data:
                self._create_system_sheet(writer)

        return output_file

    def _create_summary_sheet(self, writer):
        """创建摘要工作表。"""
        # 创建摘要数据
        summary_data = {
            '类别': [],
            '指标': [],
            '值': [],
            '单位': []
        }

        # 添加系统详情
        if 'system_details' in self.data:
            system_details = self.data['system_details']
            for key, value in system_details.items():
                summary_data['类别'].append('系统')
                summary_data['指标'].append(key)
                summary_data['值'].append(value)
                summary_data['单位'].append('')

        # 添加CPU详情
        if 'cpu_details' in self.data:
            cpu_details = self.data['cpu_details']
            for key, value in cpu_details.items():
                summary_data['类别'].append('CPU')
                summary_data['指标'].append(key)
                summary_data['值'].append(value)
                summary_data['单位'].append('%')

        # 添加内存详情
        if 'memory_details' in self.data:
            memory_details = self.data['memory_details']
            for key, value in memory_details.items():
                summary_data['类别'].append('内存')
                summary_data['指标'].append(key)
                unit = '%' if '使用率' in key else 'MB'
                summary_data['值'].append(value)
                summary_data['单位'].append(unit)

        # 添加磁盘详情
        if 'disk_details' in self.data:
            disk_details = self.data['disk_details']
            for key, value in disk_details.items():
                summary_data['类别'].append('磁盘')
                summary_data['指标'].append(key)
                unit = 'KB/s' if '速率' in key else '%'
                summary_data['值'].append(value)
                summary_data['单位'].append(unit)

        # 添加网络详情
        if 'network_details' in self.data:
            network_details = self.data['network_details']
            for key, value in network_details.items():
                summary_data['类别'].append('网络')
                summary_data['指标'].append(key)
                summary_data['值'].append(value)
                summary_data['单位'].append('KB/s')

        # 我们已经在开头添加了系统信息，这里不需要重复添加

        # 创建DataFrame并写入Excel
        df = pd.DataFrame(summary_data)
        df.to_excel(writer, sheet_name='摘要', index=False)

    def _create_cpu_sheet(self, writer):
        """创建CPU工作表。"""
        cpu_data = {
            '指标': [],
            '值': [],
            '单位': []
        }

        # 添加CPU详细信息
        for key, value in self.data['cpu_details'].items():
            cpu_data['指标'].append(key)
            cpu_data['值'].append(value)
            cpu_data['单位'].append('%')

        # 创建DataFrame并写入Excel
        df = pd.DataFrame(cpu_data)
        df.to_excel(writer, sheet_name='CPU', index=False)

    def _create_memory_sheet(self, writer):
        """创建内存工作表。"""
        memory_data = {
            '指标': [],
            '值': [],
            '单位': []
        }

        # 添加内存详细信息
        for key, value in self.data['memory_details'].items():
            memory_data['指标'].append(key)
            memory_data['值'].append(value)
            unit = '%' if '使用率' in key else 'MB'
            memory_data['单位'].append(unit)

        # 创建DataFrame并写入Excel
        df = pd.DataFrame(memory_data)
        df.to_excel(writer, sheet_name='内存', index=False)

    def _create_disk_sheet(self, writer):
        """创建磁盘工作表。"""
        disk_data = {
            '指标': [],
            '值': [],
            '单位': []
        }

        # 添加磁盘详细信息
        for key, value in self.data['disk_details'].items():
            disk_data['指标'].append(key)
            disk_data['值'].append(value)
            unit = 'KB/s' if '速率' in key else '%'
            disk_data['单位'].append(unit)

        # 创建DataFrame并写入Excel
        df = pd.DataFrame(disk_data)
        df.to_excel(writer, sheet_name='磁盘', index=False)

    def _create_network_sheet(self, writer):
        """创建网络工作表。"""
        network_data = {
            '指标': [],
            '值': [],
            '单位': []
        }

        # 添加网络详细信息
        for key, value in self.data['network_details'].items():
            network_data['指标'].append(key)
            network_data['值'].append(value)
            network_data['单位'].append('KB/s')

        # 创建DataFrame并写入Excel
        df = pd.DataFrame(network_data)
        df.to_excel(writer, sheet_name='网络', index=False)

    def _create_system_sheet(self, writer):
        """创建系统信息工作表。"""
        system_data = {
            '指标': [],
            '值': []
        }

        # 添加系统详细信息
        for key, value in self.data['system_details'].items():
            system_data['指标'].append(key)
            system_data['值'].append(value)

        # 创建DataFrame并写入Excel
        df = pd.DataFrame(system_data)
        df.to_excel(writer, sheet_name='系统信息', index=False)
