=== 性能监控数据分析器日志 ===

Checking for required fonts...
2025-05-06 10:01:05 [ERROR] 注册中文字体时出错: 'FontManager' object has no attribute '_find_fonts_by_path'
2025-05-06 10:01:05 [ERROR] 注册英文字体时出错: 'FontManager' object has no attribute '_find_fonts_by_path'
2025-05-06 10:01:05 [WARNING] 没有可用的字体，回退到系统字体
2025-05-06 10:01:37 [CRITICAL] 系统退出函数被调用
2025-05-06 10:01:37 [CRITICAL] 退出时的调用堆栈:   File "H:\PyWorkspace\NewWork\main.py", line 227, in handle_exit
    stack = traceback.format_stack()

2025-05-06 10:01:37 [CRITICAL] 退出时的活动线程数: 2
2025-05-06 10:01:37 [CRITICAL] 线程: MainThread, 活动: False, 守护: False
2025-05-06 10:01:37 [CRITICAL] 线程: WatchdogThread, 活动: True, 守护: True
2025-05-06 15:18:55 [CRITICAL] 系统退出函数被调用
2025-05-06 15:18:55 [CRITICAL] 退出时的调用堆栈:   File "main.py", line 227, in handle_exit
    stack = traceback.format_stack()

2025-05-06 15:18:55 [CRITICAL] 退出时的活动线程数: 2
2025-05-06 15:18:55 [CRITICAL] 线程: MainThread, 活动: False, 守护: False
2025-05-06 15:18:55 [CRITICAL] 线程: WatchdogThread, 活动: True, 守护: True
2025-05-06 15:22:00 [ERROR] 注册中文字体时出错: 'FontManager' object has no attribute '_find_fonts_by_path'
2025-05-06 15:22:00 [ERROR] 注册英文字体时出错: 'FontManager' object has no attribute '_find_fonts_by_path'
2025-05-06 15:22:00 [WARNING] 没有可用的字体，回退到系统字体
