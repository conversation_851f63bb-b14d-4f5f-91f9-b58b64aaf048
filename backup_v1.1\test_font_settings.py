import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import numpy as np
from ensure_fonts import (get_available_fonts, load_font_settings, 
                         save_font_settings, FONT_INFO, get_font_dir)

def test_font_settings():
    """Test the font settings functionality"""
    # Get available fonts
    available_fonts = get_available_fonts()
    print(f"Available fonts: {len(available_fonts)}")
    for font_key, font_info in available_fonts.items():
        print(f"  - {font_key}: {font_info['display_name']} ({font_info['type']})")
    
    # Get current font settings
    chinese_font, english_font = load_font_settings()
    print(f"\nCurrent font settings:")
    print(f"  Chinese font: {chinese_font} - {FONT_INFO[chinese_font]['display_name']}")
    print(f"  English font: {english_font} - {FONT_INFO[english_font]['display_name']}")
    
    # Create a test plot with current settings
    create_test_plot("current_settings.png", chinese_font, english_font)
    
    # Try changing the font settings
    if len(available_fonts) >= 2:
        # Find a different Chinese font
        chinese_fonts = [k for k, v in available_fonts.items() if v['type'] == 'chinese' and k != chinese_font]
        english_fonts = [k for k, v in available_fonts.items() if v['type'] == 'english' and k != english_font]
        
        if chinese_fonts:
            new_chinese_font = chinese_fonts[0]
            print(f"\nChanging Chinese font to: {new_chinese_font} - {FONT_INFO[new_chinese_font]['display_name']}")
            save_font_settings(new_chinese_font, english_font)
            create_test_plot("new_chinese_font.png", new_chinese_font, english_font)
        
        if english_fonts:
            new_english_font = english_fonts[0]
            print(f"\nChanging English font to: {new_english_font} - {FONT_INFO[new_english_font]['display_name']}")
            save_font_settings(chinese_font, new_english_font)
            create_test_plot("new_english_font.png", chinese_font, new_english_font)
        
        # Restore original settings
        save_font_settings(chinese_font, english_font)
        print(f"\nRestored original font settings")
    else:
        print("\nNot enough fonts available to test changing settings")

def create_test_plot(filename, chinese_font_key, english_font_key):
    """Create a test plot with the specified fonts"""
    # Get font paths
    font_dir = get_font_dir()
    chinese_font_path = os.path.join(font_dir, FONT_INFO[chinese_font_key]['filename'])
    english_font_path = os.path.join(font_dir, FONT_INFO[english_font_key]['filename'])
    
    # Create figure
    plt.figure(figsize=(10, 6))
    
    # Generate data
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    
    # Plot data
    plt.plot(x, y1, label='正弦波 (Sine Wave)')
    plt.plot(x, y2, label='余弦波 (Cosine Wave)')
    
    # Set Chinese font for title
    chinese_font_prop = fm.FontProperties(fname=chinese_font_path)
    plt.title('中文和英文字体测试 / Chinese and English Font Test', fontproperties=chinese_font_prop)
    
    # Set Chinese font for labels
    plt.xlabel('时间 (秒) / Time (seconds)', fontproperties=chinese_font_prop)
    plt.ylabel('振幅 / Amplitude', fontproperties=chinese_font_prop)
    
    # Set English font for legend
    legend = plt.legend()
    for text in legend.get_texts():
        text.set_fontproperties(chinese_font_prop)
    
    # Add grid
    plt.grid(True)
    
    # Save figure
    plt.savefig(filename, bbox_inches='tight')
    plt.close()
    
    print(f"Test plot saved to {filename}")

if __name__ == "__main__":
    test_font_settings()
