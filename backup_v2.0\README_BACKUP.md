# 性能监控数据分析器 V2.0 - 源代码备份

这是性能监控数据分析器 V2.0 版本的完整源代码备份。

## 备份内容

此备份包含以下内容：

1. 所有Python源代码文件
2. 内嵌字体文件（fonts目录）
3. 应用图标文件（icons目录）
4. 配置和构建文件
5. 版本信息文件

## 版本特性

V2.0版本包含以下主要特性和改进：

- 统一的界面设计，提供一致的用户体验
- 优化的性能监控数据分析功能
- 改进的nmon文件分析器
- 增强的原始nmon文件分析器
- 优化的图表生成和显示
- 改进的保存选项和结果导出
- 更好的错误处理和日志记录

## 恢复备份

如需从此备份恢复源代码，只需将所有文件复制到一个新的工作目录即可。

## 构建可执行文件

要从源代码构建可执行文件，请运行：

```
python -m PyInstaller performance_analyzer.spec
```

构建完成后，可执行文件将位于`dist/性能监控数据分析器_V2.0`目录中。

## 备份日期

备份日期：2025-05-06
