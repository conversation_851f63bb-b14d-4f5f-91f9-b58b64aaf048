"""
Nmon文件分析器GUI

这个模块提供了一个图形用户界面，用于分析nmon文件并生成报告。
"""

import os
import sys
import threading
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QPushButton, QLabel, QFileDialog, QProgressBar, QTextEdit, 
                            QMessageBox, QGroupBox, QDesktopWidget, QComboBox)
from PyQt5.QtCore import Qt, pyqtSignal, QObject, QThread
from PyQt5.QtGui import QDesktopServices
from PyQt5.QtCore import QUrl

from nmon_parser import NmonParser


class AnalysisThread(QThread):
    """分析线程类。"""
    
    progress_signal = pyqtSignal(int)
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    
    def __init__(self, nmon_file_path, output_dir=None):
        """
        初始化分析线程。
        
        参数:
            nmon_file_path: nmon文件路径
            output_dir: 输出目录，默认为nmon文件所在目录
        """
        super().__init__()
        self.nmon_file_path = nmon_file_path
        self.output_dir = output_dir or os.path.dirname(nmon_file_path)
        
    def run(self):
        """运行分析线程。"""
        try:
            self.log_signal.emit(f"开始分析nmon文件: {os.path.basename(self.nmon_file_path)}")
            
            # 创建解析器
            parser = NmonParser(self.nmon_file_path)
            
            # 设置进度回调
            def progress_callback(percent):
                self.progress_signal.emit(percent)
                
            # 设置日志回调
            def log_callback(message):
                self.log_signal.emit(message)
            
            # 设置回调函数
            parser.set_callbacks(progress_callback, log_callback)
            
            # 解析nmon文件
            self.progress_signal.emit(10)
            result = parser.parse()
            
            # 提取时间序列数据
            self.progress_signal.emit(40)
            self.log_signal.emit("提取时间序列数据...")
            
            # 生成Excel报告
            self.progress_signal.emit(70)
            self.log_signal.emit("生成Excel报告...")
            
            import pandas as pd
            
            # 提取时间序列数据
            timestamps = [ts_info['timestamp'] for ts_id, ts_info in sorted(result['timestamps'].items())]
            
            # 创建数据帧
            dfs = {}
            
            # CPU数据
            if result['cpu_data']:
                cpu_data = {
                    '时间戳': timestamps,
                    'CPU使用率(%)': [data['busy'] for _, data in sorted(result['cpu_data'].items())],
                    'CPU用户态(%)': [data['user'] for _, data in sorted(result['cpu_data'].items())],
                    'CPU系统态(%)': [data['sys'] for _, data in sorted(result['cpu_data'].items())],
                    'CPU等待IO(%)': [data['wait'] for _, data in sorted(result['cpu_data'].items())],
                    'CPU空闲(%)': [data['idle'] for _, data in sorted(result['cpu_data'].items())]
                }
                dfs['CPU'] = pd.DataFrame(cpu_data)
            
            # 内存数据
            if result['memory_data']:
                memory_data = {
                    '时间戳': timestamps,
                    '内存总量(MB)': [data['total'] for _, data in sorted(result['memory_data'].items())],
                    '内存空闲(MB)': [data['free'] for _, data in sorted(result['memory_data'].items())],
                    '内存使用率(%)': [((data['total'] - data['free']) / data['total'] * 100) if data['total'] > 0 else 0 
                                for _, data in sorted(result['memory_data'].items())]
                }
                dfs['内存'] = pd.DataFrame(memory_data)
            
            # 磁盘数据
            disk_names = set()
            for _, disks in result['disk_data'].items():
                disk_names.update(disks.keys())
            
            for disk_name in disk_names:
                disk_data = {
                    '时间戳': timestamps,
                    f'使用率(%)': [],
                    f'读取(KB/s)': [],
                    f'写入(KB/s)': []
                }
                
                for ts_id, _ in sorted(result['timestamps'].items()):
                    if ts_id in result['disk_data'] and disk_name in result['disk_data'][ts_id]:
                        data = result['disk_data'][ts_id][disk_name]
                        disk_data[f'使用率(%)'].append(data['busy'])
                        disk_data[f'读取(KB/s)'].append(data['read'])
                        disk_data[f'写入(KB/s)'].append(data['write'])
                    else:
                        disk_data[f'使用率(%)'].append(0)
                        disk_data[f'读取(KB/s)'].append(0)
                        disk_data[f'写入(KB/s)'].append(0)
                        
                dfs[f'磁盘_{disk_name}'] = pd.DataFrame(disk_data)
            
            # 网络数据
            interface_names = set()
            for _, interfaces in result['network_data'].items():
                interface_names.update(interfaces.keys())
            
            for interface_name in interface_names:
                net_data = {
                    '时间戳': timestamps,
                    f'读取(KB/s)': [],
                    f'写入(KB/s)': []
                }
                
                for ts_id, _ in sorted(result['timestamps'].items()):
                    if ts_id in result['network_data'] and interface_name in result['network_data'][ts_id]:
                        data = result['network_data'][ts_id][interface_name]
                        net_data[f'读取(KB/s)'].append(data['read'])
                        net_data[f'写入(KB/s)'].append(data['write'])
                    else:
                        net_data[f'读取(KB/s)'].append(0)
                        net_data[f'写入(KB/s)'].append(0)
                        
                dfs[f'网络_{interface_name}'] = pd.DataFrame(net_data)
            
            # 写入Excel文件
            output_file = os.path.join(
                self.output_dir, 
                os.path.splitext(os.path.basename(self.nmon_file_path))[0] + "_analyzed.xlsx"
            )
            
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                for sheet_name, df in dfs.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            self.progress_signal.emit(100)
            self.log_signal.emit(f"nmon文件分析完成，结果已保存到: {output_file}")
            
            # 发送完成信号
            self.finished_signal.emit({
                'output_file': output_file,
                'result': result
            })
            
        except Exception as e:
            self.error_signal.emit(f"分析nmon文件时出错: {str(e)}")
            import traceback
            self.log_signal.emit(traceback.format_exc())


class NmonAnalyzerApp(QMainWindow):
    """Nmon文件分析器应用类。"""
    
    def __init__(self):
        """初始化应用。"""
        super().__init__()
        
        self.nmon_file_path = ""
        self.output_dir = ""
        self.analysis_thread = None
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面。"""
        # 设置窗口标题和大小
        self.setWindowTitle("Nmon文件分析器")
        self.resize(800, 600)
        
        # 居中显示
        self.center()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建文件选择组
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout()
        file_group.setLayout(file_layout)
        
        # 创建nmon文件选择部件
        nmon_layout = QHBoxLayout()
        self.nmon_label = QLabel("Nmon文件:")
        self.nmon_path_label = QLabel("未选择")
        self.nmon_browse_button = QPushButton("浏览...")
        self.nmon_browse_button.clicked.connect(self.browse_nmon_file)
        
        nmon_layout.addWidget(self.nmon_label)
        nmon_layout.addWidget(self.nmon_path_label, 1)
        nmon_layout.addWidget(self.nmon_browse_button)
        
        # 创建输出目录选择部件
        output_layout = QHBoxLayout()
        self.output_label = QLabel("输出目录:")
        self.output_path_label = QLabel("与nmon文件相同")
        self.output_browse_button = QPushButton("浏览...")
        self.output_browse_button.clicked.connect(self.browse_output_dir)
        
        output_layout.addWidget(self.output_label)
        output_layout.addWidget(self.output_path_label, 1)
        output_layout.addWidget(self.output_browse_button)
        
        # 添加到文件选择组
        file_layout.addLayout(nmon_layout)
        file_layout.addLayout(output_layout)
        
        # 创建分析选项组
        options_group = QGroupBox("分析选项")
        options_layout = QVBoxLayout()
        options_group.setLayout(options_layout)
        
        # 创建分析类型选择部件
        analysis_type_layout = QHBoxLayout()
        self.analysis_type_label = QLabel("分析类型:")
        self.analysis_type_combo = QComboBox()
        self.analysis_type_combo.addItem("全面分析")
        self.analysis_type_combo.addItem("CPU分析")
        self.analysis_type_combo.addItem("内存分析")
        self.analysis_type_combo.addItem("磁盘分析")
        self.analysis_type_combo.addItem("网络分析")
        
        analysis_type_layout.addWidget(self.analysis_type_label)
        analysis_type_layout.addWidget(self.analysis_type_combo, 1)
        
        # 添加到分析选项组
        options_layout.addLayout(analysis_type_layout)
        
        # 创建操作按钮
        button_layout = QHBoxLayout()
        self.analyze_button = QPushButton("分析")
        self.analyze_button.clicked.connect(self.analyze_nmon_file)
        self.analyze_button.setEnabled(False)
        
        button_layout.addStretch()
        button_layout.addWidget(self.analyze_button)
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        
        # 创建日志文本框
        log_group = QGroupBox("日志")
        log_layout = QVBoxLayout()
        log_group.setLayout(log_layout)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        # 添加到主布局
        main_layout.addWidget(file_group)
        main_layout.addWidget(options_group)
        main_layout.addLayout(button_layout)
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(log_group)
        
        # 添加初始日志
        self.log_message("欢迎使用Nmon文件分析器")
        self.log_message("请选择一个nmon文件进行分析")
        
    def center(self):
        """将窗口居中显示。"""
        qr = self.frameGeometry()
        cp = QDesktopWidget().availableGeometry().center()
        qr.moveCenter(cp)
        self.move(qr.topLeft())
        
    def browse_nmon_file(self):
        """浏览并选择nmon文件。"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Nmon文件", "", "Nmon文件 (*.nmon);;所有文件 (*)"
        )
        
        if file_path:
            self.nmon_file_path = file_path
            self.nmon_path_label.setText(os.path.basename(file_path))
            self.analyze_button.setEnabled(True)
            self.log_message(f"已选择nmon文件: {os.path.basename(file_path)}")
            
    def browse_output_dir(self):
        """浏览并选择输出目录。"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择输出目录", ""
        )
        
        if dir_path:
            self.output_dir = dir_path
            self.output_path_label.setText(dir_path)
            self.log_message(f"已选择输出目录: {dir_path}")
        
    def analyze_nmon_file(self):
        """分析nmon文件。"""
        if not self.nmon_file_path:
            QMessageBox.warning(self, "警告", "请先选择nmon文件")
            return
            
        # 禁用分析按钮
        self.analyze_button.setEnabled(False)
        
        # 重置进度条
        self.progress_bar.setValue(0)
        
        # 创建并启动分析线程
        self.analysis_thread = AnalysisThread(self.nmon_file_path, self.output_dir)
        self.analysis_thread.progress_signal.connect(self.update_progress)
        self.analysis_thread.log_signal.connect(self.log_message)
        self.analysis_thread.finished_signal.connect(self.analysis_finished)
        self.analysis_thread.error_signal.connect(self.analysis_error)
        self.analysis_thread.start()
        
    def update_progress(self, value):
        """更新进度条。"""
        self.progress_bar.setValue(value)
        
    def log_message(self, message):
        """添加日志消息。"""
        self.log_text.append(message)
        
    def analysis_finished(self, result):
        """分析完成处理。"""
        # 启用分析按钮
        self.analyze_button.setEnabled(True)
        
        # 显示完成消息
        QMessageBox.information(
            self, 
            "分析完成", 
            f"nmon文件分析完成，结果已保存到:\n{result['output_file']}"
        )
        
        # 询问是否打开结果文件
        reply = QMessageBox.question(
            self,
            "打开结果",
            "是否打开分析结果文件？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.Yes:
            # 打开结果文件
            QDesktopServices.openUrl(QUrl.fromLocalFile(result['output_file']))
        
    def analysis_error(self, error_message):
        """分析错误处理。"""
        # 启用分析按钮
        self.analyze_button.setEnabled(True)
        
        # 显示错误消息
        QMessageBox.critical(self, "分析错误", error_message)


def main():
    """主函数。"""
    app = QApplication(sys.argv)
    window = NmonAnalyzerApp()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
