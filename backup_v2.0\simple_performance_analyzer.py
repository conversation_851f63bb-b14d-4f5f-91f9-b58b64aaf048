"""
简化版性能监控数据分析器启动脚本

这个脚本用于直接启动性能监控数据分析器GUI应用，使用简化的方式避免布局问题。
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import QSettings
from logger_utils import init_logger

class SimpleMainWindow(QMainWindow):
    """简化版主窗口，用于启动性能监控数据分析器。"""
    
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("性能监控数据分析器")
        self.setMinimumSize(800, 600)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        label = QLabel("正在加载性能监控数据分析器...")
        layout.addWidget(label)
        
        # 延迟加载真正的GUI
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(100, self.load_real_gui)
    
    def load_real_gui(self):
        """加载真正的GUI"""
        try:
            from gui import MainWindow
            self.real_window = MainWindow()
            self.real_window.show()
            self.close()
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"加载性能监控数据分析器时出错: {str(e)}")
            print(f"错误: {str(e)}")

def main():
    """主函数。"""
    # 初始化日志
    settings = QSettings("PerformanceAnalyzer", "Settings")
    log_level = settings.value("log_level", logging.INFO, type=int)
    init_logger(log_level=log_level)
    
    app = QApplication(sys.argv)
    window = SimpleMainWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
