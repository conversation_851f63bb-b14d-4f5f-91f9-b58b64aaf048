"""
测试nmon时间戳解析功能

这个脚本用于测试nmon_processor.py对ZZZZ行时间戳的解析功能。
"""

import os
import sys
import argparse
from nmon_processor import NmonProcessor

def test_timestamp_parsing(nmon_file_path):
    """测试时间戳解析功能。"""
    print(f"测试nmon文件时间戳解析: {nmon_file_path}")
    
    # 创建处理器
    processor = NmonProcessor(nmon_file_path, 30, 0)
    
    # 读取nmon文件
    processor._read_nmon_file()
    
    # 打印基本信息
    print("\n基本信息:")
    print(f"主机名: {processor.hostname}")
    print(f"操作系统: {processor.os_version}")
    print(f"日期: {processor.date}")
    print(f"时间: {processor.time}")
    print(f"采样间隔(秒): {processor.interval_seconds}")
    
    # 打印时间戳信息
    print(f"\n时间戳数量: {processor.timestamp_count}")
    print("\n时间戳信息:")
    print("序号\tT序号\t\t时间\t\t日期\t\t完整时间戳")
    
    for idx, (ts_idx, ts_info) in enumerate(sorted(processor.timestamps.items())):
        print(f"{idx}\t{ts_info['t_index']}\t{ts_info['time']}\t{ts_info['date']}\t{ts_info['timestamp']}")
    
    # 打印数据计数
    print("\n数据计数:")
    print(f"CPU数据: {processor.cpu_count}")
    print(f"内存数据1: {processor.mem_count1}")
    print(f"内存数据2: {processor.mem_count2}")
    print(f"网络数据: {processor.net_count}")
    print(f"磁盘忙碌度数据: {processor.disk_count1}")
    print(f"磁盘传输数据: {processor.disk_count2}")
    
    # 打印T序号到时间戳的映射
    print("\nT序号到时间戳的映射:")
    for t_index, ts_idx in sorted(processor.timestamp_map.items()):
        ts_info = processor.timestamps[ts_idx]
        print(f"{t_index} -> {ts_info['timestamp']}")
    
    return processor

def main():
    """主函数。"""
    parser = argparse.ArgumentParser(description='测试nmon时间戳解析功能')
    parser.add_argument('-f', '--file', type=str, required=True, help='nmon文件路径')
    
    args = parser.parse_args()
    
    try:
        test_timestamp_parsing(args.file)
    except Exception as e:
        print(f"错误: {str(e)}", file=sys.stderr)
        import traceback
        print(traceback.format_exc(), file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
