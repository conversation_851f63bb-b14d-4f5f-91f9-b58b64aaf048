# 文件清理总结

## 已删除的重复文件

### 备份文件
- `backup_source_v1.5.py` - 旧版本备份文件
- `backup_source_v2.0.py` - 旧版本备份文件

### 构建文件
- `build_exe.py` - 重复的构建脚本
- `build_unified_exe.py` - 重复的构建脚本
- `build_v3.0.py` - 重复的构建脚本
- `build_v3.1.py` - 重复的构建脚本

### 启动器文件
- `all_in_one_launcher.py` - 功能重复的启动器
- `complete_launcher.py` - 功能重复的启动器
- `simple_launcher.py` - 功能重复的启动器
- `standalone_launcher.py` - 功能重复的启动器
- `run_all_in_one.py` - 重复的运行脚本
- `run_complete_launcher.py` - 重复的运行脚本
- `run_simple_launcher.py` - 重复的运行脚本
- `run_standalone.py` - 重复的运行脚本
- `run_unified_tool.py` - 重复的运行脚本
- `run_performance_analyzer.py` - 重复的运行脚本
- `run_unified_nmon_analyzer.py` - 重复的运行脚本
- `run_nmon_analyzer.py` - 重复的运行脚本
- `run_nmon_processor.py` - 重复的运行脚本

### 测试文件
- `test_advanced_analyzer.py` - 测试文件
- `test_chart_optimization.py` - 测试文件
- `test_nmon_file.py` - 测试文件
- `test_nmon_processor.py` - 测试文件
- `test_nmon_timestamp.py` - 测试文件
- `test_save_dialog.py` - 测试文件

### 功能重复的分析器文件
- `minimal_analyzer.py` - 简化版分析器
- `simple_performance_analyzer.py` - 简化版分析器
- `unified_gui.py` - 功能重复的GUI
- `unified_nmon_analyzer.py` - 功能重复的分析器
- `nmon_to_excel.py` - 功能重复的转换器
- `nmon_analyzer_gui.py` - 简单版GUI（功能被raw_nmon_analyzer_gui.py覆盖）

### 其他重复文件
- `prepare_complete_package.py` - 打包脚本

## 保留的核心文件

### 主程序文件
- `main.py` - 主入口程序
- `unified_launcher.py` - 统一启动器
- `run_raw_nmon_analyzer.py` - 原始nmon分析器启动脚本

### 核心功能模块
- `nmon_parser.py` - nmon文件解析器
- `raw_nmon_analyzer.py` - 原始nmon文件分析器
- `raw_nmon_analyzer_gui.py` - 原始nmon文件分析器GUI
- `excel_processor.py` - Excel文件处理器
- `excel_generator.py` - Excel文件生成器
- `batch_processor.py` - 批处理器

### 图表和可视化
- `chart_generator.py` - 图表生成器
- `chart_optimizer.py` - 图表优化器
- `optimized_plotter.py` - 优化的绘图器

### 数据分析
- `data_analyzer.py` - 数据分析器
- `web_analyzer_component.py` - Web分析组件

### 用户界面
- `gui.py` - 主GUI界面
- `font_settings_dialog.py` - 字体设置对话框
- `save_options_dialog.py` - 保存选项对话框
- `settings_dialog.py` - 设置对话框

### 工具和组件
- `component_base.py` - 组件基类
- `component_manager.py` - 组件管理器
- `save_results_thread.py` - 保存结果线程
- `ensure_fonts.py` - 字体确保工具
- `logger_utils.py` - 日志工具
- `exception_handler.py` - 异常处理器
- `version.py` - 版本信息

## 清理效果

- **删除文件数量**: 29个
- **保留文件数量**: 25个
- **清理比例**: 约53.7%

## 文件功能说明

### 主要应用入口
1. `main.py` - 主应用程序入口，启动统一启动器
2. `run_raw_nmon_analyzer.py` - 直接启动原始nmon分析器

### 核心分析功能
1. `nmon_parser.py` - 解析nmon文件格式，提取性能数据
2. `raw_nmon_analyzer.py` - 分析nmon文件并生成报告
3. `raw_nmon_analyzer_gui.py` - 提供图形界面进行nmon文件分析

### 数据处理和输出
1. `excel_processor.py` - 处理Excel文件
2. `excel_generator.py` - 生成Excel报告
3. `chart_generator.py` - 生成性能图表
4. `batch_processor.py` - 批量处理多个文件

### 用户界面
1. `unified_launcher.py` - 统一启动器界面
2. `gui.py` - 主GUI界面
3. 各种对话框文件 - 提供设置和选项界面

清理后的文件结构更加清晰，去除了重复和冗余的文件，保留了核心功能模块。
