"""
Generate a simple app icon for the application.
"""

import matplotlib.pyplot as plt
import numpy as np
from PIL import Image, ImageDraw

def create_app_icon():
    """Create a simple app icon for the application."""
    # Create a 256x256 transparent image
    img = Image.new('RGBA', (256, 256), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Draw a rounded rectangle for the background
    draw.rounded_rectangle([(20, 20), (236, 236)], radius=30, fill=(65, 105, 225, 255))
    
    # Draw a CPU icon (simplified as a square with grid lines)
    draw.rounded_rectangle([(60, 60), (196, 196)], radius=15, fill=(255, 255, 255, 255))
    
    # Draw grid lines to represent a CPU
    for i in range(80, 196, 30):
        draw.line([(i, 60), (i, 196)], fill=(65, 105, 225, 128), width=3)
        draw.line([(60, i), (196, i)], fill=(65, 105, 225, 128), width=3)
    
    # Draw a graph line to represent performance monitoring
    x = np.linspace(60, 196, 20)
    y = 180 - 30 * np.sin(np.linspace(0, 3*np.pi, 20))
    points = list(zip(x, y))
    draw.line(points, fill=(255, 69, 0, 255), width=5)
    
    # Save as ICO file
    img.save('icons/app_icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
    
    # Also save as PNG for reference
    img.save('icons/app_icon.png')
    
    print("App icon created successfully!")

if __name__ == "__main__":
    create_app_icon()
