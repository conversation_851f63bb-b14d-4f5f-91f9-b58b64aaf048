import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import io
from logger_utils import log, log_exception, get_logs

class OptimizedPlotter:
    """优化的绘图类，提供更高效的数据采样和绘图功能"""

    def __init__(self):
        """初始化优化绘图器"""
        pass

    def plot_with_timestamps_optimized(self, timestamps, data, label=None, valid_timestamp_count=None):
        """
        优化后的绘图函数，更智能的数据采样

        参数:
            timestamps: 时间戳列表或数组
            data: 要绘制的数据点列表或数组
            label: 图表图例的标签
            valid_timestamp_count: 有效时间戳的行数

        返回:
            None
        """
        # 强制限制数据点数量，防止生成过多刻度
        log(f"原始数据点数: {len(data)}")

        # 检查时间戳和数据的长度
        if len(data) == 0:
            log(f"警告: {label or '未命名数据'} 没有数据可绘制")
            return

        # 根据数据量动态调整采样策略
        if len(data) > 10000:
            max_points = 300  # 超大数据集使用更少的点
        elif len(data) > 5000:
            max_points = 400  # 大数据集
        else:
            max_points = 500  # 中小数据集

        # 检查时间戳类型
        is_datetime = False
        try:
            if isinstance(timestamps, pd.Series) and pd.api.types.is_datetime64_any_dtype(timestamps):
                is_datetime = True
            elif len(timestamps) > 0 and (isinstance(timestamps[0], (pd.Timestamp, np.datetime64)) or hasattr(timestamps[0], 'strftime')):
                is_datetime = True
        except Exception as e:
            log(f"警告: 检查时间戳类型时出错: {e}")

        # 使用更高效的采样方法
        if len(data) > max_points:
            # 使用更高效的采样方法
            try:
                # 计算采样间隔
                sample_interval = max(1, len(data) // max_points)
                log(f"采样间隔: {sample_interval}")

                if is_datetime and len(timestamps) >= len(data):
                    # 对于时间序列数据，使用更智能的采样
                    try:
                        # 创建时间序列
                        ts = pd.Series(data, index=pd.to_datetime(timestamps[:len(data)]))

                        # 检查时间范围
                        time_range = ts.index.max() - ts.index.min()
                        total_seconds = time_range.total_seconds()

                        if total_seconds > 0:
                            # 计算合适的采样频率
                            freq = f"{max(1, int(total_seconds / max_points))}S"  # 每x秒一个点
                            log(f"使用时间采样频率: {freq}")

                            # 重采样
                            resampled = ts.resample(freq).mean()
                            data = resampled.values
                            timestamps = resampled.index
                            log(f"时间采样后的数据点数: {len(data)}")
                        else:
                            # 如果时间范围为0，使用简单采样
                            data = data[::sample_interval]
                            timestamps = timestamps[:len(data)]
                            log(f"时间范围为0，使用简单采样，采样后数据点数: {len(data)}")
                    except Exception as e:
                        log(f"时间序列采样失败，回退到简单采样: {e}")
                        data = data[::sample_interval]
                        timestamps = timestamps[:len(data)]
                else:
                    # 对于非时间序列数据，使用简单采样
                    data = data[::sample_interval]
                    if len(timestamps) >= len(data):
                        timestamps = timestamps[:len(data)]
                    else:
                        timestamps = list(range(len(data)))

                log(f"采样后的数据点数: {len(data)}")
            except Exception as e:
                log(f"采样失败: {e}")

        # 确保时间戳和数据长度一致
        if len(timestamps) > len(data):
            log(f"时间戳数量({len(timestamps)})大于数据点数({len(data)})，截断时间戳")
            timestamps = timestamps[:len(data)]
        elif len(timestamps) < len(data):
            log(f"时间戳数量({len(timestamps)})小于数据点数({len(data)})，截断数据")
            data = data[:len(timestamps)]

        # 如果时间戳为空，使用索引
        if len(timestamps) == 0:
            log(f"警告: {label or '未命名数据'} 没有时间戳数据，使用索引")
            plt.plot(range(len(data)), data, label=label, marker='', linestyle='-')
            plt.xlim(left=0)
            return

        # 绘制图表
        if is_datetime:
            try:
                log(f"使用时间戳作为 {label or '未命名数据'} 的x轴")
                # 确保数据从最小时间戳开始
                plt.plot(timestamps, data, label=label, marker='', linestyle='-')

                # 设置x轴范围，确保数据线从横坐标时间范围的起点开始
                try:
                    min_time = min(timestamps)
                    plt.xlim(left=min_time)
                    log(f"设置时间轴起点为 {min_time}")
                except Exception as e:
                    log(f"警告: 设置时间轴范围时出错: {e}")
            except Exception as e:
                log(f"警告: 使用时间戳绘图失败: {e}")
                log("回退到使用索引绘图")
                plt.plot(range(len(data)), data, label=label, marker='', linestyle='-')
                # 对于索引绘图，设置x轴从0开始
                plt.xlim(left=0)
        else:
            # 如果时间戳不是日期时间对象或为空，使用索引
            log(f"使用数字索引作为 {label or '未命名数据'} 的x轴")
            plt.plot(range(len(data)), data, label=label, marker='', linestyle='-')
            # 对于索引绘图，设置x轴从0开始
            plt.xlim(left=0)

    def format_time_axis_optimized(self, timestamps, valid_timestamp_count=None):
        """
        优化的时间轴格式化函数

        参数:
            timestamps: 时间戳列表或数组
            valid_timestamp_count: 有效时间戳的行数

        返回:
            bool: 如果应用了格式化返回True，否则返回False
        """
        try:
            # 增加输出确定异常位置
            log("开始格式化时间轴...")

            # 检查时间戳类型
            is_datetime = False
            if len(timestamps) > 0:
                if isinstance(timestamps, pd.Series) and pd.api.types.is_datetime64_any_dtype(timestamps):
                    is_datetime = True
                    log("时间戳类型: pandas Series datetime64")
                elif isinstance(timestamps[0], (pd.Timestamp, np.datetime64)) or hasattr(timestamps[0], 'strftime'):
                    is_datetime = True
                    log(f"时间戳类型: {type(timestamps[0]).__name__}")
                else:
                    log(f"时间戳类型: {type(timestamps[0]).__name__} (非日期时间类型)")

            if is_datetime:
                # 确保 x 轴格式化为时间（小时:分钟:秒）
                import matplotlib.dates as mdates

                # 根据时间范围动态选择格式
                try:
                    min_time = min(timestamps)
                    max_time = max(timestamps)
                    time_range = max_time - min_time

                    if hasattr(time_range, 'days'):
                        days = time_range.days
                        if days > 30:  # 超过一个月
                            date_format = '%Y-%m-%d'
                            log("使用年-月-日格式")
                        elif days > 1:  # 超过一天
                            date_format = '%m-%d %H:%M'
                            log("使用月-日 时:分格式")
                        else:  # 一天内
                            date_format = '%H:%M:%S'
                            log("使用时:分:秒格式")
                    else:
                        date_format = '%H:%M:%S'
                        log("默认使用时:分:秒格式")

                    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter(date_format))
                except Exception as e:
                    log(f"设置日期格式化器时出错: {e}，使用默认格式")
                    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))

                plt.gcf().autofmt_xdate()  # 自动格式化 x 轴日期标签

                # 限制刻度数量，防止超过最大限制
                # 如果提供了有效时间戳行数，使用它来确定刻度数量
                if valid_timestamp_count is not None and valid_timestamp_count > 0:
                    data_length = valid_timestamp_count
                    log(f"使用有效时间戳行数 {valid_timestamp_count} 进行刻度计算")
                else:
                    data_length = len(timestamps)
                    log(f"使用时间戳长度 {data_length} 进行刻度计算")

                # 根据数据量动态调整刻度数量
                if data_length > 10000:
                    max_ticks = 5  # 超大数据集使用更少的刻度
                elif data_length > 5000:
                    max_ticks = 7  # 大数据集
                else:
                    max_ticks = 10  # 中小数据集

                log(f"设置最大刻度数量为 {max_ticks}")
                plt.gca().xaxis.set_major_locator(plt.MaxNLocator(max_ticks))

                return True
            else:
                # 如果不是时间戳，限制数字刻度的数量
                data_length = len(timestamps) if len(timestamps) > 0 else 100
                max_ticks = min(10, data_length // 50 + 1)  # 非常保守的刻度数量
                log(f"非时间戳数据，设置最大刻度数量为 {max_ticks}")
                plt.gca().xaxis.set_major_locator(plt.MaxNLocator(max_ticks))
                return False
        except Exception as e:
            log(f"错误: 格式化时间轴时出错: {e}")
            import traceback
            log(f"错误详情: {traceback.format_exc()}")
            # 如果出错，使用最安全的设置
            log("使用最安全的设置: MaxNLocator(5)")
            plt.gca().xaxis.set_major_locator(plt.MaxNLocator(5))  # 只显示5个刻度
            return False
