"""
组件基类模块，定义了所有组件必须实现的接口。
"""

from enum import Enum
from typing import Dict, List, Any, Optional, Callable
from PyQt5.QtWidgets import QWidget
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QIcon


class ComponentState(Enum):
    """组件状态枚举"""
    UNINITIALIZED = "uninitialized"
    INITIALIZED = "initialized"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


class ComponentEvent:
    """组件事件类"""
    def __init__(self, event_type: str, source: str, data: Any = None):
        self.event_type = event_type
        self.source = source
        self.data = data
        self.timestamp = None

        from datetime import datetime
        self.timestamp = datetime.now()


class ComponentBase(QWidget):
    """
    增强的组件基类，提供完整的生命周期管理和事件系统。
    """

    # 信号定义
    state_changed = pyqtSignal(str, str)  # (old_state, new_state)
    event_occurred = pyqtSignal(object)   # ComponentEvent
    error_occurred = pyqtSignal(str)      # error_message

    def __init__(self, parent=None):
        """
        初始化组件基类。

        参数:
            parent: 父窗口
        """
        super().__init__(parent)

        # 基本属性
        self._component_id = ""
        self._component_name = ""
        self._component_description = ""
        self._component_version = "1.0.0"
        self._component_icon = None

        # 状态管理
        self._state = ComponentState.UNINITIALIZED
        self._previous_state = None

        # 依赖管理
        self._dependencies = []
        self._dependents = []

        # 事件系统
        self._event_handlers = {}
        self._event_queue = []

        # 配置管理
        self._config = {}

        # 初始化组件
        self._initialize()

    # 基本属性
    @property
    def component_id(self) -> str:
        """组件唯一标识符"""
        return self._component_id

    @component_id.setter
    def component_id(self, value: str):
        self._component_id = value

    @property
    def component_name(self) -> str:
        """组件名称"""
        return self._component_name

    @component_name.setter
    def component_name(self, value: str):
        self._component_name = value

    @property
    def component_description(self) -> str:
        """组件描述"""
        return self._component_description

    @component_description.setter
    def component_description(self, value: str):
        self._component_description = value

    @property
    def component_version(self) -> str:
        """组件版本"""
        return self._component_version

    @component_version.setter
    def component_version(self, value: str):
        self._component_version = value

    @property
    def component_icon(self) -> Optional[QIcon]:
        """组件图标"""
        return self._component_icon

    @component_icon.setter
    def component_icon(self, value: Optional[QIcon]):
        self._component_icon = value

    # 状态管理
    @property
    def state(self) -> ComponentState:
        """组件当前状态"""
        return self._state

    @property
    def is_running(self) -> bool:
        """组件是否正在运行"""
        return self._state == ComponentState.RUNNING

    def _set_state(self, new_state: ComponentState):
        """设置组件状态"""
        if self._state != new_state:
            old_state = self._state
            self._previous_state = old_state
            self._state = new_state
            self.state_changed.emit(old_state.value, new_state.value)

    def _initialize(self):
        """内部初始化方法"""
        try:
            self.on_initialize()
            self._set_state(ComponentState.INITIALIZED)
        except Exception as e:
            self._set_state(ComponentState.ERROR)
            self.error_occurred.emit(f"组件初始化失败: {str(e)}")

    def start(self):
        """启动组件"""
        if self._state not in [ComponentState.INITIALIZED, ComponentState.STOPPED]:
            return False

        try:
            self._set_state(ComponentState.STARTING)
            self.on_start()
            self._set_state(ComponentState.RUNNING)
            return True
        except Exception as e:
            self._set_state(ComponentState.ERROR)
            self.error_occurred.emit(f"组件启动失败: {str(e)}")
            return False

    def stop(self):
        """停止组件"""
        if self._state != ComponentState.RUNNING:
            return False

        try:
            self._set_state(ComponentState.STOPPING)
            self.on_stop()
            self._set_state(ComponentState.STOPPED)
            return True
        except Exception as e:
            self._set_state(ComponentState.ERROR)
            self.error_occurred.emit(f"组件停止失败: {str(e)}")
            return False

    # 事件系统
    def register_event_handler(self, event_type: str, handler: Callable):
        """注册事件处理器"""
        if event_type not in self._event_handlers:
            self._event_handlers[event_type] = []
        self._event_handlers[event_type].append(handler)

    def unregister_event_handler(self, event_type: str, handler: Callable):
        """注销事件处理器"""
        if event_type in self._event_handlers:
            try:
                self._event_handlers[event_type].remove(handler)
            except ValueError:
                pass

    def emit_event(self, event_type: str, data: Any = None):
        """发送事件"""
        event = ComponentEvent(event_type, self.component_id, data)
        self.event_occurred.emit(event)

        # 处理本地事件处理器
        if event_type in self._event_handlers:
            for handler in self._event_handlers[event_type]:
                try:
                    handler(event)
                except Exception as e:
                    self.error_occurred.emit(f"事件处理器错误: {str(e)}")

    # 依赖管理
    def add_dependency(self, component_id: str):
        """添加依赖组件"""
        if component_id not in self._dependencies:
            self._dependencies.append(component_id)

    def remove_dependency(self, component_id: str):
        """移除依赖组件"""
        if component_id in self._dependencies:
            self._dependencies.remove(component_id)

    def get_dependencies(self) -> List[str]:
        """获取依赖组件列表"""
        return self._dependencies.copy()

    # 配置管理
    def set_config(self, key: str, value: Any):
        """设置配置项"""
        self._config[key] = value

    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        return self._config.get(key, default)

    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()

    # 抽象方法 - 子类需要实现
    def on_initialize(self):
        """组件初始化时调用"""
        pass

    def on_start(self):
        """组件启动时调用"""
        pass

    def on_stop(self):
        """组件停止时调用"""
        pass

    def get_widget(self) -> QWidget:
        """获取组件的主窗口部件"""
        return self

    def get_menu_items(self) -> List[Dict[str, Any]]:
        """获取组件的菜单项"""
        return []

    def get_toolbar_items(self) -> List[Dict[str, Any]]:
        """获取组件的工具栏项"""
        return []

    def save_state(self) -> Dict[str, Any]:
        """保存组件状态"""
        return {
            'component_id': self.component_id,
            'state': self._state.value,
            'config': self._config.copy()
        }

    def restore_state(self, state: Dict[str, Any]):
        """恢复组件状态"""
        if 'config' in state:
            self._config.update(state['config'])

    # 工具方法
    def get_info(self) -> Dict[str, Any]:
        """获取组件信息"""
        return {
            'id': self.component_id,
            'name': self.component_name,
            'description': self.component_description,
            'version': self.component_version,
            'state': self._state.value,
            'dependencies': self._dependencies.copy()
        }
