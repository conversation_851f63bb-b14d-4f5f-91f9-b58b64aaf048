"""
组件基类模块，定义了所有组件必须实现的接口。
"""

from PyQt5.QtWidgets import QWidget
from PyQt5.QtGui import QIcon


class ComponentBase(QWidget):
    """
    组件基类，所有功能组件必须继承此类并实现其方法。
    """

    def __init__(self, parent=None):
        """
        初始化组件基类。
        
        参数:
            parent: 父窗口
        """
        super().__init__(parent)
        self._component_id = ""
        self._component_name = ""
        self._component_description = ""
        self._component_version = ""
        self._component_icon = None
        self._is_active = False
        
    @property
    def component_id(self):
        """组件唯一标识符"""
        return self._component_id
        
    @component_id.setter
    def component_id(self, value):
        self._component_id = value
        
    @property
    def component_name(self):
        """组件名称"""
        return self._component_name
        
    @component_name.setter
    def component_name(self, value):
        self._component_name = value
        
    @property
    def component_description(self):
        """组件描述"""
        return self._component_description
        
    @component_description.setter
    def component_description(self, value):
        self._component_description = value
        
    @property
    def component_version(self):
        """组件版本"""
        return self._component_version
        
    @component_version.setter
    def component_version(self, value):
        self._component_version = value
        
    @property
    def component_icon(self):
        """组件图标"""
        return self._component_icon
        
    @component_icon.setter
    def component_icon(self, value):
        self._component_icon = value
        
    @property
    def is_active(self):
        """组件是否处于活动状态"""
        return self._is_active
        
    @is_active.setter
    def is_active(self, value):
        self._is_active = value
        
    def activate(self):
        """
        激活组件，当组件被选中显示时调用。
        子类可以重写此方法以执行必要的初始化。
        """
        self._is_active = True
        
    def deactivate(self):
        """
        停用组件，当组件被隐藏时调用。
        子类可以重写此方法以执行必要的清理。
        """
        self._is_active = False
        
    def get_widget(self):
        """
        获取组件的主窗口部件。
        
        返回:
            QWidget: 组件的主窗口部件
        """
        return self
        
    def get_menu_items(self):
        """
        获取组件的菜单项。
        
        返回:
            list: 菜单项列表，每个菜单项是一个字典，包含以下键：
                - text: 菜单项文本
                - icon: 菜单项图标（可选）
                - shortcut: 菜单项快捷键（可选）
                - callback: 菜单项回调函数
        """
        return []
        
    def get_toolbar_items(self):
        """
        获取组件的工具栏项。
        
        返回:
            list: 工具栏项列表，每个工具栏项是一个字典，包含以下键：
                - text: 工具栏项文本
                - icon: 工具栏项图标
                - tooltip: 工具栏项提示文本
                - callback: 工具栏项回调函数
        """
        return []
        
    def handle_event(self, event_type, event_data=None):
        """
        处理事件。
        
        参数:
            event_type: 事件类型
            event_data: 事件数据
            
        返回:
            bool: 是否处理了事件
        """
        return False
        
    def save_state(self):
        """
        保存组件状态。
        
        返回:
            dict: 组件状态
        """
        return {}
        
    def restore_state(self, state):
        """
        恢复组件状态。
        
        参数:
            state: 组件状态
        """
        pass
