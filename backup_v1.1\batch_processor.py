import os
import pandas as pd
from openpyxl import load_workbook
from logger_utils import log, log_exception, get_logs

class BatchProcessor:
    """用于批处理大量文件的类"""

    def __init__(self, chart_optimizer):
        """
        初始化批处理器

        参数:
            chart_optimizer: ChartOptimizer实例，用于优化图表生成
        """
        self.chart_optimizer = chart_optimizer
        self.progress_callback = None

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback
        self.chart_optimizer.set_progress_callback(callback)

    def update_progress(self, message, percent=None):
        """更新进度信息"""
        # 记录日志，但不使用返回值
        log(message)

        # 确保 message 是字符串
        if not isinstance(message, str):
            message = str(message)

        # 调用回调函数
        if self.progress_callback:
            self.progress_callback(message, percent)

    def save_summary_only(self, results, output_path):
        """
        只保存摘要数据，不生成图表，适用于大量文件的快速处理

        参数:
            results: 结果列表
            output_path: 输出文件路径

        返回:
            output_path: 保存的文件路径
        """
        if not results:
            raise ValueError("No results to save. Process files first.")

        self.update_progress("创建摘要数据...")

        # 创建摘要DataFrame
        summary_df = pd.DataFrame([
            {
                'File': r['File'],
                'IP Address': r['IP Address'],
                'CPU Usage (%)': r['CPU Usage (%)'],
                'Memory Usage (%)': r['Memory Usage (%)'],
                '磁盘繁忙度 (%)': r.get('Disk Busy (%)', r.get('Max DiskIO Usage (%)', 0)),
                'Max Disk Column': r['Max Disk Column']
            } for r in results
        ])

        # 添加统计信息
        stats_df = pd.DataFrame([{
            'File': '平均值',
            'IP Address': '',
            'CPU Usage (%)': summary_df['CPU Usage (%)'].mean(),
            'Memory Usage (%)': summary_df['Memory Usage (%)'].mean(),
            '磁盘繁忙度 (%)': summary_df['磁盘繁忙度 (%)'].mean(),
            'Max Disk Column': ''
        }, {
            'File': '最大值',
            'IP Address': '',
            'CPU Usage (%)': summary_df['CPU Usage (%)'].max(),
            'Memory Usage (%)': summary_df['Memory Usage (%)'].max(),
            '磁盘繁忙度 (%)': summary_df['磁盘繁忙度 (%)'].max(),
            'Max Disk Column': ''
        }])

        # 合并摘要和统计信息
        final_df = pd.concat([summary_df, stats_df], ignore_index=True)

        self.update_progress(f"保存摘要数据到 {output_path}...")

        # 保存到Excel
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            final_df.to_excel(writer, sheet_name='Summary', index=False)

            # 获取工作表并设置列宽
            worksheet = writer.sheets['Summary']
            for i, col in enumerate(final_df.columns):
                max_len = max(
                    len(str(col)),
                    final_df[col].astype(str).map(len).max() if len(final_df) > 0 else 0
                )
                worksheet.column_dimensions[chr(65 + i)].width = max_len + 4

        self.update_progress("摘要数据保存完成")
        return output_path

    def add_charts_to_excel_with_retry(self, results, excel_path, max_retries=3):
        """
        带有重试机制的图表添加方法

        参数:
            results: 结果列表
            excel_path: Excel文件路径
            max_retries: 最大重试次数

        返回:
            (result, logs): 成功返回Excel路径和日志，失败返回(None, logs)
        """
        for attempt in range(max_retries):
            try:
                self.update_progress(f"尝试添加图表 (第 {attempt+1}/{max_retries} 次)...")
                result, logs = self.chart_optimizer.add_charts_to_excel_optimized(results, excel_path)
                if result:
                    return result, logs
                else:
                    self.update_progress(f"第 {attempt+1} 次尝试失败，准备重试...")
            except Exception as e:
                self.update_progress(f"第 {attempt+1} 次尝试出错: {e}")
                import traceback
                self.update_progress(f"错误详情: {traceback.format_exc()}")

                # 如果是最后一次尝试，尝试只保存摘要
                if attempt == max_retries - 1:
                    self.update_progress("所有重试都失败，尝试只保存摘要数据...")
                    try:
                        self.save_summary_only(results, excel_path)
                        return excel_path, get_logs() + ["只保存了摘要数据，图表生成失败"]
                    except Exception as e2:
                        self.update_progress(f"保存摘要也失败: {e2}")

        return None, get_logs()

    def save_results_in_batches(self, results, output_path, batch_size=10):
        """
        分批处理大量文件，避免内存溢出

        参数:
            results: 结果列表
            output_path: 输出文件路径
            batch_size: 每批处理的文件数量

        返回:
            (output_path, logs): 保存的文件路径和日志
        """
        if len(results) <= batch_size:
            # 文件数量少，直接处理
            self.update_progress(f"文件数量较少 ({len(results)}个)，直接处理...")
            return self.add_charts_to_excel_with_retry(results, output_path)

        # 文件数量多，分批处理
        self.update_progress(f"文件数量较多 ({len(results)}个)，分批处理...")

        # 先保存摘要
        self.save_summary_only(results, output_path)
        self.update_progress("主摘要数据已保存")

        # 分批处理图表
        all_logs = []
        success_count = 0

        # 计算批次数
        num_batches = (len(results) + batch_size - 1) // batch_size

        for i in range(0, len(results), batch_size):
            batch = results[i:i+batch_size]
            batch_num = i // batch_size + 1

            # 计算总体进度百分比
            overall_percent = int(batch_num / num_batches * 100)
            self.update_progress(f"处理批次 {batch_num}/{num_batches} ({len(batch)}个文件)...", overall_percent)

            # 为每个批次创建单独的Excel文件
            batch_path = output_path.replace('.xlsx', f'_batch{batch_num}.xlsx')

            # 先保存该批次的摘要
            self.save_summary_only(batch, batch_path)

            # 添加图表
            result, logs = self.add_charts_to_excel_with_retry(batch, batch_path)
            all_logs.extend(logs)

            if result:
                success_count += 1
                self.update_progress(f"批次 {batch_num} 处理成功")
            else:
                self.update_progress(f"批次 {batch_num} 处理失败")

        self.update_progress(f"分批处理完成: 共 {num_batches} 个批次, 成功 {success_count} 个", 100)
        return output_path, all_logs
