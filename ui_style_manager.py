"""
UI样式管理器

统一管理所有界面组件的样式，确保一致的用户体验。
"""

from PyQt5.QtCore import QSettings
from PyQt5.QtGui import QFont, QPalette, QColor
from PyQt5.QtWidgets import QApplication
from typing import Dict, Any


class UIStyleManager:
    """UI样式管理器类"""
    
    # 样式主题定义
    THEMES = {
        'modern_blue': {
            'name': '现代蓝色',
            'description': '现代化的蓝色主题，适合专业环境',
            'colors': {
                'primary': '#2a82da',
                'primary_hover': '#3a92ea',
                'primary_pressed': '#1a72ca',
                'secondary': '#4CAF50',
                'secondary_hover': '#5CBF60',
                'secondary_pressed': '#3C9F40',
                'accent': '#FF9800',
                'accent_hover': '#FFA820',
                'accent_pressed': '#E08800',
                'background': '#f0f5fa',
                'surface': '#ffffff',
                'text_primary': '#333333',
                'text_secondary': '#666666',
                'text_hint': '#999999',
                'border': '#c0d0e0',
                'border_light': '#e0e9f0',
                'success': '#4CAF50',
                'warning': '#FF9800',
                'error': '#f44336',
                'info': '#2196F3'
            }
        },
        'dark_modern': {
            'name': '现代暗色',
            'description': '现代化的暗色主题，减少眼部疲劳',
            'colors': {
                'primary': '#3a82f7',
                'primary_hover': '#4a92ff',
                'primary_pressed': '#2a72e7',
                'secondary': '#5CBF60',
                'secondary_hover': '#6CCF70',
                'secondary_pressed': '#4CAF50',
                'accent': '#FFA726',
                'accent_hover': '#FFB74D',
                'accent_pressed': '#FF9800',
                'background': '#1e1e1e',
                'surface': '#2d2d2d',
                'text_primary': '#ffffff',
                'text_secondary': '#cccccc',
                'text_hint': '#888888',
                'border': '#404040',
                'border_light': '#505050',
                'success': '#4CAF50',
                'warning': '#FF9800',
                'error': '#f44336',
                'info': '#2196F3'
            }
        },
        'light_classic': {
            'name': '浅色经典',
            'description': '经典的浅色主题，简洁明了',
            'colors': {
                'primary': '#0078d4',
                'primary_hover': '#106ebe',
                'primary_pressed': '#005a9e',
                'secondary': '#107c10',
                'secondary_hover': '#0e6e0e',
                'secondary_pressed': '#0c5e0c',
                'accent': '#d83b01',
                'accent_hover': '#c23201',
                'accent_pressed': '#a62d01',
                'background': '#f5f5f5',
                'surface': '#ffffff',
                'text_primary': '#323130',
                'text_secondary': '#605e5c',
                'text_hint': '#8a8886',
                'border': '#d0d0d0',
                'border_light': '#e0e0e0',
                'success': '#107c10',
                'warning': '#d83b01',
                'error': '#d13438',
                'info': '#0078d4'
            }
        }
    }
    
    # 字体大小定义
    FONT_SIZES = {
        'tiny': 8,
        'small': 9,
        'normal': 10,
        'medium': 11,
        'large': 12,
        'xlarge': 14,
        'xxlarge': 16,
        'title': 18,
        'header': 20
    }
    
    def __init__(self):
        """初始化样式管理器"""
        self.settings = QSettings("PerformanceAnalyzer", "Settings")
        self.current_theme = self.get_current_theme()
        self.current_font_size = self.get_current_font_size()
    
    def get_current_theme(self) -> str:
        """获取当前主题"""
        return self.settings.value("ui_theme", "modern_blue", type=str)
    
    def get_current_font_size(self) -> int:
        """获取当前字体大小"""
        return self.settings.value("font_size", 10, type=int)
    
    def set_theme(self, theme_name: str):
        """设置主题"""
        if theme_name in self.THEMES:
            self.current_theme = theme_name
            self.settings.setValue("ui_theme", theme_name)
            self.settings.sync()
    
    def set_font_size(self, size: int):
        """设置字体大小"""
        self.current_font_size = size
        self.settings.setValue("font_size", size)
        self.settings.sync()
    
    def get_theme_colors(self, theme_name: str = None) -> Dict[str, str]:
        """获取主题颜色"""
        theme = theme_name or self.current_theme
        return self.THEMES.get(theme, self.THEMES['modern_blue'])['colors']
    
    def get_color(self, color_name: str, theme_name: str = None) -> str:
        """获取指定颜色"""
        colors = self.get_theme_colors(theme_name)
        return colors.get(color_name, '#000000')
    
    def apply_global_style(self):
        """应用全局样式"""
        # 设置全局字体
        font = QFont()
        font.setPointSize(self.current_font_size)
        font.setFamily("Microsoft YaHei, Arial, sans-serif")
        QApplication.setFont(font)
        
        # 设置Fusion样式
        QApplication.setStyle("Fusion")
        
        # 应用调色板
        self._apply_palette()
    
    def _apply_palette(self):
        """应用调色板"""
        colors = self.get_theme_colors()
        palette = QPalette()
        
        # 设置基本颜色
        palette.setColor(QPalette.Window, QColor(colors['background']))
        palette.setColor(QPalette.WindowText, QColor(colors['text_primary']))
        palette.setColor(QPalette.Base, QColor(colors['surface']))
        palette.setColor(QPalette.AlternateBase, QColor(colors['border_light']))
        palette.setColor(QPalette.Text, QColor(colors['text_primary']))
        palette.setColor(QPalette.Button, QColor(colors['surface']))
        palette.setColor(QPalette.ButtonText, QColor(colors['text_primary']))
        palette.setColor(QPalette.Highlight, QColor(colors['primary']))
        palette.setColor(QPalette.HighlightedText, QColor('#ffffff'))
        
        QApplication.setPalette(palette)
    
    def get_button_style(self, button_type: str = 'primary', size: str = 'normal') -> str:
        """获取按钮样式"""
        colors = self.get_theme_colors()
        
        # 按钮类型颜色映射
        type_colors = {
            'primary': (colors['primary'], colors['primary_hover'], colors['primary_pressed']),
            'secondary': (colors['secondary'], colors['secondary_hover'], colors['secondary_pressed']),
            'accent': (colors['accent'], colors['accent_hover'], colors['accent_pressed']),
            'success': (colors['success'], colors['success'], colors['success']),
            'warning': (colors['warning'], colors['warning'], colors['warning']),
            'error': (colors['error'], colors['error'], colors['error']),
            'info': (colors['info'], colors['info'], colors['info'])
        }
        
        bg_color, hover_color, pressed_color = type_colors.get(button_type, type_colors['primary'])
        
        # 按钮大小映射
        size_styles = {
            'small': {'padding': '6px 12px', 'font_size': f'{self.current_font_size - 1}px', 'border_radius': '3px'},
            'normal': {'padding': '8px 16px', 'font_size': f'{self.current_font_size}px', 'border_radius': '4px'},
            'large': {'padding': '12px 24px', 'font_size': f'{self.current_font_size + 1}px', 'border_radius': '5px'},
            'xlarge': {'padding': '16px 32px', 'font_size': f'{self.current_font_size + 2}px', 'border_radius': '6px'}
        }
        
        style_props = size_styles.get(size, size_styles['normal'])
        
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                padding: {style_props['padding']};
                border-radius: {style_props['border_radius']};
                font-size: {style_props['font_size']};
                font-weight: 500;
                text-align: center;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {pressed_color};
            }}
            QPushButton:disabled {{
                background-color: {colors['border']};
                color: {colors['text_hint']};
            }}
        """
    
    def get_input_style(self) -> str:
        """获取输入框样式"""
        colors = self.get_theme_colors()
        
        return f"""
            QLineEdit, QTextEdit, QPlainTextEdit {{
                background-color: {colors['surface']};
                color: {colors['text_primary']};
                border: 1px solid {colors['border']};
                border-radius: 4px;
                padding: 6px 8px;
                font-size: {self.current_font_size}px;
                selection-background-color: {colors['primary']};
                selection-color: white;
            }}
            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
                border-color: {colors['primary']};
                outline: none;
            }}
            QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {{
                background-color: {colors['border_light']};
                color: {colors['text_hint']};
            }}
        """
    
    def get_combobox_style(self) -> str:
        """获取下拉框样式"""
        colors = self.get_theme_colors()
        
        return f"""
            QComboBox {{
                background-color: {colors['surface']};
                color: {colors['text_primary']};
                border: 1px solid {colors['border']};
                border-radius: 4px;
                padding: 6px 8px;
                font-size: {self.current_font_size}px;
                min-height: 20px;
            }}
            QComboBox:hover {{
                border-color: {colors['primary']};
            }}
            QComboBox:focus {{
                border-color: {colors['primary']};
                outline: none;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                width: 12px;
                height: 12px;
                margin: 2px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {colors['surface']};
                color: {colors['text_primary']};
                border: 1px solid {colors['border']};
                selection-background-color: {colors['primary']};
                selection-color: white;
                outline: none;
            }}
        """
    
    def get_groupbox_style(self) -> str:
        """获取分组框样式"""
        colors = self.get_theme_colors()
        
        return f"""
            QGroupBox {{
                border: 1px solid {colors['border']};
                border-radius: 6px;
                margin-top: 12px;
                font-weight: 600;
                color: {colors['text_primary']};
                font-size: {self.current_font_size + 1}px;
                padding-top: 8px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px;
                background-color: {colors['background']};
            }}
        """
    
    def get_label_style(self, label_type: str = 'normal') -> str:
        """获取标签样式"""
        colors = self.get_theme_colors()
        
        type_styles = {
            'normal': {'color': colors['text_primary'], 'font_size': f'{self.current_font_size}px', 'font_weight': 'normal'},
            'title': {'color': colors['text_primary'], 'font_size': f'{self.current_font_size + 6}px', 'font_weight': 'bold'},
            'subtitle': {'color': colors['text_primary'], 'font_size': f'{self.current_font_size + 2}px', 'font_weight': '600'},
            'caption': {'color': colors['text_secondary'], 'font_size': f'{self.current_font_size - 1}px', 'font_weight': 'normal'},
            'hint': {'color': colors['text_hint'], 'font_size': f'{self.current_font_size - 1}px', 'font_weight': 'normal'},
            'success': {'color': colors['success'], 'font_size': f'{self.current_font_size}px', 'font_weight': 'normal'},
            'warning': {'color': colors['warning'], 'font_size': f'{self.current_font_size}px', 'font_weight': 'normal'},
            'error': {'color': colors['error'], 'font_size': f'{self.current_font_size}px', 'font_weight': 'normal'}
        }
        
        style_props = type_styles.get(label_type, type_styles['normal'])
        
        return f"""
            color: {style_props['color']};
            font-size: {style_props['font_size']};
            font-weight: {style_props['font_weight']};
        """
    
    def get_dialog_style(self) -> str:
        """获取对话框样式"""
        colors = self.get_theme_colors()
        
        return f"""
            QDialog {{
                background-color: {colors['background']};
                color: {colors['text_primary']};
            }}
        """
    
    def get_table_style(self) -> str:
        """获取表格样式"""
        colors = self.get_theme_colors()
        
        return f"""
            QTableWidget, QTableView {{
                background-color: {colors['surface']};
                color: {colors['text_primary']};
                border: 1px solid {colors['border']};
                gridline-color: {colors['border_light']};
                selection-background-color: {colors['primary']};
                selection-color: white;
                font-size: {self.current_font_size}px;
            }}
            QHeaderView::section {{
                background-color: {colors['border_light']};
                color: {colors['text_primary']};
                padding: 6px 8px;
                border: 1px solid {colors['border']};
                font-weight: 600;
                font-size: {self.current_font_size}px;
            }}
            QTableWidget::item {{
                padding: 4px 8px;
                border: none;
            }}
            QTableWidget::item:selected {{
                background-color: {colors['primary']};
                color: white;
            }}
        """
    
    def get_tab_style(self) -> str:
        """获取选项卡样式"""
        colors = self.get_theme_colors()
        
        return f"""
            QTabWidget::pane {{
                border: 1px solid {colors['border']};
                background-color: {colors['surface']};
            }}
            QTabBar::tab {{
                background-color: {colors['border_light']};
                color: {colors['text_primary']};
                padding: 8px 16px;
                border: 1px solid {colors['border']};
                border-bottom: none;
                margin-right: 2px;
                font-size: {self.current_font_size}px;
            }}
            QTabBar::tab:selected {{
                background-color: {colors['surface']};
                color: {colors['text_primary']};
                font-weight: 600;
            }}
            QTabBar::tab:hover {{
                background-color: {colors['primary']};
                color: white;
            }}
        """
    
    def get_progressbar_style(self) -> str:
        """获取进度条样式"""
        colors = self.get_theme_colors()
        
        return f"""
            QProgressBar {{
                border: 1px solid {colors['border']};
                border-radius: 4px;
                background-color: {colors['border_light']};
                text-align: center;
                font-size: {self.current_font_size}px;
                color: {colors['text_primary']};
            }}
            QProgressBar::chunk {{
                background-color: {colors['primary']};
                border-radius: 3px;
            }}
        """
    
    def get_scrollbar_style(self) -> str:
        """获取滚动条样式"""
        colors = self.get_theme_colors()
        
        return f"""
            QScrollBar:vertical {{
                background-color: {colors['border_light']};
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {colors['border']};
                border-radius: 6px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {colors['primary']};
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                border: none;
                background: none;
            }}
            QScrollBar:horizontal {{
                background-color: {colors['border_light']};
                height: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:horizontal {{
                background-color: {colors['border']};
                border-radius: 6px;
                min-width: 20px;
            }}
            QScrollBar::handle:horizontal:hover {{
                background-color: {colors['primary']};
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                border: none;
                background: none;
            }}
        """
    
    def get_complete_stylesheet(self) -> str:
        """获取完整的样式表"""
        return f"""
            {self.get_button_style()}
            {self.get_input_style()}
            {self.get_combobox_style()}
            {self.get_groupbox_style()}
            {self.get_dialog_style()}
            {self.get_table_style()}
            {self.get_tab_style()}
            {self.get_progressbar_style()}
            {self.get_scrollbar_style()}
        """
    
    def apply_to_widget(self, widget, widget_type: str = 'dialog'):
        """应用样式到指定控件"""
        if widget_type == 'dialog':
            widget.setStyleSheet(self.get_dialog_style() + self.get_complete_stylesheet())
        elif widget_type == 'main_window':
            widget.setStyleSheet(self.get_complete_stylesheet())
        else:
            widget.setStyleSheet(self.get_complete_stylesheet())


# 全局样式管理器实例
_style_manager = None

def get_style_manager() -> UIStyleManager:
    """获取全局样式管理器实例"""
    global _style_manager
    if _style_manager is None:
        _style_manager = UIStyleManager()
    return _style_manager

def apply_global_style():
    """应用全局样式"""
    get_style_manager().apply_global_style()

def get_button_style(button_type: str = 'primary', size: str = 'normal') -> str:
    """快捷方式：获取按钮样式"""
    return get_style_manager().get_button_style(button_type, size)

def get_input_style() -> str:
    """快捷方式：获取输入框样式"""
    return get_style_manager().get_input_style()

def get_label_style(label_type: str = 'normal') -> str:
    """快捷方式：获取标签样式"""
    return get_style_manager().get_label_style(label_type)

def apply_widget_style(widget, widget_type: str = 'dialog'):
    """快捷方式：应用样式到控件"""
    get_style_manager().apply_to_widget(widget, widget_type)
