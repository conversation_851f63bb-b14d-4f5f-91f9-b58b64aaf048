import os

import matplotlib.pyplot as plt
from openpyxl import load_workbook
from openpyxl.drawing.image import Image
from openpyxl.styles import Font
import io
import matplotlib.font_manager as fm
import platform
import matplotlib as mpl
from cycler import cycler
from logger_utils import log, log_exception, get_logs

# 设置中文字体
# 根据不同操作系统选择适合的字体

def set_chart_fonts():
    """设置图表字体，使用用户选择的字体或默认字体"""
    import os
    from ensure_fonts import load_font_settings, FONT_INFO, get_font_dir

    # 加载字体设置
    chinese_font_key, english_font_key = load_font_settings()

    # 获取字体路径
    font_dir = get_font_dir()
    chinese_font_path = os.path.join(font_dir, FONT_INFO[chinese_font_key]['filename'])
    english_font_path = os.path.join(font_dir, FONT_INFO[english_font_key]['filename'])

    # 检查中文字体是否存在
    chinese_font_available = os.path.exists(chinese_font_path)
    english_font_available = os.path.exists(english_font_path)

    if chinese_font_available:
        log(f"使用中文字体: {FONT_INFO[chinese_font_key]['display_name']}", "INFO")

        # 注册中文字体
        try:
            # 添加字体文件到matplotlib字体管理器
            fm.fontManager.addfont(chinese_font_path)

            # 获取字体名称
            font_id = fm.fontManager._find_fonts_by_path(chinese_font_path)
            if font_id:
                chinese_font_name = fm.fontManager.get_font(font_id[0]).family_name
            else:
                # 如果无法获取字体名称，使用默认名称
                chinese_font_name = 'Source Han Sans SC'

            log(f"成功注册中文字体: {chinese_font_name}", "INFO")
        except Exception as e:
            log(f"注册中文字体时出错: {str(e)}", "ERROR")
            chinese_font_available = False

    # 检查英文字体是否存在
    if english_font_available:
        log(f"使用英文字体: {FONT_INFO[english_font_key]['display_name']}", "INFO")

        # 注册英文字体
        try:
            # 添加字体文件到matplotlib字体管理器
            fm.fontManager.addfont(english_font_path)

            # 获取字体名称
            font_id = fm.fontManager._find_fonts_by_path(english_font_path)
            if font_id:
                english_font_name = fm.fontManager.get_font(font_id[0]).family_name
            else:
                # 如果无法获取字体名称，使用默认名称
                english_font_name = 'Open Sans'

            log(f"成功注册英文字体: {english_font_name}", "INFO")
        except Exception as e:
            log(f"注册英文字体时出错: {str(e)}", "ERROR")
            english_font_available = False

    # 设置字体
    if chinese_font_available or english_font_available:
        # 设置字体家族
        plt.rcParams['font.family'] = 'sans-serif'
        mpl.rcParams['font.family'] = 'sans-serif'

        # 初始化字体列表
        font_list = []

        # 添加中文字体
        if chinese_font_available:
            font_list.append(chinese_font_name)

        # 添加英文字体
        if english_font_available:
            font_list.append(english_font_name)

        # 设置字体列表
        plt.rcParams['font.sans-serif'] = font_list + plt.rcParams['font.sans-serif']
        mpl.rcParams['font.sans-serif'] = font_list + mpl.rcParams['font.sans-serif']
    else:
        # 如果没有可用的字体，回退到系统字体
        log("没有可用的字体，回退到系统字体", "WARNING")
        _use_system_fonts()

    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False
    mpl.rcParams['axes.unicode_minus'] = False

# 回退函数：使用系统字体
def _use_system_fonts():
    system = platform.system()
    if system == 'Windows':
        # Windows系统常用字体
        font_names = ['Microsoft YaHei', 'SimHei', 'SimSun', 'Arial Unicode MS', 'Microsoft Sans Serif']
    elif system == 'Darwin':
        # macOS系统常用字体
        font_names = ['PingFang SC', 'Heiti SC', 'STHeiti', 'Arial Unicode MS']
    else:
        # Linux系统常用字体
        font_names = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Droid Sans Fallback', 'Noto Sans CJK SC']

    # 检查字体是否可用
    all_fonts = [f.name for f in fm.fontManager.ttflist]
    available_fonts = [f for f in font_names if f.lower() in [f.name.lower() for f in fm.fontManager.ttflist]]

    if available_fonts:
        # 设置全局字体
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['font.sans-serif'] = [available_fonts[0]] + plt.rcParams['font.sans-serif']

        # 直接设置matplotlib的字体属性
        mpl.rcParams['font.family'] = 'sans-serif'
        mpl.rcParams['font.sans-serif'] = [available_fonts[0]] + mpl.rcParams['font.sans-serif']

        log(f"使用系统字体: {available_fonts[0]}", "INFO")
    else:
        # 尝试使用系统默认字体
        log("警告: 未找到支持中文的系统字体，尝试使用默认字体", "WARNING")
        if system == 'Windows':
            plt.rcParams['font.sans-serif'] = ['SimSun'] + plt.rcParams['font.sans-serif']
            mpl.rcParams['font.sans-serif'] = ['SimSun'] + mpl.rcParams['font.sans-serif']

# 向后兼容的函数
def set_chinese_font():
    """向后兼容的函数，调用新的字体设置函数"""
    return set_chart_fonts()

# 设置现代化的图表样式
def set_modern_style():
    # 设置现代化的颜色周期
    # 使用更现代的色彩方案，增强可读性和美观性
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
              '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
    plt.rcParams['axes.prop_cycle'] = cycler(color=colors)

    # 设置图表背景和网格样式
    plt.rcParams['figure.facecolor'] = 'white'
    plt.rcParams['axes.facecolor'] = 'white'
    plt.rcParams['axes.grid'] = True
    plt.rcParams['grid.alpha'] = 0.3
    plt.rcParams['grid.linestyle'] = '--'

    # 设置字体大小
    plt.rcParams['font.size'] = 11
    plt.rcParams['axes.titlesize'] = 14
    plt.rcParams['axes.labelsize'] = 12

    # 设置线条宽度和样式
    plt.rcParams['lines.linewidth'] = 2.0
    # 不使用标记点

    # 设置轴线和刻度样式
    plt.rcParams['axes.spines.top'] = False
    plt.rcParams['axes.spines.right'] = False
    plt.rcParams['xtick.direction'] = 'out'
    plt.rcParams['ytick.direction'] = 'out'
    plt.rcParams['xtick.major.size'] = 5.0
    plt.rcParams['ytick.major.size'] = 5.0

    # 设置图例样式
    plt.rcParams['legend.frameon'] = True
    plt.rcParams['legend.framealpha'] = 0.8
    plt.rcParams['legend.edgecolor'] = 'lightgray'
    plt.rcParams['legend.fancybox'] = True
    plt.rcParams['legend.shadow'] = False

# 初始化时设置字体和样式
set_chart_fonts()
set_modern_style()

# 保存图表时处理字体
def save_figure_with_chinese(fig, output, format='png'):
    """
    保存图表并确保中文显示正确

    参数:
        fig: matplotlib图表对象
        output: 输出路径或BytesIO对象
        format: 输出格式，默认为png
    """
    try:
        # 在保存前再次确认字体设置
        import os
        from ensure_fonts import load_font_settings, FONT_INFO, get_font_dir

        # 加载字体设置
        chinese_font_key, english_font_key = load_font_settings()

        # 获取字体路径
        font_dir = get_font_dir()
        chinese_font_path = os.path.join(font_dir, FONT_INFO[chinese_font_key]['filename'])

        # 检查中文字体是否存在
        if os.path.exists(chinese_font_path):
            # 使用选定的中文字体
            font_prop = fm.FontProperties(fname=chinese_font_path)
        else:
            # 回退到系统字体
            system = platform.system()
            if system == 'Windows':
                font_prop = fm.FontProperties(family='Microsoft YaHei')
            else:
                # 其他系统尝试使用默认字体
                font_prop = fm.FontProperties()

        # 更新图表中的所有文本元素的字体
        for ax in fig.get_axes():
            for text in ax.get_xticklabels() + ax.get_yticklabels():
                text.set_fontproperties(font_prop)

            # 更新标题和轴标签
            title = ax.get_title()
            xlabel = ax.get_xlabel()
            ylabel = ax.get_ylabel()

            ax.set_title(title, fontproperties=font_prop)
            ax.set_xlabel(xlabel, fontproperties=font_prop)
            ax.set_ylabel(ylabel, fontproperties=font_prop)

            # 更新图例文本
            legend = ax.get_legend()
            if legend is not None:
                for text in legend.get_texts():
                    text.set_fontproperties(font_prop)

        # 保存图表
        fig.savefig(output, format=format, bbox_inches='tight')
    except Exception as e:
        # 记录错误
        log(f"警告: 保存图表时出错: {str(e)}", "WARNING")
        # 如果出错，回退到原始的保存方式
        try:
            fig.savefig(output, format=format)
        except Exception as e2:
            log(f"错误: 保存图表失败: {str(e2)}", "ERROR")

# 导入优化的绘图函数
from optimized_plotter import OptimizedPlotter

# 创建优化绘图器实例
optimized_plotter = OptimizedPlotter()

def plot_with_timestamps(timestamps, data, label=None, valid_timestamp_count=None):
    """
    使用时间戳作为x轴绘制数据（如果可能），否则使用索引。
    此函数现在使用优化的绘图器实现。

    参数:
        timestamps: 时间戳列表或数组
        data: 要绘制的数据点列表或数组
        label: 图表图例的标签
        valid_timestamp_count: 有效时间戳的行数

    返回:
        None
    """
    # 使用优化的绘图函数
    return optimized_plotter.plot_with_timestamps_optimized(timestamps, data, label, valid_timestamp_count)



def format_time_axis(timestamps, valid_timestamp_count=None):
    """
    格式化时间轴仅显示小时:分钟:秒。
    此函数现在使用优化的绘图器实现。

    参数:
        timestamps: 时间戳列表或数组
        valid_timestamp_count: 有效时间戳的行数

    返回:
        bool: 如果应用了格式化返回Ture，否则返回False
    """
    # 使用优化的时间轴格式化函数
    return optimized_plotter.format_time_axis_optimized(timestamps, valid_timestamp_count)



class ChartGenerator:
    """用于从处理数据生成图表的类。"""

    def __init__(self):
        """初始化图表生成器。"""
        self.dpi = 120  # 提高DPI以获得更清晰的图表
        self.figure_size = (12, 7)  # 稍微增大图表尺寸以获得更好的视觉效果
        self.progress_callback = None

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    def update_progress(self, message, percent=None):
        """更新进度信息"""
        # 只在关键节点记录日志，减少日志输出
        if message and ("错误" in message or "警告" in message):
            log(message, "WARNING" if "警告" in message else "ERROR")
        # 仍然发送进度回调，但减少日志记录
        if self.progress_callback:
            self.progress_callback(message, percent)

    def create_cpu_chart(self, result, output_path=None):
        """
        为单个文件创建CPU使用率图表。

        参数:
            result: 单个结果字典
            output_path (str, 可选): 保存图表图像的路径

        返回:
            str 或 None: 如果提供了output_path，则返回保存的图像路径
        """
        try:
            # 强制关闭所有图表，确保没有残留状态
            import matplotlib.pyplot as plt
            plt.close('all')

            # 强制清理matplotlib缓存
            plt.clf()
            plt.cla()

            log(f"开始为文件 {result['File']} 创建CPU图表")

            # 先关闭之前的图表，避免多个文件的数据被绘制到同一个图表上
            plt.close('all')

            # 创建带有现代化样式的图表
            fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)

            # 设置图表样式
            set_modern_style()

            # 添加浅色背景渐变效果
            ax.set_facecolor('#f8f9fa')  # 设置浅色背景
            fig.patch.set_facecolor('white')  # 设置图表背景为白色

            # 获取IP地址作为标题前缀
            ip_prefix = ""
            if 'IP Address' in result:
                ip = result['IP Address']
                if ip and ip != '未知':
                    ip_prefix = f"[{ip}] "

            # 使用不同的线型样式
            line_styles = ['-', '--', '-.', ':']

            # 处理单个结果对象
            file_name = result['File']
            cpu_data = result['Raw Data']['CPU']
            timestamps = result['Raw Data'].get('Timestamps', {}).get('CPU', [])

            # 记录日志
            log(f"CPU图表: 处理文件 {file_name}, 数据点数: {len(cpu_data)}, 时间戳数: {len(timestamps)}", "INFO")

            # 获取时间戳行数信息
            timestamp_counts = result['Raw Data'].get('TimestampCounts', {})
            cpu_timestamp_count = timestamp_counts.get('CPU', 0)

            # 使用文件名作为图例标签
            label = file_name
            # 如果有IP地址且不是未知，添加到标签中
            if 'IP Address' in result and result['IP Address'] and result['IP Address'] != '未知':
                label = f"{result['IP Address']} - {file_name}"

            # 选择线型样式
            line_style = line_styles[0]  # 始终使用第一种线型样式

            # 传递时间戳行数信息和样式参数
            if len(timestamps) > 0 and len(cpu_data) > 0:
                # 确保时间戳和数据长度一致
                if len(timestamps) > len(cpu_data):
                    log(f"警告: 时间戳数量({len(timestamps)})大于CPU数据点数({len(cpu_data)})，截断时间戳")
                    timestamps = timestamps[:len(cpu_data)]
                elif len(timestamps) < len(cpu_data):
                    log(f"警告: 时间戳数量({len(timestamps)})小于CPU数据点数({len(cpu_data)})，截断CPU数据")
                    cpu_data = cpu_data[:len(timestamps)]

                # 确保数据长度一致后再次检查
                if len(timestamps) != len(cpu_data):
                    log(f"警告: 即使截断后，时间戳数量({len(timestamps)})和CPU数据点数({len(cpu_data)})仍然不匹配")
                    # 取两者的最小值作为共同长度
                    min_len = min(len(timestamps), len(cpu_data))
                    timestamps = timestamps[:min_len]
                    cpu_data = cpu_data[:min_len]

                try:
                    if hasattr(timestamps[0], 'strftime'):
                        plt.plot(timestamps, cpu_data, label=label, linestyle=line_style,
                                 linewidth=2.0, alpha=0.9)
                    else:
                        plt.plot(range(len(cpu_data)), cpu_data, label=label, linestyle=line_style,
                                 linewidth=2.0, alpha=0.9)
                except Exception as e:
                    log(f"CPU图表: 绘制数据时出错: {e}", "ERROR")

            # Format time axis if timestamps are datetime objects
            if 'Timestamps' in result['Raw Data'] and 'CPU' in result['Raw Data']['Timestamps']:
                # 传递时间戳行数信息
                timestamp_counts = result['Raw Data'].get('TimestampCounts', {})
                cpu_timestamp_count = timestamp_counts.get('CPU', 0)

                # 使用优化的时间轴格式化函数
                try:
                    if len(timestamps) > 0:
                        # 不记录时间轴格式化日志
                        format_time_axis(timestamps, valid_timestamp_count=cpu_timestamp_count)
                    # 不记录时间戳列表为空的警告
                except Exception as e:
                    # 只在出错时记录日志
                    log(f"CPU图表: 格式化时间轴时出错", "ERROR")

            # 设置标题和轴标签，使用更现代的字体样式
            plt.title(f'{ip_prefix}CPU使用率 (%)', fontweight='bold', pad=15)
            plt.xlabel('时间', fontweight='bold', labelpad=10)
            plt.ylabel('CPU使用率 (%)', fontweight='bold', labelpad=10)

            # 设置纵坐标范围并添加水平参考线
            plt.ylim(0, 100)  # 设置纵坐标范围为0-100%

            # 添加水平参考线，稍微改变颜色和样式以增强可读性
            plt.axhline(y=25, color='#17a2b8', linestyle='--', alpha=0.3, linewidth=1)
            plt.axhline(y=50, color='#ffc107', linestyle='--', alpha=0.5, linewidth=1)
            plt.axhline(y=75, color='#dc3545', linestyle='--', alpha=0.3, linewidth=1)

            # 设置x轴范围，确保数据线从横坐标时间范围的起点开始
            # 检查是否有时间戳数据
            if 'Timestamps' in result['Raw Data'] and 'CPU' in result['Raw Data']['Timestamps']:
                if len(timestamps) > 0 and hasattr(timestamps[0], 'strftime'):
                    # 对于时间戳数据，设置起点为第一个时间戳
                    try:
                        min_time = min(timestamps)
                        max_time = max(timestamps)
                        plt.xlim(left=min_time)
                        log(f"CPU图表: 设置时间轴范围从 {min_time} 到 {max_time}", "INFO")
                    except Exception as e:
                        log(f"CPU图表: 设置时间轴范围时出错: {e}", "ERROR")
                        # 如果出错，不设置范围
                else:
                    # 如果不是时间戳数据，则设置x轴从0开始
                    plt.xlim(left=0)
                    log("CPU图表: 非时间戳数据，设置x轴从0开始", "INFO")
            else:
                # 如果没有时间戳数据，则设置x轴从0开始
                plt.xlim(left=0)
                log("CPU图表: 没有时间戳数据，设置x轴从0开始", "INFO")

            # 设置更美观的图例
            legend = plt.legend(loc='upper right', framealpha=0.9, fancybox=True, shadow=True,
                               fontsize=10, ncol=1)
            legend.get_frame().set_edgecolor('lightgray')

            # 添加网格线以提高可读性
            plt.grid(True, linestyle='--', alpha=0.7, color='#cccccc')

            # 添加文本注释，显示数据时间范围
            if 'Timestamps' in result['Raw Data'] and 'CPU' in result['Raw Data']['Timestamps']:
                if len(timestamps) > 1 and hasattr(timestamps[0], 'strftime'):
                    try:
                        start_time = timestamps[0].strftime('%Y-%m-%d %H:%M:%S')
                        # 安全地访问最后一个时间戳
                        last_idx = len(timestamps) - 1
                        end_time = timestamps[last_idx].strftime('%Y-%m-%d %H:%M:%S')
                        plt.figtext(0.5, 0.01, f'数据时间范围: {start_time} 至 {end_time}',
                                   ha='center', fontsize=9, style='italic', color='#666666')
                    except Exception as e:
                        log(f"CPU图表: 格式化时间范围文本时出错: {e}", "ERROR")

            plt.tight_layout(pad=2.0)  # 增加填充以留出更多空间

            if output_path:
                try:
                    save_figure_with_chinese(plt.gcf(), output_path)
                    log(f"CPU图表: 已保存到 {output_path}", "INFO")
                    plt.close()
                    return output_path
                except Exception as e:
                    log(f"CPU图表: 保存图表时出错: {e}", "ERROR")
                    plt.close()
                    # 即使出错也返回路径，确保返回值类型一致
                    return output_path
            else:
                try:
                    log("CPU图表: 创建内存图表...")
                    img_data = io.BytesIO()
                    save_figure_with_chinese(plt.gcf(), img_data)
                    plt.close()
                    img_data.seek(0)
                    log("CPU图表: 内存图表创建成功")
                    return img_data
                except Exception as e:
                    log(f"CPU图表: 创建内存图表时出错: {e}", "ERROR")
                    plt.close()
                    # 创建一个空的BytesIO对象作为备用
                    log("CPU图表: 返回空的BytesIO对象")
                    empty_data = io.BytesIO()
                    return empty_data
        except Exception as e:
            log_exception(e, "CPU图表创建过程中")
            # 如果出错，尝试创建一个空白图表
            if output_path:
                try:
                    # 创建一个空白图表
                    plt.close('all')
                    plt.figure(figsize=self.figure_size, dpi=self.dpi)
                    plt.text(0.5, 0.5, f"CPU图表创建失败: {str(e)}",
                             horizontalalignment='center', verticalalignment='center',
                             transform=plt.gca().transAxes, fontsize=12, color='red')
                    plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight')
                    log(f"CPU图表: 创建失败，已生成错误图表到 {output_path}", "WARNING")
                    return output_path
                except Exception as e2:
                    log(f"CPU图表: 创建错误图表也失败: {e2}", "ERROR")

        return None

    def create_memory_chart(self, result, output_path=None):
        """
        为单个文件创建内存使用率图表。

        参数:
            result: 单个结果字典
            output_path (str, 可选): 保存图表图像的路径

        返回:
            str 或 None: 如果提供了output_path，则返回保存的图像路径
        """
        try:
            # 强制关闭所有图表，确保没有残留状态
            import matplotlib.pyplot as plt
            plt.close('all')

            # 强制清理matplotlib缓存
            plt.clf()
            plt.cla()

            log(f"开始为文件 {result['File']} 创建内存图表")

            # 创建带有现代化样式的图表
            fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)

            # 添加浅色背景渐变效果 - 使用不同的背景色区分CPU和内存图表
            ax.set_facecolor('#f0f7ff')  # 设置浅蓝色背景区分内存图表
            fig.patch.set_facecolor('white')  # 设置图表背景为白色

            # 获取IP地址作为标题前缀
            ip_prefix = ""
            if 'IP Address' in result:
                ip = result['IP Address']
                if ip and ip != '未知':
                    ip_prefix = f"[{ip}] "

            # 使用不同的线型样式
            line_styles = ['-', '--', '-.', ':']

            # 处理单个结果对象
            file_name = result['File']
            mem_data = result['Raw Data']['Memory']
            timestamps = result['Raw Data'].get('Timestamps', {}).get('MEM', [])

            # 获取时间戳行数信息
            timestamp_counts = result['Raw Data'].get('TimestampCounts', {})
            mem_timestamp_count = timestamp_counts.get('MEM', 0)

            # 使用文件名作为图例标签
            label = file_name
            # 如果有IP地址且不是未知，添加到标签中
            if 'IP Address' in result and result['IP Address'] and result['IP Address'] != '未知':
                label = f"{result['IP Address']} - {file_name}"

            # 选择线型样式
            line_style = line_styles[0]  # 始终使用第一种线型样式

            # 传递时间戳行数信息和样式参数
            if len(timestamps) > 0 and len(mem_data) > 0:
                # 确保时间戳和数据长度一致
                if len(timestamps) > len(mem_data):
                    log(f"警告: 时间戳数量({len(timestamps)})大于内存数据点数({len(mem_data)})，截断时间戳")
                    timestamps = timestamps[:len(mem_data)]
                elif len(timestamps) < len(mem_data):
                    log(f"警告: 时间戳数量({len(timestamps)})小于内存数据点数({len(mem_data)})，截断内存数据")
                    mem_data = mem_data[:len(timestamps)]

            # 确保数据长度一致后再次检查
            if len(timestamps) != len(mem_data):
                log(f"警告: 即使截断后，时间戳数量({len(timestamps)})和内存数据点数({len(mem_data)})仍然不匹配")
                # 取两者的最小值作为共同长度
                min_len = min(len(timestamps), len(mem_data))
                timestamps = timestamps[:min_len]
                mem_data = mem_data[:min_len]

            try:
                if hasattr(timestamps[0], 'strftime'):
                    plt.plot(timestamps, mem_data, label=label, linestyle=line_style,
                             linewidth=2.0, alpha=0.9)
                else:
                    plt.plot(range(len(mem_data)), mem_data, label=label, linestyle=line_style,
                             linewidth=2.0, alpha=0.9)
            except Exception as e:
                log(f"内存图表: 绘制数据时出错: {e}", "ERROR")

            # Format time axis if timestamps are datetime objects
            if 'Timestamps' in result['Raw Data'] and 'MEM' in result['Raw Data']['Timestamps']:
                # 传递时间戳行数信息
                if len(timestamps) > 0:
                    format_time_axis(timestamps, valid_timestamp_count=mem_timestamp_count)

            # 设置标题和轴标签，使用更现代的字体样式
            plt.title(f'{ip_prefix}内存使用率 (%)', fontweight='bold', pad=15)
            plt.xlabel('时间', fontweight='bold', labelpad=10)
            plt.ylabel('内存使用率 (%)', fontweight='bold', labelpad=10)

            # 设置纵坐标范围并添加水平参考线
            plt.ylim(0, 100)  # 设置纵坐标范围为0-100%

            # 添加水平参考线，使用蓝色系与内存主题相匹配
            plt.axhline(y=25, color='#007bff', linestyle='--', alpha=0.3, linewidth=1)
            plt.axhline(y=50, color='#0056b3', linestyle='--', alpha=0.5, linewidth=1)
            plt.axhline(y=75, color='#003580', linestyle='--', alpha=0.3, linewidth=1)

            # 设置x轴范围，确保数据线从横坐标时间范围的起点开始
            # 检查是否有时间戳数据
            if 'Timestamps' in result['Raw Data'] and 'MEM' in result['Raw Data']['Timestamps']:
                if len(timestamps) > 0 and hasattr(timestamps[0], 'strftime'):
                    # 对于时间戳数据，设置起点为第一个时间戳
                    try:
                        min_time = min(timestamps)
                        max_time = max(timestamps)
                        plt.xlim(left=min_time)
                        log(f"设置内存图表时间轴范围从 {min_time} 到 {max_time}","INFO")
                    except Exception as e:
                        log(f"设置内存图表时间轴范围时出错: {e}","ERROR")
                        # 如果出错，不设置范围
                else:
                    # 如果不是时间戳数据，则设置x轴从0开始
                    plt.xlim(left=0)
                    log("非时间戳数据，设置内存图表x轴从0开始")
            else:
                # 如果没有时间戳数据，则设置x轴从0开始
                plt.xlim(left=0)
                log("没有时间戳数据，设置内存图表x轴从0开始", "DEBUG")

            # 设置更美观的图例
            legend = plt.legend(loc='upper right', framealpha=0.9, fancybox=True, shadow=True,
                               fontsize=10, ncol=1)
            legend.get_frame().set_edgecolor('lightgray')

            # 添加网格线以提高可读性
            plt.grid(True, linestyle='--', alpha=0.7, color='#cccccc')

            # 添加文本注释，显示数据时间范围
            if 'Timestamps' in result['Raw Data'] and 'MEM' in result['Raw Data']['Timestamps']:
                if len(timestamps) > 1 and hasattr(timestamps[0], 'strftime'):
                    try:
                        start_time = timestamps[0].strftime('%Y-%m-%d %H:%M:%S')
                        # 安全地访问最后一个时间戳
                        last_idx = len(timestamps) - 1
                        end_time = timestamps[last_idx].strftime('%Y-%m-%d %H:%M:%S')
                        plt.figtext(0.5, 0.01, f'数据时间范围: {start_time} 至 {end_time}',
                                   ha='center', fontsize=9, style='italic', color='#666666')
                    except Exception as e:
                        log(f"格式化内存图表时间范围文本时出错: {e}", "ERROR")

            plt.tight_layout(pad=2.0)  # 增加填充以留出更多空间
        except Exception as e:
            log_exception(e, "内存图表创建过程中")
            # 如果出错，尝试创建一个空白图表
            plt.close('all')
            plt.figure(figsize=self.figure_size, dpi=self.dpi)
            plt.text(0.5, 0.5, f"内存图表创建失败: {str(e)}",
                     horizontalalignment='center', verticalalignment='center',
                     transform=plt.gca().transAxes, fontsize=12, color='red')

        if output_path:
            try:
                save_figure_with_chinese(plt.gcf(), output_path)
                log(f"内存图表: 已保存到 {output_path}", "INFO")
                plt.close()
                return output_path
            except Exception as e:
                log(f"内存图表: 保存图表时出错: {e}", "ERROR")
                plt.close()
                # 即使出错也返回路径，确保返回值类型一致
                return output_path
        else:
            try:
                log("内存图表: 创建内存图表...")
                img_data = io.BytesIO()
                save_figure_with_chinese(plt.gcf(), img_data)
                plt.close()
                img_data.seek(0)
                log("内存图表: 内存图表创建成功")
                return img_data
            except Exception as e:
                log(f"内存图表: 创建内存图表时出错: {e}", "ERROR")
                plt.close()
                # 创建一个空的BytesIO对象作为备用
                log("内存图表: 返回空的BytesIO对象")
                empty_data = io.BytesIO()
                return empty_data

    def create_disk_chart(self, result, output_path=None):
        """
        创建磁盘繁忙度图表，与CPU和内存图表保持一致的样式。

        参数:
            result: 单个结果字典
            output_path (str, 可选): 保存图表图像的路径

        返回:
            str 或 None: 如果提供了output_path，则返回保存的图像路径
        """
        try:
            # 强制关闭所有图表，确保没有残留状态
            import matplotlib.pyplot as plt
            plt.close('all')

            # 强制清理matplotlib缓存
            plt.clf()
            plt.cla()

            log(f"开始为文件 {result['File']} 创建磁盘图表")

            # 创建带有现代化样式的图表
            fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)

            # 添加浅色背景渐变效果 - 使用不同的背景色区分磁盘图表
            ax.set_facecolor('#f5f5dc')  # 设置浅粉色背景区分磁盘图表
            fig.patch.set_facecolor('white')  # 设置图表背景为白色

            # 获取IP地址作为标题前缀
            ip_prefix = ""
            if 'IP Address' in result:
                ip = result['IP Address']
                if ip and ip != '未知':
                    ip_prefix = f"[{ip}] "

            # 使用时间序列绘制磁盘使用率图表，与CPU和内存图表保持一致
            file_name = result['File']
            # 获取磁盘数据
            disk_data = []
            disk_col = result.get('Max Disk Column', '')

            # 检查Raw Data是否存在
            if 'Raw Data' not in result:
                log(f"错误: 文件 {file_name} 的结果中没有Raw Data字段", "ERROR")
                raise ValueError(f"文件 {file_name} 的结果中没有Raw Data字段")

            # 检查Disk数据是否存在
            if 'Disk' not in result['Raw Data']:
                log(f"错误: 文件 {file_name} 的结果中没有磁盘数据", "ERROR")
                raise ValueError(f"文件 {file_name} 的结果中没有磁盘数据")

            # 记录磁盘数据的键
            disk_keys = list(result['Raw Data']['Disk'].keys())
            log(f"文件 {file_name} 的磁盘数据键: {disk_keys}", "INFO")

            if disk_col and disk_col in result['Raw Data']['Disk']:
                disk_data = result['Raw Data']['Disk'][disk_col]
                log(f"使用指定的磁盘列 {disk_col}, 数据长度: {len(disk_data)}", "INFO")
            else:
                # 如果没有指定列或该列不存在，尝试使用第一个可用的磁盘列
                for col, data in result['Raw Data']['Disk'].items():
                    if col.lower() != 'totals' and data:
                        disk_data = data
                        disk_col = col
                        log(f"使用第一个可用的磁盘列 {disk_col}, 数据长度: {len(disk_data)}", "INFO")
                        break

            # 获取时间戳
            timestamps = result['Raw Data'].get('Timestamps', {}).get('DISK', [])
            log(f"时间戳长度: {len(timestamps)}", "INFO")

            # 获取时间戳行数信息
            timestamp_counts = result['Raw Data'].get('TimestampCounts', {})
            disk_timestamp_count = timestamp_counts.get('DISK', 0)
            log(f"磁盘时间戳行数: {disk_timestamp_count}", "INFO")

            # 确保磁盘数据有效
            if not disk_data:
                log(f"警告: 文件 {file_name} 没有有效的磁盘数据", "WARNING")
                # 创建一个空的数据集，以便仍然可以生成图表
                disk_data = [0]
                if not timestamps:
                    timestamps = [0]

            # 绘制磁盘使用率图表
            if disk_data:
                # 使用文件名和磁盘列作为图例标签
                label = file_name
                if disk_col:
                    # 如果有IP地址且不是未知，添加到标签中
                    if 'IP Address' in result and result['IP Address'] and result['IP Address'] != '未知':
                        label = f"{result['IP Address']} - {file_name} ({disk_col})"
                    else:
                        label = f"{file_name} ({disk_col})"

                # 确保时间戳和数据长度匹配
                if len(timestamps) != len(disk_data):
                    log(f"警告: 时间戳数量({len(timestamps)})和磁盘数据点数({len(disk_data)})不匹配，将进行截断", "WARNING")
                    # 取两者的最小值作为共同长度
                    min_len = min(len(timestamps), len(disk_data))
                    timestamps = timestamps[:min_len]
                    disk_data = disk_data[:min_len]

                # 传递时间戳行数信息
                try:
                    plot_with_timestamps(timestamps, disk_data, label=label, valid_timestamp_count=disk_timestamp_count)
                    log(f"成功绘制磁盘图表: {label}", "INFO")
                except Exception as e:
                    log(f"绘制磁盘图表时出错: {e}", "ERROR")
                    import traceback
                    log(traceback.format_exc(), "ERROR")

            # 格式化时间轴（如果有时间戳）
            try:
                if 'Timestamps' in result['Raw Data'] and 'DISK' in result['Raw Data']['Timestamps']:
                    # 传递时间戳行数信息
                    if len(timestamps) > 0:
                        format_time_axis(timestamps, valid_timestamp_count=disk_timestamp_count)
                        log(f"成功格式化时间轴", "INFO")
            except Exception as e:
                log(f"格式化时间轴时出错: {e}", "ERROR")
                import traceback
                log(traceback.format_exc(), "ERROR")

            # 设置图表标题和轴标签
            try:
                plt.title(f'{ip_prefix}磁盘繁忙度 (%)', fontweight='bold', pad=15)
                plt.xlabel('时间', fontweight='bold', labelpad=10)
                plt.ylabel('磁盘繁忙度 (%)', fontweight='bold', labelpad=10)
                plt.ylim(0, 100)  # 设置纵坐标范围为0-100%
                log("成功设置图表标题和轴标签", "INFO")
            except Exception as e:
                log(f"设置图表标题和轴标签时出错: {e}", "ERROR")

            # 设置x轴范围，确保数据线从横坐标时间范围的起点开始
            try:
                # 检查是否有时间戳数据
                if 'Timestamps' in result['Raw Data'] and 'DISK' in result['Raw Data']['Timestamps']:
                    if len(timestamps) > 0 and hasattr(timestamps[0], 'strftime'):
                        # 对于时间戳数据，设置起点为第一个时间戳
                        try:
                            min_time = min(timestamps)
                            max_time = max(timestamps)
                            plt.xlim(left=min_time)
                            log(f"设置磁盘图表时间轴范围从 {min_time} 到 {max_time}", "INFO")
                        except Exception as e:
                            log(f"设置磁盘图表时间轴范围时出错: {e}", "ERROR")
                            # 如果出错，不设置范围
                    else:
                        # 如果不是时间戳数据，则设置x轴从0开始
                        plt.xlim(left=0)
                        log("非时间戳数据，设置磁盘图表x轴从0开始", "INFO")
                else:
                    # 如果没有时间戳数据，则设置x轴从0开始
                    plt.xlim(left=0)
                    log("没有时间戳数据，设置磁盘图表x轴从0开始", "INFO")
            except Exception as e:
                log(f"设置x轴范围时出错: {e}", "ERROR")

            # 添加水平参考线
            try:
                # 添加水平参考线，使用绿色系与磁盘主题相匹配
                plt.axhline(y=25, color='#2e8b57', linestyle='--', alpha=0.3, linewidth=1)
                plt.axhline(y=50, color='#3cb371', linestyle='--', alpha=0.5, linewidth=1)
                plt.axhline(y=75, color='#66cdaa', linestyle='--', alpha=0.3, linewidth=1)
                log("成功添加水平参考线", "INFO")
            except Exception as e:
                log(f"添加水平参考线时出错: {e}", "ERROR")

            # 设置图例
            try:
                # 设置更美观的图例
                legend = plt.legend(loc='upper right', framealpha=0.9, fancybox=True, shadow=True,
                                   fontsize=10, ncol=1)
                legend.get_frame().set_edgecolor('lightgray')
                log("成功设置图例", "INFO")
            except Exception as e:
                log(f"设置图例时出错: {e}", "ERROR")

            # 添加网格线
            try:
                # 添加网格线以提高可读性
                plt.grid(True, linestyle='--', alpha=0.7, color='#cccccc')
                log("成功添加网格线", "INFO")
            except Exception as e:
                log(f"添加网格线时出错: {e}", "ERROR")

            # 添加时间范围注释
            try:
                # 添加文本注释，显示数据时间范围
                if 'Timestamps' in result['Raw Data'] and 'DISK' in result['Raw Data']['Timestamps']:
                    if len(timestamps) > 1 and hasattr(timestamps[0], 'strftime'):
                        try:
                            start_time = timestamps[0].strftime('%Y-%m-%d %H:%M:%S')
                            # 安全地访问最后一个时间戳
                            last_idx = len(timestamps) - 1
                            end_time = timestamps[last_idx].strftime('%Y-%m-%d %H:%M:%S')
                            plt.figtext(0.5, 0.01, f'数据时间范围: {start_time} 至 {end_time}',
                                       ha='center', fontsize=9, style='italic', color='#666666')
                            log(f"成功添加时间范围注释: {start_time} 至 {end_time}", "INFO")
                        except Exception as e:
                            log(f"格式化磁盘图表时间范围文本时出错: {e}", "ERROR")
            except Exception as e:
                log(f"添加时间范围注释时出错: {e}", "ERROR")

            # 调整布局
            try:
                plt.tight_layout(pad=2.0)  # 增加填充以留出更多空间
                log("成功调整布局", "INFO")
            except Exception as e:
                log(f"调整布局时出错: {e}", "ERROR")
        except Exception as e:
            log_exception(e, "磁盘图表创建过程中")
            # 如果出错，尝试创建一个空白图表
            try:
                plt.close('all')
                plt.figure(figsize=self.figure_size, dpi=self.dpi)
                plt.text(0.5, 0.5, f"磁盘图表创建失败: {str(e)}",
                         horizontalalignment='center', verticalalignment='center',
                         transform=plt.gca().transAxes, fontsize=12, color='red')
                log("创建了带有错误信息的空白图表", "INFO")
            except Exception as e2:
                log(f"创建空白错误图表时出错: {e2}", "ERROR")
                # 尝试再次创建一个空白图表，使用最简单的方式
                try:
                    plt.close('all')
                    plt.figure()
                    plt.text(0.5, 0.5, "磁盘图表创建失败",
                             horizontalalignment='center', verticalalignment='center')
                except Exception as e3:
                    log(f"第二次创建空白错误图表时出错: {e3}", "ERROR")

        # 保存图表
        if output_path:
            try:
                save_figure_with_chinese(plt.gcf(), output_path)
                log(f"磁盘图表: 已保存到 {output_path}", "INFO")
                plt.close()
                return output_path
            except Exception as e:
                log(f"磁盘图表: 保存图表时出错: {e}", "ERROR")
                import traceback
                log(traceback.format_exc(), "ERROR")

                # 尝试使用更简单的方式保存
                try:
                    plt.savefig(output_path)
                    log(f"磁盘图表: 使用备用方法保存到 {output_path}", "INFO")
                except Exception as e2:
                    log(f"磁盘图表: 备用保存方法也失败: {e2}", "ERROR")

                plt.close()
                # 即使出错也返回路径，确保返回值类型一致
                return output_path
        else:
            try:
                log("磁盘图表: 创建内存图表...")
                img_data = io.BytesIO()
                save_figure_with_chinese(plt.gcf(), img_data)
                plt.close()
                img_data.seek(0)
                log("磁盘图表: 内存图表创建成功")
                return img_data
            except Exception as e:
                log(f"磁盘图表: 创建内存图表时出错: {e}", "ERROR")
                import traceback
                log(traceback.format_exc(), "ERROR")

                # 尝试使用更简单的方式保存
                try:
                    img_data = io.BytesIO()
                    plt.savefig(img_data, format='png')
                    plt.close()
                    img_data.seek(0)
                    log("磁盘图表: 使用备用方法创建内存图表成功")
                    return img_data
                except Exception as e2:
                    log(f"磁盘图表: 备用创建内存图表方法也失败: {e2}", "ERROR")
                    plt.close()

                # 创建一个空的BytesIO对象作为备用
                log("磁盘图表: 返回空的BytesIO对象")
                empty_data = io.BytesIO()
                return empty_data

    def add_charts_to_excel(self, results, excel_path):
        """
        将图表添加到现有的Excel文件中。
        当处理多个文件时，每个文件创建一个独立的sheet。

        参数:
            results (list): 结果字典列表
            excel_path (str): Excel文件的路径

        返回:
            str: 更新后的Excel文件路径
        """
        try:
            # 加载Excel工作簿 - 不记录日志
            wb = load_workbook(excel_path)

            # 删除旧的单独图表工作表（如果存在） - 不记录日志
            for old_sheet_name in ['CPU_Chart', 'Memory_Chart', 'Disk_Chart', 'Charts']:
                if old_sheet_name in wb.sheetnames:
                    del wb[old_sheet_name]

            # 处理每个文件的结果
            for i, result in enumerate(results):
                # 为每个文件创建一个独立的sheet
                file_name = result['File']
                # 将文件名转换为有效的sheet名（去除特殊字符）
                sheet_name = f"Chart_{i+1}_{file_name[:20]}"
                # 替换无效的sheet名字符
                sheet_name = ''.join(c if c.isalnum() or c in '_- ' else '_' for c in sheet_name)
                # 确保名称不超过31个字符（Excel限制）
                sheet_name = sheet_name[:31]

                # 不记录创建工作表的日志

                # 创建新的工作表
                if sheet_name in wb.sheetnames:
                    # 如果已存在同名工作表，删除它
                    del wb[sheet_name]
                ws = wb.create_sheet(sheet_name)

                # 设置列宽和行高，以便图表有足够的空间
                for col in range(1, 20):  # A到T列
                    ws.column_dimensions[chr(64 + col)].width = 15  # 设置列宽为15个字符

                for row in range(1, 60):  # 1到60行
                    ws.row_dimensions[row].height = 20  # 设置行高

                # 设置文件信息标题
                ws['A1'] = f'文件: {file_name}'
                ws['A1'].font = Font(bold=True, size=14)
                ws['A2'] = f'IP地址: {result["IP Address"]}'
                ws['A2'].font = Font(bold=True, size=12)

                # 为当前文件生成图表
                # 传递单个结果对象而不是列表
                # 创建临时文件路径
                import tempfile
                temp_dir = tempfile.mkdtemp()

                log(f"生成 {file_name} 的CPU图表...")
                cpu_path = os.path.join(temp_dir, f"cpu_{i}.png")
                cpu_img = self.create_cpu_chart(result, cpu_path)
                log(f"CPU图表生成结果路径: {cpu_img}")

                log(f"生成 {file_name} 的内存图表...")
                mem_path = os.path.join(temp_dir, f"mem_{i}.png")
                mem_img = self.create_memory_chart(result, mem_path)
                log(f"内存图表生成结果路径: {mem_img}")

                log(f"生成 {file_name} 的磁盘图表...")
                disk_path = os.path.join(temp_dir, f"disk_{i}.png")
                disk_img = self.create_disk_chart(result, disk_path)
                log(f"磁盘图表生成结果路径: {disk_img}")

                # 设置图表标题
                ws['A4'] = 'CPU使用率图表'
                ws['A4'].font = Font(bold=True, size=12)
                ws['A30'] = '内存使用率图表'
                ws['A30'].font = Font(bold=True, size=12)
                ws['A56'] = '磁盘繁忙度图表'
                ws['A56'].font = Font(bold=True, size=12)

                # 调整图表大小和位置
                try:
                    # 添加CPU图表
                    log(f"添加CPU图表到Excel, 图表类型: {type(cpu_img)}")
                    img = Image(cpu_img)
                    # 调整图表大小，保持纵横比
                    img.width = 800  # 像素
                    img.height = 450  # 像素
                    ws.add_image(img, 'A5')  # 从第5行开始
                    log("添加CPU图表成功")
                except Exception as e:
                    log(f"添加CPU图表到Excel时出错: {e}", "ERROR")

                try:
                    # 添加内存图表
                    log(f"添加内存图表到Excel, 图表类型: {type(mem_img)}")
                    img = Image(mem_img)
                    img.width = 800  # 像素
                    img.height = 450  # 像素
                    ws.add_image(img, 'A31')  # 从第31行开始
                    log("添加内存图表成功")
                except Exception as e:
                    log(f"添加内存图表到Excel时出错: {e}", "ERROR")

                try:
                    # 添加磁盘图表
                    log(f"添加磁盘图表到Excel, 图表类型: {type(disk_img)}")
                    img = Image(disk_img)
                    img.width = 800  # 像素
                    img.height = 450  # 像素
                    ws.add_image(img, 'A57')  # 从第57行开始
                    log("添加磁盘图表成功")
                except Exception as e:
                    log(f"添加磁盘图表到Excel时出错: {e}", "ERROR")

            # 保存工作簿 - 减少日志记录
            try:
                # 不记录尝试保存的日志
                wb.save(excel_path)
                # 只记录一条成功日志
                log("图表添加完成")
            except Exception as save_error:
                # 只记录简洁的错误信息
                log(f"保存Excel文件时出错", "ERROR")
                # 尝试关闭Excel工作簿对象
                try:
                    wb._archive.close()
                    log("已关闭Excel工作簿对象", "INFO")
                except Exception as close_error:
                    log(f"关闭Excel工作簿对象时出错: {close_error}", "ERROR")
                # 即使保存失败，也返回路径和日志
                logs = get_logs()
                return excel_path, logs

            # 清理资源
            try:
                # 关闭Excel工作簿对象
                wb._archive.close()
                log("已关闭Excel工作簿对象", "INFO")

                # 清理临时目录
                try:
                    import shutil
                    for i, result in enumerate(results):
                        temp_dir = tempfile.gettempdir()
                        cpu_path = os.path.join(temp_dir, f"cpu_{i}.png")
                        if os.path.exists(os.path.dirname(cpu_path)):
                            shutil.rmtree(os.path.dirname(cpu_path))
                            log(f"清理临时目录: {os.path.dirname(cpu_path)}")
                except Exception as e:
                    log(f"清理临时目录时出错: {e}", "ERROR")

                # 强制垃圾回收
                import gc
                gc.collect()
                log("已执行垃圾回收", "INFO")
            except Exception as cleanup_error:
                log(f"清理资源时出错: {cleanup_error}", "ERROR")

            # 返回日志信息和Excel文件路径 - 只返回最后10条日志
            logs = get_logs()[-10:] if len(get_logs()) > 10 else get_logs()
            # 不记录准备返回结果的日志
            return excel_path, logs

        except Exception as e:
            # 只记录简洁的错误信息
            log(f"添加图表时出错", "ERROR")
            # 不记录详细的错误堆栈

            # 只返回最后10条日志
            logs = get_logs()[-10:] if len(get_logs()) > 10 else get_logs()
            return None, logs
