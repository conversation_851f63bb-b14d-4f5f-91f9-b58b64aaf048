"""
Version information for the application.
"""

__version__ = "2.0.0"
__app_name__ = "性能监控数据分析器"
__release_date__ = "2025-05-06"
__author__ = "Performance Analyzer Team"

def get_version_info():
    """Return a formatted version string."""
    return f"{__app_name__} v{__version__}"

def get_about_text():
    """Return the about text for the application."""
    return f"""
{__app_name__} v{__version__}
发布日期: {__release_date__}

一个用于分析Excel文件中的CPU、内存和磁盘使用率数据的工具。
通过直观的图形界面，轻松处理和可视化性能监控数据。

V2.0版本特性：
- 统一的界面设计，提供一致的用户体验
- 优化的性能监控数据分析功能
- 改进的nmon文件分析器
- 增强的原始nmon文件分析器
- 优化的图表生成和显示
- 改进的保存选项和结果导出
- 更好的错误处理和日志记录

© 2025 {__author__}
"""
