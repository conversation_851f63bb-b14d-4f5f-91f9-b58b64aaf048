import os
import sys
import logging
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QFileDialog,
                             QProgressBar, QMessageBox, QTabWidget, QTextEdit,
                             QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
                             QProgressDialog, QDialog, QAction, QMenu, QStatusBar,
                             QFrame, QSizePolicy)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QDateTime, QSettings, QSize
from PyQt5.QtGui import QFont, QIcon, QColor, QPalette

from excel_processor import ExcelProcessor
from data_analyzer import DataAnalyzer
from chart_generator import ChartGenerator
from save_results_thread import SaveResultsThread
from save_options_dialog import SaveOptionsDialog
from settings_dialog import SettingsDialog
from logger_utils import log, log_exception, get_logs, init_logger
from exception_handler import gui_exception_handler
from version import get_about_text, __version__, __app_name__

class ProcessingThread(QThread):
    """用于处理Excel文件的线程，不会阻塞GUI。"""

    progress_signal = pyqtSignal(int, int)  # (当前, 总数)
    result_signal = pyqtSignal(list)
    error_signal = pyqtSignal(str)
    finished_signal = pyqtSignal()

    def __init__(self, mode, file_path=None, directory_path=None):
        """
        初始化处理线程。

        参数:
            mode (str): 'file'（文件）或 'directory'（目录）
            file_path (str, 可选): 单个文件的路径
            directory_path (str, 可选): 目录的路径
        """
        super().__init__()
        self.mode = mode
        self.file_path = file_path
        self.directory_path = directory_path
        self.processor = ExcelProcessor()

    def run(self):
        """运行处理线程。"""
        try:
            if self.mode == 'file':
                # 处理单个文件
                try:
                    # 初始化进度显示
                    self.progress_signal.emit(0, 1)

                    # 检查文件格式
                    if not self._check_file_format(self.file_path):
                        self.error_signal.emit(f"文件格式错误: {os.path.basename(self.file_path)} 不是有效的性能监控数据文件。")
                        return

                    result = self.processor.process_file(self.file_path)
                    # 单文件处理完成，设置进度为100%
                    self.progress_signal.emit(1, 1)
                    self.result_signal.emit([result])
                except Exception as e:
                    self.error_signal.emit(f"处理文件时出错: {str(e)}")
                    return

            elif self.mode == 'directory':
                # 获取目录中的所有Excel文件
                files = [f for f in os.listdir(self.directory_path)
                        if f.endswith(('.xlsx', '.xls')) and not f.startswith('~$')]
                total_files = len(files)

                if total_files == 0:
                    self.error_signal.emit("在选定目录中没有找到Excel文件。")
                    return

                # 发送日志信息
                self.error_signal.emit(f"找到 {total_files} 个Excel文件，开始处理...")

                results = []
                processed_files = 0
                skipped_files = 0
                error_files = 0

                for i, file in enumerate(files):
                    file_path = os.path.join(self.directory_path, file)
                    try:
                        # 检查文件格式
                        if not self._check_file_format(file_path):
                            self.error_signal.emit(f"跳过文件: {file} - 不是有效的性能监控数据文件")
                            skipped_files += 1
                            continue

                        # 处理文件
                        self.error_signal.emit(f"正在处理: {file}")
                        result = self.processor.process_file(file_path)
                        results.append(result)
                        processed_files += 1
                        self.error_signal.emit(f"成功处理: {file}")
                    except Exception as e:
                        self.error_signal.emit(f"处理 {file} 时出错: {str(e)}")
                        error_files += 1

                    # 更新进度
                    self.progress_signal.emit(i + 1, total_files)

                # 确保进度显示为100%
                self.progress_signal.emit(total_files, total_files)

                # 发送处理结果摘要
                summary = f"处理完成: 共 {total_files} 个文件, 成功 {processed_files} 个, 跳过 {skipped_files} 个, 错误 {error_files} 个"
                self.error_signal.emit(summary)

                if results:
                    self.result_signal.emit(results)
                else:
                    self.error_signal.emit("没有成功处理任何文件，无法生成报告。")
                    return

            self.finished_signal.emit()

        except Exception as e:
            self.error_signal.emit(f"处理错误: {str(e)}")

    def _check_file_format(self, file_path):
        """检查文件是否为有效的性能监控数据文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False

            # 检查文件是否为Excel文件
            if not file_path.endswith(('.xlsx', '.xls')):
                return False

            # 检查文件是否包含必要的工作表
            xls = pd.ExcelFile(file_path)
            required_sheets = ['CPU_ALL', 'MEM', 'DISKBUSY', 'ZZZZ']
            for sheet in required_sheets:
                if sheet not in xls.sheet_names:
                    return False

            return True
        except Exception:
            return False





class MainWindow(QMainWindow):
    """用于Excel分析器应用程序的主窗口。"""

    def __init__(self):
        """初始化主窗口。"""
        super().__init__()

        self.setWindowTitle("性能监控数据分析器")
        self.setMinimumSize(800, 600)

        # 设置应用程序样式
        self.set_style()

        # 初始化组件
        self.processor = ExcelProcessor()
        self.analyzer = DataAnalyzer()
        self.chart_generator = ChartGenerator()

        # 初始化UI
        self.init_ui()

        # 数据存储
        self.results = []
        self.output_path = None

    def create_menu_bar(self):
        """创建菜单栏"""
        # 创建菜单栏
        menu_bar = self.menuBar()

        # 文件菜单
        file_menu = menu_bar.addMenu("文件")

        # 打开文件动作
        open_file_action = QAction("打开文件", self)
        open_file_action.setShortcut("Ctrl+O")
        open_file_action.triggered.connect(self.select_file)
        file_menu.addAction(open_file_action)

        # 打开目录动作
        open_dir_action = QAction("打开目录", self)
        open_dir_action.setShortcut("Ctrl+D")
        open_dir_action.triggered.connect(self.select_directory)
        file_menu.addAction(open_dir_action)

        file_menu.addSeparator()

        # 选择输出文件动作
        select_output_action = QAction("选择输出文件", self)
        select_output_action.triggered.connect(self.select_output_file)
        file_menu.addAction(select_output_action)

        file_menu.addSeparator()

        # 退出动作
        exit_action = QAction("退出", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 设置菜单
        settings_menu = menu_bar.addMenu("设置")

        # 首选项动作
        preferences_action = QAction("首选项", self)
        preferences_action.triggered.connect(self.show_settings_dialog)
        settings_menu.addAction(preferences_action)

        # 帮助菜单
        help_menu = menu_bar.addMenu("帮助")

        # 关于动作
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)

    def show_settings_dialog(self):
        """显示设置对话框"""
        try:
            settings_dialog = SettingsDialog(self)
            if settings_dialog.exec_() == QDialog.Accepted:
                # 应用设置
                self.apply_settings()
        except Exception as e:
            log_exception(e, "显示设置对话框时")
            QMessageBox.critical(self, "错误", f"显示设置对话框时出错: {str(e)}")

    def show_about_dialog(self):
        """显示关于对话框"""
        QMessageBox.about(self, f"关于{__app_name__}", get_about_text())

    def apply_settings(self):
        """应用设置更改"""
        try:
            # 重新设置样式
            self.set_style()

            # 更新日志级别
            settings = QSettings("PerformanceAnalyzer", "Settings")
            log_level = settings.value("log_level", logging.INFO, type=int)
            init_logger(log_level=log_level)

            # 更新状态栏
            self.status_bar.showMessage("设置已应用")
        except Exception as e:
            log_exception(e, "应用设置时")
            QMessageBox.critical(self, "错误", f"应用设置时出错: {str(e)}")

    def apply_font_settings(self):
        """应用图表字体设置"""
        try:
            # 重新加载图表字体
            from chart_generator import set_chart_fonts
            set_chart_fonts()

            # 更新状态栏
            self.status_bar.showMessage("图表字体设置已应用")

            # 显示成功消息
            QMessageBox.information(self, "成功", "图表字体设置已更新，新生成的图表将使用选定的字体。")
        except Exception as e:
            log_exception(e, "应用图表字体设置时")
            QMessageBox.critical(self, "错误", f"应用图表字体设置时出错: {str(e)}")

    def set_style(self):
        """设置应用程序样式。"""
        # 从设置中读取样式
        settings = QSettings("PerformanceAnalyzer", "Settings")
        style_name = settings.value("ui_style", "modern_blue", type=str)
        font_size = settings.value("font_size", 9, type=int)

        # 设置字体大小
        font = QFont()
        font.setPointSize(font_size)
        QApplication.setFont(font)

        # 设置样式
        QApplication.setStyle("Fusion")

        # 根据选择的样式设置调色板
        if style_name == "modern_blue":
            # 现代蓝模式
            palette = QPalette()
            palette.setColor(QPalette.Window, QColor(240, 245, 250))
            palette.setColor(QPalette.WindowText, QColor(35, 35, 35))
            palette.setColor(QPalette.Base, QColor(255, 255, 255))
            palette.setColor(QPalette.AlternateBase, QColor(245, 249, 252))
            palette.setColor(QPalette.ToolTipBase, QColor(255, 255, 255))
            palette.setColor(QPalette.ToolTipText, QColor(35, 35, 35))
            palette.setColor(QPalette.Text, QColor(35, 35, 35))
            palette.setColor(QPalette.Button, QColor(240, 245, 250))
            palette.setColor(QPalette.ButtonText, QColor(35, 35, 35))
            palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
            palette.setColor(QPalette.Link, QColor(42, 130, 218))
            palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
            palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
            QApplication.setPalette(palette)

            # 设置样式表
            self.setStyleSheet("""
                QToolTip { color: #333333; background-color: #f0f5fa; border: 1px solid #2a82da; }
                QTabWidget::pane { border: 1px solid #c0d0e0; }
                QTabBar::tab { background: #e0e9f0; color: #333333; padding: 5px; border: 1px solid #c0d0e0; border-bottom: none; }
                QTabBar::tab:selected { background: #ffffff; color: #2a82da; }
                QTableWidget { gridline-color: #e0e0e0; }
                QHeaderView::section { background-color: #f0f5fa; color: #333333; padding: 4px; border: 1px solid #c0d0e0; }
                QPushButton { background-color: #2a82da; color: white; border: none; padding: 5px 15px; border-radius: 3px; }
                QPushButton:hover { background-color: #3a92ea; }
                QPushButton:pressed { background-color: #1a72ca; }
                QTextEdit { background-color: #ffffff; color: #333333; border: 1px solid #c0d0e0; }
            """)

        elif style_name == "light_classic":
            # 浅色经典模式
            palette = QPalette()
            QApplication.setPalette(palette)  # 使用默认调色板

            # 设置样式表
            self.setStyleSheet("""
                QTabWidget::pane { border: 1px solid #c0c0c0; }
                QTabBar::tab { background: #f0f0f0; color: #505050; padding: 5px; border: 1px solid #c0c0c0; border-bottom: none; }
                QTabBar::tab:selected { background: #ffffff; color: #000000; }
                QTableWidget { gridline-color: #d0d0d0; }
                QHeaderView::section { background-color: #f0f0f0; color: #505050; padding: 4px; border: 1px solid #c0c0c0; }
            """)

        else:  # system_default
            # 系统默认模式
            QApplication.setPalette(QApplication.style().standardPalette())
            self.setStyleSheet("")  # 清除样式表

    def init_ui(self):
        """初始化用户界面。"""
        # 创建菜单栏
        self.create_menu_bar()

        # 创建状态栏
        status_bar = self.statusBar()
        status_bar.showMessage("就绪。选择文件或目录开始分析。")

        # 创建中心组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)  # 增加边距使界面更加宽敞
        main_layout.setSpacing(12)  # 增加间距使元素分布更合理

        # 标题区域
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 10)  # 减少底部边距

        # 标题
        title_label = QLabel("性能监控数据分析器")
        title_font = QFont("Arial", 18, QFont.Bold)  # 增大字体
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)

        # 添加副标题/描述
        subtitle_label = QLabel("分析性能监控数据并生成图表报告")
        subtitle_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle_label)

        # 添加水平分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        header_layout.addWidget(line)

        main_layout.addWidget(header_widget)

        # 输入部分
        input_group = QGroupBox("数据输入")
        input_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        input_layout = QVBoxLayout()
        input_layout.setSpacing(10)  # 增加元素间的间距

        # 文件选择
        file_layout = QHBoxLayout()
        file_label = QLabel("文件:")
        file_label.setMinimumWidth(50)  # 设置最小宽度使标签对齐
        self.file_path_label = QLabel("未选择文件")
        self.file_path_label.setStyleSheet("background-color: #f8f8f8; padding: 5px; border: 1px solid #ddd; border-radius: 3px;")
        select_file_button = QPushButton("选择文件")
        select_file_button.setIcon(QIcon.fromTheme("document-open", QIcon()))
        select_file_button.setCursor(Qt.PointingHandCursor)  # 鼠标悬停时显示手型光标
        select_file_button.setMinimumWidth(100)  # 设置按钮最小宽度
        select_file_button.clicked.connect(self.select_file)  # 连接点击事件
        file_layout.addWidget(file_label)
        file_layout.addWidget(self.file_path_label, 1)
        file_layout.addWidget(select_file_button)
        input_layout.addLayout(file_layout)

        # 目录选择
        dir_layout = QHBoxLayout()
        dir_label = QLabel("目录:")
        dir_label.setMinimumWidth(50)  # 设置最小宽度使标签对齐
        self.dir_path_label = QLabel("未选择目录")
        self.dir_path_label.setStyleSheet("background-color: #f8f8f8; padding: 5px; border: 1px solid #ddd; border-radius: 3px;")
        select_dir_button = QPushButton("选择目录")
        select_dir_button.setIcon(QIcon.fromTheme("folder-open", QIcon()))
        select_dir_button.setCursor(Qt.PointingHandCursor)  # 鼠标悬停时显示手型光标
        select_dir_button.setMinimumWidth(100)  # 设置按钮最小宽度
        select_dir_button.clicked.connect(self.select_directory)  # 连接点击事件
        dir_layout.addWidget(dir_label)
        dir_layout.addWidget(self.dir_path_label, 1)
        dir_layout.addWidget(select_dir_button)
        input_layout.addLayout(dir_layout)

        # 添加提示信息
        tip_label = QLabel("提示: 选择单个文件或包含多个文件的目录")
        tip_label.setStyleSheet("color: #666; font-style: italic;")
        input_layout.addWidget(tip_label)

        input_group.setLayout(input_layout)
        main_layout.addWidget(input_group)

        # 处理部分
        processing_group = QGroupBox("数据处理")
        processing_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        processing_layout = QVBoxLayout()
        processing_layout.setSpacing(10)  # 增加元素间的间距

        # 处理按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)  # 增加按钮间的间距

        # 处理文件按钮
        self.process_file_button = QPushButton("处理文件")
        self.process_file_button.setIcon(QIcon.fromTheme("document-save", QIcon()))
        self.process_file_button.setIconSize(QSize(16, 16))
        self.process_file_button.clicked.connect(self.process_file)
        self.process_file_button.setEnabled(False)
        self.process_file_button.setCursor(Qt.PointingHandCursor)
        self.process_file_button.setMinimumHeight(30)  # 增加按钮高度
        self.process_file_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 4px;
                padding: 5px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        # 处理目录按钮
        self.process_dir_button = QPushButton("处理目录")
        self.process_dir_button.setIcon(QIcon.fromTheme("folder-save", QIcon()))
        self.process_dir_button.setIconSize(QSize(16, 16))
        self.process_dir_button.clicked.connect(self.process_directory)
        self.process_dir_button.setEnabled(False)
        self.process_dir_button.setCursor(Qt.PointingHandCursor)
        self.process_dir_button.setMinimumHeight(30)  # 增加按钮高度
        self.process_dir_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border-radius: 4px;
                padding: 5px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        button_layout.addWidget(self.process_file_button)
        button_layout.addWidget(self.process_dir_button)
        processing_layout.addLayout(button_layout)

        # 进度条
        progress_layout = QVBoxLayout()
        progress_label = QLabel("处理进度:")
        progress_layout.addWidget(progress_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p%")
        self.progress_bar.setMinimumHeight(20)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #bbb;
                border-radius: 4px;
                text-align: center;
                background-color: #f0f0f0;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                width: 10px;
                margin: 0.5px;
                border-radius: 3px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)
        processing_layout.addLayout(progress_layout)

        processing_group.setLayout(processing_layout)
        main_layout.addWidget(processing_group)

        # 输出部分
        output_group = QGroupBox("结果输出")
        output_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        output_layout = QVBoxLayout()
        output_layout.setSpacing(10)  # 增加元素间的间距

        # 输出文件选择
        output_file_layout = QHBoxLayout()
        output_label = QLabel("输出:")
        output_label.setMinimumWidth(50)  # 设置最小宽度使标签对齐
        self.output_path_label = QLabel("未选择输出文件")
        self.output_path_label.setStyleSheet("background-color: #f8f8f8; padding: 5px; border: 1px solid #ddd; border-radius: 3px;")
        select_output_button = QPushButton("选择输出文件")
        select_output_button.setIcon(QIcon.fromTheme("document-save-as", QIcon()))
        select_output_button.setCursor(Qt.PointingHandCursor)
        select_output_button.setMinimumWidth(120)  # 设置按钮最小宽度
        select_output_button.clicked.connect(self.select_output_file)  # 连接点击事件
        select_output_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
        """)
        output_file_layout.addWidget(output_label)
        output_file_layout.addWidget(self.output_path_label, 1)
        output_file_layout.addWidget(select_output_button)
        output_layout.addLayout(output_file_layout)

        # 保存按钮
        save_button_layout = QHBoxLayout()
        save_button_layout.addStretch(1)  # 添加弹性空间，使按钮居中

        self.save_button = QPushButton("保存结果")
        self.save_button.setIcon(QIcon.fromTheme("document-save", QIcon()))
        self.save_button.setIconSize(QSize(20, 20))
        self.save_button.clicked.connect(self.save_results)
        self.save_button.setEnabled(False)
        self.save_button.setCursor(Qt.PointingHandCursor)
        self.save_button.setMinimumHeight(36)  # 增加按钮高度
        self.save_button.setMinimumWidth(150)  # 增加按钮宽度
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #FF5722;
                color: white;
                border-radius: 4px;
                padding: 8px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #E64A19;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        save_button_layout.addWidget(self.save_button)
        save_button_layout.addStretch(1)  # 添加弹性空间，使按钮居中
        output_layout.addLayout(save_button_layout)

        # 添加提示信息
        save_tip_label = QLabel("提示: 处理完成后可以保存结果到Excel文件")
        save_tip_label.setStyleSheet("color: #666; font-style: italic;")
        save_tip_label.setAlignment(Qt.AlignCenter)
        output_layout.addWidget(save_tip_label)

        output_group.setLayout(output_layout)
        main_layout.addWidget(output_group)

        # 结果选项卡组件
        results_group = QGroupBox("分析结果")
        results_group.setStyleSheet("QGroupBox { font-weight: bold; }")
        results_layout = QVBoxLayout()

        self.results_tabs = QTabWidget()
        self.results_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background: white;
                border-radius: 3px;
            }
            QTabBar::tab {
                background: #f0f0f0;
                border: 1px solid #cccccc;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 6px 12px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background: #ffffff;
                border-bottom: 1px solid white;
                font-weight: bold;
            }
            QTabBar::tab:hover:!selected {
                background: #e0e0e0;
            }
        """)

        # 摘要选项卡
        self.summary_table = QTableWidget()
        self.summary_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #555555;
                selection-background-color: #0078d7;
                selection-color: #ffffff;
                color: #333333;
                background-color: #ffffff;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 5px;
                border: 1px solid #555555;
                font-weight: bold;
                color: #333333;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #eeeeee;
            }
            /* 深色模式下的样式 */
            .dark QTableWidget {
                gridline-color: #555555;
                selection-background-color: #0078d7;
                selection-color: #ffffff;
                color: #ffffff;
                background-color: #333333;
                border: 1px solid #555555;
            }
            .dark QHeaderView::section {
                background-color: #444444;
                color: #ffffff;
                border: 1px solid #555555;
            }
            .dark QTableWidget::item {
                border-bottom: 1px solid #444444;
            }
        """)
        self.results_tabs.addTab(self.summary_table, "摘要")

        # 日志选项卡
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f8f8;
                border: 1px solid #dddddd;
                border-radius: 3px;
                padding: 5px;
                font-family: Consolas, Monospace;
                color: #333333;
            }
            /* 深色模式下的样式 */
            .dark QTextEdit {
                background-color: #2d2d2d;
                border: 1px solid #555555;
                color: #e0e0e0;
                border-radius: 3px;
                padding: 5px;
                font-family: Consolas, Monospace;
            }
        """)
        self.results_tabs.addTab(self.log_text, "日志")

        results_layout.addWidget(self.results_tabs)
        results_group.setLayout(results_layout)
        main_layout.addWidget(results_group, 1)  # 给它更多空间

        # 添加底部状态栏
        status_layout = QHBoxLayout()
        status_layout.setContentsMargins(0, 5, 0, 0)

        # 状态图标
        status_icon = QLabel()
        status_icon.setPixmap(QIcon.fromTheme("dialog-information", QIcon()).pixmap(16, 16))
        status_layout.addWidget(status_icon)

        # 状态文本
        self.status_text = QLabel("就绪 - 准备就绪")
        status_layout.addWidget(self.status_text, 1)

        # 添加到主布局
        main_layout.addLayout(status_layout)

        # 状态栏
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪")

    def select_file(self):
        """打开文件对话框选择Excel文件。"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Excel文件", "", "Excel文件 (*.xlsx *.xls)"
        )

        if file_path:
            self.file_path_label.setText(file_path)
            self.process_file_button.setEnabled(True)
            self.log_message(f"已选择文件: {file_path}")

    def select_directory(self):
        """打开目录对话框选择包含Excel文件的文件夹。"""
        directory_path = QFileDialog.getExistingDirectory(
            self, "选择包含Excel文件的目录"
        )

        if directory_path:
            self.dir_path_label.setText(directory_path)
            self.process_dir_button.setEnabled(True)
            self.log_message(f"已选择目录: {directory_path}")

    def select_output_file(self):
        """打开文件对话框选择输出Excel文件。"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "选择输出Excel文件", "", "Excel文件 (*.xlsx)"
        )

        if file_path:
            if not file_path.endswith('.xlsx'):
                file_path += '.xlsx'
            self.output_path_label.setText(file_path)
            self.output_path = file_path
            self.log_message(f"已选择输出文件: {file_path}")

            # 如果有结果，启用保存按钮
            if self.results:
                self.save_button.setEnabled(True)

    def process_file(self):
        """处理选定的Excel文件。"""
        file_path = self.file_path_label.text()

        if file_path == "未选择文件":
            QMessageBox.warning(self, "警告", "请先选择文件。")
            return

        self.log_message(f"正在处理文件: {file_path}")
        self.status_bar.showMessage("正在处理文件...")
        self.progress_bar.setValue(0)

        # 处理期间禁用按钮
        self.process_file_button.setEnabled(False)
        self.process_dir_button.setEnabled(False)

        # 启动处理线程
        self.processing_thread = ProcessingThread('file', file_path=file_path)
        self.processing_thread.progress_signal.connect(self.update_progress)
        self.processing_thread.result_signal.connect(self.handle_results)
        self.processing_thread.error_signal.connect(self.handle_error)
        self.processing_thread.finished_signal.connect(self.processing_finished)
        self.processing_thread.start()

    def process_directory(self):
        """处理选定目录中的所有Excel文件。"""
        directory_path = self.dir_path_label.text()

        if directory_path == "未选择目录":
            QMessageBox.warning(self, "警告", "请先选择目录。")
            return

        self.log_message(f"正在处理目录: {directory_path}")
        self.status_bar.showMessage("正在处理目录...")
        self.progress_bar.setValue(0)

        # 处理期间禁用按钮
        self.process_file_button.setEnabled(False)
        self.process_dir_button.setEnabled(False)

        # 启动处理线程
        self.processing_thread = ProcessingThread('directory', directory_path=directory_path)
        self.processing_thread.progress_signal.connect(self.update_progress)
        self.processing_thread.result_signal.connect(self.handle_results)
        self.processing_thread.error_signal.connect(self.handle_error)
        self.processing_thread.finished_signal.connect(self.processing_finished)
        self.processing_thread.start()

    def update_progress(self, current, total):
        """更新进度条。"""
        # 确保进度计算正确，特别是对于最后一个文件
        progress = min(100, int(current / total * 100))
        self.progress_bar.setValue(progress)
        self.status_bar.showMessage(f"正在处理: {current}/{total} 文件")

    def handle_results(self, results):
        """处理处理结果。"""
        self.results = results
        self.log_message(f"成功处理了 {len(results)} 个文件。")

        # 更新摘要表格
        self.update_summary_table(results)

        # 如果设置了输出路径，启用保存按钮
        if self.output_path:
            self.save_button.setEnabled(True)

    def handle_error(self, error_message):
        """处理错误。"""
        # 将错误消息添加到日志
        self.log_message(error_message)

        # 只有严重错误才弹出对话框
        if error_message.startswith("处理错误") or \
           error_message.startswith("文件格式错误") or \
           error_message.startswith("在选定目录中没有找到") or \
           error_message.startswith("没有成功处理任何文件"):
            QMessageBox.critical(self, "错误", error_message)

        # 自动切换到日志选项卡
        self.results_tabs.setCurrentIndex(1)

    def processing_finished(self):
        """处理完成时的操作。"""
        # 重新启用按钮
        if self.file_path_label.text() != "未选择文件":
            self.process_file_button.setEnabled(True)
        if self.dir_path_label.text() != "未选择目录":
            self.process_dir_button.setEnabled(True)

        self.status_bar.showMessage("处理完成")

    def update_summary_table(self, results):
        """使用结果更新摘要表格。"""
        # 清空表格
        self.summary_table.clear()

        # 设置表格属性
        self.summary_table.setRowCount(len(results))
        self.summary_table.setColumnCount(6)
        self.summary_table.setHorizontalHeaderLabels([
            "文件", "IP地址", "CPU使用率 (%)", "内存使用率 (%)", "磁盘繁忙度 (%)", "最大磁盘列"
        ])

        # 填充表格
        for row, result in enumerate(results):
            self.summary_table.setItem(row, 0, QTableWidgetItem(result['File']))
            self.summary_table.setItem(row, 1, QTableWidgetItem(str(result['IP Address'])))
            self.summary_table.setItem(row, 2, QTableWidgetItem(str(result['CPU Usage (%)'])))
            self.summary_table.setItem(row, 3, QTableWidgetItem(str(result['Memory Usage (%)'])))
            self.summary_table.setItem(row, 4, QTableWidgetItem(str(result['Disk Busy (%)'])))
            # 添加最大磁盘列名
            self.summary_table.setItem(row, 5, QTableWidgetItem(str(result['Max Disk Column']) if result['Max Disk Column'] else ""))

        # 调整列宽以适应内容
        self.summary_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

    def save_results(self):
        """将结果保存到Excel文件。"""
        try:
            # 记录保存操作开始
            log("开始保存结果操作", "INFO")

            if not self.results:
                log("没有结果可保存", "WARNING")
                QMessageBox.warning(self, "警告", "没有结果可保存。")
                return

            if not self.output_path:
                log("未选择输出文件", "WARNING")
                QMessageBox.warning(self, "警告", "请先选择输出文件。")
                return

            # 显示保存选项对话框
            try:
                log("显示保存选项对话框", "INFO")
                save_options_dialog = SaveOptionsDialog(self, len(self.results))
                if save_options_dialog.exec_() != QDialog.Accepted:
                    log("用户取消了保存选项对话框", "INFO")
                    return
            except Exception as dialog_error:
                log(f"显示保存选项对话框时出错: {dialog_error}", "ERROR")
                import traceback
                log(traceback.format_exc(), "ERROR")
                QMessageBox.critical(self, "错误", f"显示保存选项对话框时出错: {dialog_error}")
                return

            # 获取选择的保存模式和批次大小
            try:
                save_mode = save_options_dialog.get_selected_mode()
                batch_size = save_options_dialog.get_batch_size()
                log(f"选择的保存模式: {save_mode}, 批次大小: {batch_size}", "INFO")
                self.log_message(f"选择的保存模式: {save_mode}, 批次大小: {batch_size}")
            except Exception as mode_error:
                log(f"获取保存模式时出错: {mode_error}", "ERROR")
                import traceback
                log(traceback.format_exc(), "ERROR")
                QMessageBox.critical(self, "错误", f"获取保存模式时出错: {mode_error}")
                return

            # 禁用保存按钮，防止重复点击
            try:
                log("禁用保存按钮", "INFO")
                self.save_button.setEnabled(False)
            except Exception as button_error:
                log(f"禁用保存按钮时出错: {button_error}", "ERROR")
                # 不返回，继续执行

            # 创建进度对话框
            try:
                log("创建进度对话框", "INFO")

                # 如果选择的是快速保存模式，不显示进度对话框
                if save_mode == 'summary_only':
                    self.progress_dialog = None
                    log("快速保存模式，不显示进度对话框", "INFO")
                else:
                    # 对于其他模式，创建非模态的进度对话框
                    self.progress_dialog = QProgressDialog("正在保存结果...", "取消", 0, 100, self)
                    self.progress_dialog.setWindowTitle("保存进度")
                    # 使用非模态对话框，避免阻塞主窗口
                    self.progress_dialog.setWindowModality(Qt.NonModal)
                    self.progress_dialog.setMinimumDuration(0)  # 立即显示
                    self.progress_dialog.setValue(0)
                    log("进度对话框已创建", "INFO")
            except Exception as dialog_error:
                log(f"创建进度对话框时出错: {dialog_error}", "ERROR")
                import traceback
                log(traceback.format_exc(), "ERROR")
                # 如果创建进度对话框失败，仍然继续
                self.progress_dialog = None

            # 创建并启动保存结果线程
            try:
                log(f"创建保存结果线程，模式: {save_mode}, 批次大小: {batch_size}, 输出路径: {self.output_path}", "INFO")
                self.save_thread = SaveResultsThread(self.results, self.output_path, save_mode, batch_size)

                # 连接信号
                log("连接线程信号", "INFO")
                # 先连接成功信号，确保它先于完成信号处理
                self.save_thread.success_signal.connect(self.handle_save_success)
                # 然后连接其他信号
                self.save_thread.progress_signal.connect(self.log_message)
                self.save_thread.error_signal.connect(self.handle_save_error)
                self.save_thread.finished_signal.connect(self.save_finished)

                # 如果进度对话框存在，连接百分比信号
                if hasattr(self, 'progress_dialog') and self.progress_dialog is not None:
                    self.save_thread.percent_signal.connect(self.update_progress_dialog)
                    # 连接进度对话框的取消信号
                    self.progress_dialog.canceled.connect(self.cancel_save)

                # 启动线程
                log("启动保存线程", "INFO")
                self.save_thread.start()
                log("保存线程已启动", "INFO")
            except Exception as thread_error:
                log(f"创建或启动保存线程时出错: {thread_error}", "ERROR")
                import traceback
                log(traceback.format_exc(), "ERROR")

                # 重新启用保存按钮
                self.save_button.setEnabled(True)

                # 关闭进度对话框
                if hasattr(self, 'progress_dialog') and self.progress_dialog is not None:
                    try:
                        self.progress_dialog.close()
                    except:
                        pass
                    self.progress_dialog = None

                # 显示错误消息
                QMessageBox.critical(self, "错误", f"创建或启动保存线程时出错: {thread_error}")
        except Exception as e:
            # 记录异常
            error_msg, error_tb = log_exception(e, "保存结果时")
            self.log_message(f"错误: {error_msg}")
            self.log_message(error_tb)

            # 显示错误对话框
            QMessageBox.critical(self, "错误", f"保存结果时出错: {error_msg}")

            # 重新启用保存按钮
            self.save_button.setEnabled(True)

    @gui_exception_handler
    def update_progress_dialog(self, value):
        """更新进度对话框的值"""
        if hasattr(self, 'progress_dialog') and self.progress_dialog is not None:
            try:
                # 防止进度对话框已经被关闭的情况
                if not self.progress_dialog.isVisible():
                    return

                # 更新进度值
                self.progress_dialog.setValue(value)

                # 根据进度更新文本
                if value < 10:
                    self.progress_dialog.setLabelText("准备保存...")
                elif value < 30:
                    self.progress_dialog.setLabelText("保存摘要数据...")
                elif value < 60:
                    self.progress_dialog.setLabelText("生成图表...")
                elif value < 90:
                    self.progress_dialog.setLabelText("完成图表生成...")
                elif value >= 100:
                    # 如果进度完成，不要关闭对话框，只显示完成状态
                    self.progress_dialog.setLabelText("保存完成!")
                    # 确保进度条显示100%
                    self.progress_dialog.setValue(100)
                    # 禁用取消按钮，表示已完成
                    self.progress_dialog.setCancelButton(None)
                    # 强制刷新界面
                    QApplication.processEvents()
                    # 等待一小段时间，让用户看到100%完成
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(1000, lambda: None)
                    # 再次强制刷新界面
                    QApplication.processEvents()

                # 处理应用程序事件，确保界面反应
                QApplication.processEvents()
            except Exception as e:
                log_exception(e, "更新进度对话框时")

    @gui_exception_handler
    def handle_save_error(self, error_message):
        """处理保存错误。"""
        # 记录错误
        log(f"错误: {error_message}", "ERROR")
        self.log_message(f"错误: {error_message}")

        # 显示错误对话框
        QMessageBox.warning(self, "警告", error_message)

        # 自动切换到日志选项卡
        self.results_tabs.setCurrentIndex(1)

    @gui_exception_handler
    def handle_save_success(self, success_message):
        """处理保存成功。"""
        # 记录日志
        log("收到保存成功信号", "INFO")
        self.log_message(success_message)

        # 先隐藏进度对话框（如果存在）
        if hasattr(self, 'progress_dialog') and self.progress_dialog is not None:
            try:
                # 设置进度为100%
                self.progress_dialog.setValue(100)
                self.progress_dialog.setLabelText("保存完成!")
                # 不要立即隐藏，让用户看到100%完成
                log("在显示成功对话框前更新进度对话框", "INFO")
            except Exception as e:
                log_exception(e, "更新进度对话框时")

        # 强制处理应用程序事件，确保界面更新
        QApplication.processEvents()

        # 延迟一小段时间再显示成功对话框，确保进度对话框更新
        from PyQt5.QtCore import QTimer

        def show_success_dialog():
            try:
                log("定时器触发，准备显示成功对话框", "INFO")

                # 现在隐藏进度对话框
                if hasattr(self, 'progress_dialog') and self.progress_dialog is not None:
                    log("尝试隐藏进度对话框", "INFO")
                    self.progress_dialog.hide()
                    log("进度对话框已隐藏", "INFO")
                else:
                    log("没有进度对话框需要隐藏", "INFO")

                # 强制处理应用程序事件，确保界面更新
                QApplication.processEvents()

                log("显示成功对话框", "INFO")
                # 使用模态对话框，确保用户看到
                QMessageBox.information(self, "成功", success_message)
                log("成功对话框已显示", "INFO")
            except Exception as e:
                log_exception(e, "显示成功对话框时")

        # 增加延迟时间，确保进度对话框已经完全更新
        log("设置成功对话框延迟显示定时器，延迟1500毫秒", "INFO")
        QTimer.singleShot(1500, show_success_dialog)
        log("定时器已设置", "INFO")

        # 强制处理事件，确保界面已更新
        QApplication.processEvents()

        # 重新启用保存按钮
        self.save_button.setEnabled(True)

    @gui_exception_handler
    def save_finished(self):
        """保存完成时的操作。"""
        log("保存线程完成信号已接收", "INFO")

        # 检查是否正在取消保存
        if hasattr(self, '_cancelling_save') and self._cancelling_save:
            log("取消保存操作正在进行中，忽略save_finished调用", "INFO")
            return

        # 注意：不在这里处理进度对话框，因为成功弹窗可能还没有显示
        # 只在这里清理线程资源

        # 更新状态栏
        self.status_bar.showMessage("保存完成")

        # 安全地清理线程对象
        if hasattr(self, 'save_thread') and self.save_thread is not None:
            # 不要尝试终止线程，只需将引用设为None
            self.save_thread = None

        # 重新启用保存按钮（如果没有在handle_save_success中启用）
        if not self.save_button.isEnabled():
            self.save_button.setEnabled(True)

        # 处理应用程序事件，确保界面反应
        from PyQt5.QtCore import QCoreApplication
        QCoreApplication.processEvents()

        # 添加延时器，在成功弹窗关闭后清理进度对话框
        from PyQt5.QtCore import QTimer

        def cleanup_progress_dialog():
            # 关闭进度对话框
            if hasattr(self, 'progress_dialog') and self.progress_dialog is not None:
                try:
                    # 隐藏进度对话框
                    self.progress_dialog.hide()
                    log("延时清理：进度对话框已隐藏", "INFO")
                    self.progress_dialog = None
                except Exception as e:
                    log_exception(e, "延时清理进度对话框时")

        # 3秒后清理进度对话框，给成功弹窗足够的时间显示
        QTimer.singleShot(3000, cleanup_progress_dialog)

        log("保存完成方法已执行完毕", "INFO")

    @gui_exception_handler
    def cancel_save(self):
        """取消保存操作。"""
        # 添加一个标志来防止重复清理
        if hasattr(self, '_cancelling_save') and self._cancelling_save:
            log("取消保存操作正在进行中，忽略重复调用", "INFO")
            return

        self._cancelling_save = True

        try:
            # 记录日志
            log("用户取消保存操作", "INFO")
            self.log_message("用户取消保存操作")
            self.status_bar.showMessage("保存已取消")

            # 处理线程
            if hasattr(self, 'save_thread') and self.save_thread is not None:
                try:
                    if self.save_thread.isRunning():
                        # 注意：不能直接终止线程，因为可能会损坏文件
                        # 设置取消标志，让线程自己处理
                        log("线程正在运行，设置取消标志", "INFO")

                        # 设置取消标志
                        self.save_thread.set_cancelled(True)

                        # 更新进度对话框消息
                        if hasattr(self, 'progress_dialog') and self.progress_dialog is not None:
                            self.progress_dialog.setLabelText("正在取消操作...请稍等")
                            # 禁用取消按钮，避免重复点击
                            self.progress_dialog.setCancelButton(None)

                        # 等待线程响应取消请求，最多等待5秒
                        for i in range(50):  # 50 * 0.1 = 5秒
                            if not self.save_thread.isRunning():
                                log(f"线程已响应取消请求，用时 {i/10:.1f} 秒", "INFO")
                                break
                            # 强制处理事件，确保界面响应
                            QApplication.processEvents()
                            # 等待一小段时间
                            import time
                            time.sleep(0.1)

                            # 更新进度对话框消息，显示等待时间
                            if hasattr(self, 'progress_dialog') and self.progress_dialog is not None:
                                self.progress_dialog.setLabelText(f"正在取消操作...({i/10:.1f}秒)")

                        # 如果线程仍然在运行，则尝试使用quit和terminate
                        if self.save_thread.isRunning():
                            log("线程没有响应取消请求，尝试使用quit", "WARNING")
                            self.save_thread.quit()
                            self.save_thread.wait(1000)  # 等待最多1秒

                            # 如果线程仍然在运行，则强制终止
                            if self.save_thread.isRunning():
                                log("线程仍在运行，强制终止", "WARNING")
                                self.save_thread.terminate()
                                self.save_thread.wait()

                        # 关闭进度对话框
                        if hasattr(self, 'progress_dialog') and self.progress_dialog is not None:
                            self.progress_dialog.close()

                        # 显示取消成功消息
                        QMessageBox.information(self, "操作已取消", "保存操作已成功取消。\n\n注意：部分数据可能已经保存。")

                    # 清理线程对象
                    if self.save_thread is not None:
                        self.save_thread.deleteLater()
                        self.save_thread = None
                except Exception as e:
                    log(f"终止线程时出错: {e}", "ERROR")
                    # 即使出错也要确保线程对象被清理
                    self.save_thread = None

            # 关闭进度对话框
            if hasattr(self, 'progress_dialog') and self.progress_dialog is not None:
                try:
                    self.progress_dialog.close()
                except Exception as e:
                    log(f"关闭进度对话框时出错: {e}", "ERROR")
                finally:
                    self.progress_dialog = None

            # 重新启用保存按钮
            self.save_button.setEnabled(True)

            # 强制垃圾回收
            import gc
            gc.collect()

            # 处理应用程序事件，确保界面反应
            QApplication.processEvents()

        except Exception as e:
            log_exception(e, "处理取消保存操作时")

        finally:
            # 重置取消标志
            self._cancelling_save = False

    @gui_exception_handler
    def log_message(self, message):
        """将消息添加到日志。"""
        # 添加时间戳
        timestamp = QDateTime.currentDateTime().toString('yyyy-MM-dd hh:mm:ss')

        # 记录到日志文件
        level = "INFO"
        if message.startswith("错误") or message.startswith("处理") and "出错" in message:
            level = "ERROR"
        elif message.startswith("警告") or message.startswith("跳过"):
            level = "WARNING"

        # 使用日志模块记录
        log(message, level)

        # 根据消息类型设置不同的颜色
        if message.startswith("错误") or message.startswith("处理") and "出错" in message:
            # 错误消息显示为红色
            formatted_message = f"<span style='color:red;'>[{timestamp}] {message}</span>"
        elif message.startswith("警告") or message.startswith("跳过"):
            # 警告消息显示为橙色
            formatted_message = f"<span style='color:orange;'>[{timestamp}] {message}</span>"
        elif message.startswith("成功") or "处理完成" in message:
            # 成功消息显示为绿色
            formatted_message = f"<span style='color:green;'>[{timestamp}] {message}</span>"
        elif "开始处理" in message or "正在处理" in message:
            # 处理信息显示为蓝色
            formatted_message = f"<span style='color:blue;'>[{timestamp}] {message}</span>"
        else:
            # 其他消息保持默认颜色
            formatted_message = f"[{timestamp}] {message}"

        # 添加到日志文本框
        self.log_text.append(formatted_message)

        # 滚动到最新消息
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

        # 处理应用程序事件，确保界面反应
        QApplication.processEvents()
