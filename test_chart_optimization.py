import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from optimized_plotter import OptimizedPlotter

# 创建测试数据
def create_test_data():
    # 创建时间戳
    start_time = datetime.now()
    timestamps = [start_time + timedelta(minutes=i) for i in range(100)]
    
    # 创建CPU数据 (0-100之间的随机值)
    cpu_data = np.random.rand(100) * 100
    
    return timestamps, cpu_data

def test_optimized_plotter():
    # 创建测试数据
    timestamps, cpu_data = create_test_data()
    
    # 创建优化绘图器实例
    plotter = OptimizedPlotter()
    
    # 创建图表
    plt.figure(figsize=(12, 7))
    
    # 使用优化的绘图函数
    plotter.plot_with_timestamps_optimized(timestamps, cpu_data, label="CPU使用率")
    
    # 设置图表标题和轴标签
    plt.title('CPU使用率测试图表', fontweight='bold', pad=15)
    plt.xlabel('时间', fontweight='bold', labelpad=10)
    plt.ylabel('CPU使用率 (%)', fontweight='bold', labelpad=10)
    
    # 设置y轴范围
    plt.ylim(0, 100)
    
    # 添加网格线
    plt.grid(True, linestyle='--', alpha=0.7, color='#cccccc')
    
    # 设置图例
    plt.legend(loc='upper right')
    
    # 保存图表
    plt.savefig('test_chart_optimization.png')
    print("图表已保存为 test_chart_optimization.png")
    
    # 显示图表
    plt.show()

if __name__ == "__main__":
    test_optimized_plotter()
