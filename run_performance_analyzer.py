#!/usr/bin/env python3
"""
性能监控数据分析器启动脚本

这个脚本用于启动性能监控数据分析器GUI应用。
"""

import sys
import os

def main():
    """主函数"""
    try:
        # 确保当前目录在Python路径中
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 导入并启动GUI应用
        from PyQt5.QtWidgets import QApplication
        from gui import MainWindow
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保gui.py文件存在且PyQt5已正确安装")
        sys.exit(1)
    except Exception as e:
        print(f"启动错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
