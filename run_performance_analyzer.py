"""
性能监控数据分析器启动脚本

这个脚本用于直接启动性能监控数据分析器GUI应用。
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QSettings
from gui import MainWindow
from logger_utils import init_logger

def main():
    """主函数。"""
    # 初始化日志
    settings = QSettings("PerformanceAnalyzer", "Settings")
    log_level = settings.value("log_level", logging.INFO, type=int)
    init_logger(log_level=log_level)

    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
