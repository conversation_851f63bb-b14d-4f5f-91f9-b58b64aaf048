import os
import pandas as pd
import re
from openpyxl import load_workbook, Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from datetime import datetime

def is_valid_timestamp(timestamp_str):
    """
    验证时间戳是否有效
    """
    try:
        datetime.strptime(str(timestamp_str), '%Y-%m-%d %H:%M:%S')
        return True
    except (ValueError, TypeError):
        return False



def extract_ip_from_bbbp(xls):
    """
    从BBBP表格中提取ifconfig命令输出的IP信息

    参数:
        xls: pandas ExcelFile对象

    返回:
        dict: 包含IP地址信息的字典
    """
    try:
        # 检查BBBP表是否存在
        if 'BBBP' not in xls.sheet_names:
            print("未找到BBBP表格，无法提取IP信息")
            return {}

        # 读取BBBP表格
        df = pd.read_excel(xls, sheet_name='BBBP')

        # 查找包含IP信息的行
        ip_info = {}

        # 遍历每一行查找IP相关信息
        for index, row in df.iterrows():
            # 确保至少有两列
            if len(row) < 2:
                continue

            command = str(row.iloc[0]).lower()  # 获取第一列（命令）
            output = str(row.iloc[1])           # 获取第二列（输出）

            # 查找包含IP信息的命令
            if any(cmd in command for cmd in ['ifconfig', 'ip addr', 'ip a']):
                print(f"找到网络配置命令: {command}")

                # 尝试匹配IP地址
                # 匹配 inet xxx.xxx.xxx.xxx 格式
                ip_matches = re.finditer(r'inet\s+(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})', output)
                for match in ip_matches:
                    ip = match.group(1)
                    if not ip.startswith('127.'): # 排除本地回环地址
                        print(f"找到IP地址: {ip}")
                        ip_info['ip'] = ip

                # 如果找到了非本地IP，就停止搜索
                if ip_info:
                    break

        return ip_info

    except Exception as e:
        print(f"提取IP信息时出错: {e}")
        return {}

class ExcelProcessor:
    def __init__(self):
        """初始化Excel处理器。"""
        self.results = []
        self.file_paths = []

    def _get_timestamps_from_zzzz(self, file_path: str):
        """从ZZZZ表格获取时间戳数据"""
        try:
            # 读取ZZZZ表格
            zzzz_df = pd.read_excel(file_path, sheet_name='ZZZZ')
            if zzzz_df.empty:
                print(f"ZZZZ表格为空")
                return None

            # 打印ZZZZ表格的列名和前几行数据以进行调试
            print(f"ZZZZ表格列名: {zzzz_df.columns.tolist()}")
            print(f"ZZZZ表格前3行:\n{zzzz_df.head(3)}")

            # 检查是否首行是标题行
            # 尝试将第一行的第四列转换为时间戳
            if zzzz_df.shape[1] > 3:
                try:
                    pd.to_datetime(zzzz_df.iloc[0, 3], format='%Y-%m-%d %H:%M:%S', errors='raise')
                    # 如果能转换为时间戳，说明不是标题行
                    pass
                except (ValueError, TypeError):
                    # 如果不能转换为时间戳，说明是标题行，跳过第一行
                    print(f"检测到ZZZZ表格首行可能是标题行，跳过第一行")
                    # 更新DataFrame，移除首行
                    zzzz_df = zzzz_df.iloc[1:].reset_index(drop=True)

            # 尝试不同的列来获取时间数据
            # 首先尝试第四列（索引为3）
            if zzzz_df.shape[1] > 3:
                timestamps = pd.to_datetime(zzzz_df.iloc[:, 3], format='%Y-%m-%d %H:%M:%S', errors='coerce')
                valid_timestamps = timestamps[timestamps.notna()]
                if not valid_timestamps.empty:
                    print(f"从第4列找到{len(valid_timestamps)}个有效时间戳")
                    return valid_timestamps

            # 如果第四列不成功，尝试第二列（索引为1）
            if zzzz_df.shape[1] > 1:
                timestamps = pd.to_datetime(zzzz_df.iloc[:, 1], format='%Y-%m-%d %H:%M:%S', errors='coerce')
                valid_timestamps = timestamps[timestamps.notna()]
                if not valid_timestamps.empty:
                    print(f"从第2列找到{len(valid_timestamps)}个有效时间戳")
                    return valid_timestamps

            # 如果上述方法都失败，尝试遍历所有列
            for i in range(zzzz_df.shape[1]):
                timestamps = pd.to_datetime(zzzz_df.iloc[:, i], format='%Y-%m-%d %H:%M:%S', errors='coerce')
                valid_timestamps = timestamps[timestamps.notna()]
                if not valid_timestamps.empty:
                    print(f"从第{i+1}列找到{len(valid_timestamps)}个有效时间戳")
                    return valid_timestamps

            # 如果所有列都没有有效的时间数据，创建人工时间序列
            print(f"ZZZZ表格中没有有效的时间戳数据，创建人工时间序列")
            num_rows = len(zzzz_df)
            base_time = pd.Timestamp.now().floor('D')  # 今天的开始
            timestamps = [base_time + pd.Timedelta(minutes=i*5) for i in range(num_rows)]
            return pd.Series(timestamps)

        except Exception as e:
            print(f"读取ZZZZ表格时出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    def process_file(self, file_path):
        """
        处理单个Excel文件以提取CPU、内存和磁盘使用率数据。

        参数:
            file_path (str): Excel文件的路径

        返回:
            dict: 包含文件名、CPU、内存和磁盘使用率的提取数据
        """
        try:
            # Load the Excel file
            xls = pd.ExcelFile(file_path)

            # Check if required sheets exist
            required_sheets = ['CPU_ALL', 'MEM', 'DISKBUSY']
            for sheet in required_sheets:
                if sheet not in xls.sheet_names:
                    raise ValueError(f"Sheet '{sheet}' not found in {file_path}")

            # Extract CPU data - get CPU% column
            cpu_df = pd.read_excel(xls, 'CPU_ALL')
            if 'CPU%' not in cpu_df.columns:
                raise ValueError(f"Column 'CPU%' not found in CPU_ALL sheet of {file_path}")

            # 检查是否首行是标题行（通常标题行的CPU%值不是数值）
            try:
                float(cpu_df.iloc[0]['CPU%'])
                # 如果能转换为浮点数，说明不是标题行
                cpu_usage = cpu_df['CPU%'].mean()
            except (ValueError, TypeError):
                # 如果不能转换为浮点数，说明是标题行，跳过第一行
                print(f"检测到CPU_ALL表格首行可能是标题行，跳过第一行")
                cpu_usage = cpu_df.iloc[1:]['CPU%'].mean()
                # 更新DataFrame，移除首行
                cpu_df = cpu_df.iloc[1:].reset_index(drop=True)

            # Extract memory data
            mem_df = pd.read_excel(xls, 'MEM')
            required_mem_cols = ['memtotal', 'memfree', 'cached', 'buffers']
            for col in required_mem_cols:
                if col not in mem_df.columns:
                    raise ValueError(f"Column '{col}' not found in MEM sheet of {file_path}")

            # 检查是否首行是标题行
            try:
                # 尝试将第一行的memtotal转换为浮点数
                float(mem_df.iloc[0]['memtotal'])
                # 如果能转换为浮点数，说明不是标题行
                # Calculate memory usage: (memtotal - memfree - cached - buffers) / memtotal * 100
                mem_df['mem_usage'] = (mem_df['memtotal'] - mem_df['memfree'] -
                                      mem_df['cached'] - mem_df['buffers']) / mem_df['memtotal'] * 100
                mem_usage = mem_df['mem_usage'].mean()
            except (ValueError, TypeError):
                # 如果不能转换为浮点数，说明是标题行，跳过第一行
                print(f"检测到MEM表格首行可能是标题行，跳过第一行")
                # 更新DataFrame，移除首行
                mem_df = mem_df.iloc[1:].reset_index(drop=True)
                # Calculate memory usage: (memtotal - memfree - cached - buffers) / memtotal * 100
                mem_df['mem_usage'] = (mem_df['memtotal'] - mem_df['memfree'] -
                                      mem_df['cached'] - mem_df['buffers']) / mem_df['memtotal'] * 100
                mem_usage = mem_df['mem_usage'].mean()

            # Extract disk busy data
            try:
                disk_df = pd.read_excel(xls, 'DISKBUSY')

                # 检查是否首行是标题行
                first_col_name = disk_df.columns[0]
                try:
                    # 尝试将第一行的第一列转换为时间戳
                    is_valid_timestamp(disk_df.iloc[0][first_col_name])
                    # 如果能转换为时间戳，说明不是标题行
                    pass
                except (ValueError, TypeError):
                    # 如果不能转换为时间戳，说明是标题行，跳过第一行
                    print(f"检测到DISKBUSY表格首行可能是标题行，跳过第一行")
                    # 更新DataFrame，移除首行
                    disk_df = disk_df.iloc[1:].reset_index(drop=True)

                # 验证时间戳并过滤有效行
                valid_rows = disk_df.iloc[:, 0].apply(is_valid_timestamp)
                disk_df = disk_df[valid_rows]

                if disk_df.empty:
                    print(f"文件 {file_path} 没有找到有效的数据行")
                    max_disk_col = None
                    max_disk_usage = 0.0
                else:
                    # 获取所有列名（排除第一列时间戳）
                    disk_cols = disk_df.columns[1:]

                    # 排除Totals列
                    disk_cols_no_totals = [col for col in disk_cols if col.lower() != 'totals']

                    if not disk_cols_no_totals:
                        print(f"文件 {file_path} 没有有效的磁盘列")
                        max_disk_col = None
                        max_disk_usage = 0.0
                    else:
                        # 使用排除Totals后的列
                        disk_cols = disk_cols_no_totals

                        # 转换所有磁盘列为数值类型
                        numeric_df = disk_df[disk_cols].apply(pd.to_numeric, errors='coerce')

                        # 计算每个磁盘的平均值
                        disk_averages = numeric_df.mean()

                        # 获取最大值及其列名
                        max_disk_col = disk_averages.idxmax()
                        max_disk_usage = disk_averages[max_disk_col]

                        # 确保值不超过 100%
                        max_disk_usage = float(max_disk_usage) if pd.notnull(max_disk_usage) else 0.0
                        max_disk_usage = min(max_disk_usage, 100.0)

                        print(f"磁盘繁忙度: {max_disk_usage:.2f}% (磁盘: {max_disk_col})")
            except Exception as e:
                print(f"计算磁盘繁忙度时出错: {e}")
                max_disk_col = None
                max_disk_usage = 0.0

            # Get file name without extension (handling multiple dots in filename)
            file_name = os.path.splitext(os.path.basename(file_path))[0]

            # 获取时间戳数据
            def convert_timestamps(df, source_name):
                """尝试不同的时间格式转换时间戳"""
                try:
                    # 首先检查第一列是否已经是时间类型
                    first_col = df.iloc[:, 0]
                    if pd.api.types.is_datetime64_any_dtype(first_col):
                        print(f"{source_name} 列已经是时间类型")
                        # 记录有效时间戳的行数
                        valid_timestamps_count = len(first_col.dropna())
                        print(f"{source_name} 有效时间戳行数: {valid_timestamps_count}")
                        return first_col, valid_timestamps_count

                    # 尝试标准格式
                    timestamps = pd.to_datetime(first_col, format='%Y-%m-%d %H:%M:%S', errors='coerce')
                    valid_timestamps_count = len(timestamps.dropna())
                    if valid_timestamps_count > 0:
                        print(f"{source_name} 使用标准时间格式转换成功, 有效行数: {valid_timestamps_count}")
                        return timestamps, valid_timestamps_count

                    # 尝试其他常见格式
                    for fmt in ['%d-%m-%Y %H:%M:%S', '%m/%d/%Y %H:%M:%S', '%Y/%m/%d %H:%M:%S']:
                        timestamps = pd.to_datetime(first_col, format=fmt, errors='coerce')
                        valid_timestamps_count = len(timestamps.dropna())
                        if valid_timestamps_count > 0:
                            print(f"{source_name} 使用格式 {fmt} 转换成功, 有效行数: {valid_timestamps_count}")
                            return timestamps, valid_timestamps_count

                    # 尝试自动推断格式
                    timestamps = pd.to_datetime(first_col, errors='coerce')
                    valid_timestamps_count = len(timestamps.dropna())
                    if valid_timestamps_count > 0:
                        print(f"{source_name} 使用自动推断格式转换成功, 有效行数: {valid_timestamps_count}")
                        return timestamps, valid_timestamps_count

                    # 如果所有尝试都失败，使用索引
                    print(f"{source_name} 时间戳转换失败，使用索引替代")
                    return pd.Series(range(len(df))), 0
                except Exception as e:
                    print(f"{source_name} 时间戳转换错误: {e}")
                    return pd.Series(range(len(df))), 0

            # 尝试从ZZZZ表格获取时间戳
            zzzz_timestamps = self._get_timestamps_from_zzzz(file_path)
            zzzz_valid_count = len(zzzz_timestamps) if zzzz_timestamps is not None else 0

            if zzzz_timestamps is not None and zzzz_valid_count > 0:
                print(f"使用ZZZZ表格中的时间戳，有效行数: {zzzz_valid_count}")
                # 如果有ZZZZ时间戳，则使用它们作为所有表格的时间戳
                cpu_timestamps = zzzz_timestamps
                mem_timestamps = zzzz_timestamps
                disk_timestamps = zzzz_timestamps

                # 记录每个表格的有效时间戳行数
                timestamps_info = {
                    'CPU': zzzz_valid_count,
                    'MEM': zzzz_valid_count,
                    'DISK': zzzz_valid_count
                }
            else:
                print("未找到ZZZZ表格或表格中没有有效时间戳，使用各表格自带的时间戳")
                # 如果没有ZZZZ时间戳，则使用各表格自带的时间戳
                # 分别转换各个表格的时间戳，并获取有效行数
                cpu_timestamps, cpu_valid_count = convert_timestamps(cpu_df, "CPU")
                mem_timestamps, mem_valid_count = convert_timestamps(mem_df, "MEM")

                if 'disk_df' in locals() and not disk_df.empty:
                    disk_timestamps, disk_valid_count = convert_timestamps(disk_df, "DISK")
                else:
                    disk_timestamps, disk_valid_count = pd.Series([]), 0

                # 记录每个表格的有效时间戳行数
                timestamps_info = {
                    'CPU': cpu_valid_count,
                    'MEM': mem_valid_count,
                    'DISK': disk_valid_count
                }

            # 提取IP地址信息
            ip_info = extract_ip_from_bbbp(xls)
            ip_address = ip_info.get('ip', '未知')
            print(f"IP地址: {ip_address}")

            # Create result dictionary
            result = {
                'File': file_name,
                'IP Address': ip_address,
                'CPU Usage (%)': round(cpu_usage, 2),
                'Memory Usage (%)': round(mem_usage, 2),
                'Disk Busy (%)': round(max_disk_usage, 2),
                'Max Disk Column': max_disk_col,  # 添加最大磁盘列名
                'Raw Data': {
                    'CPU': cpu_df['CPU%'].tolist(),
                    'Memory': mem_df['mem_usage'].tolist(),
                    'Disk': {col: disk_df[col].tolist() for col in disk_cols if col in disk_df.columns},
                    'Timestamps': {
                        'CPU': cpu_timestamps,
                        'MEM': mem_timestamps,
                        'DISK': disk_timestamps
                    },
                    'TimestampCounts': timestamps_info  # 添加时间戳行数信息
                }
            }

            return result

        except Exception as e:
            raise Exception(f"Error processing file {file_path}: {str(e)}")

    def process_directory(self, directory_path):
        """
        Process all Excel files in a directory.

        Args:
            directory_path (str): Path to the directory containing Excel files

        Returns:
            list: List of results from all processed files
        """
        self.results = []
        self.file_paths = []

        # Get all Excel files in the directory
        for file in os.listdir(directory_path):
            if file.endswith(('.xlsx', '.xls')) and not file.startswith('~$'):
                file_path = os.path.join(directory_path, file)
                self.file_paths.append(file_path)

        # Process each file
        for file_path in self.file_paths:
            try:
                result = self.process_file(file_path)
                self.results.append(result)
            except Exception as e:
                print(f"Warning: {str(e)}")

        return self.results

    def save_summary(self, output_path):
        """
        Save the summary of all processed files to a new Excel file.

        Args:
            output_path (str): Path to save the output Excel file

        Returns:
            str: Path to the saved file
        """
        if not self.results:
            raise ValueError("No results to save. Process files first.")

        # Create a DataFrame from the results
        summary_df = pd.DataFrame([
            {
                'File': r['File'],
                'IP Address': r['IP Address'],
                'CPU Usage (%)': r['CPU Usage (%)'],
                'Memory Usage (%)': r['Memory Usage (%)'],
                '磁盘繁忙度 (%)': r['Disk Busy (%)'],
                'Max Disk Column': r['Max Disk Column']
            } for r in self.results
        ])

        # Save to Excel
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            summary_df.to_excel(writer, sheet_name='Summary', index=False)

            # 获取工作表并设置列宽
            worksheet = writer.sheets['Summary']
            for i, col in enumerate(summary_df.columns):
                # 设置列宽为列名长度和数据最大长度的较大值
                max_len = max(
                    len(str(col)),
                    summary_df[col].astype(str).map(len).max() if len(summary_df) > 0 else 0
                )
                # 添加一些额外空间
                worksheet.column_dimensions[chr(65 + i)].width = max_len + 4

        return output_path
