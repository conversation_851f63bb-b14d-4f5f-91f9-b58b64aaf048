"""
配置管理器

统一管理应用程序的配置设置。
"""

import os
import logging
from typing import Any, Optional


class ConfigManager:
    """统一配置管理器"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        # 界面设置
        'ui_theme': 'modern_blue',
        'font_size': 9,
        'window_width': 800,
        'window_height': 600,
        
        # 日志设置
        'log_level': logging.INFO,
        'log_dir': 'logs',
        'log_retention_days': 30,
        
        # 数据处理设置
        'max_file_size_mb': 100,
        'enable_charts': True,
        'chart_dpi': 120,
        'chart_width': 12,
        'chart_height': 7,
        
        # 性能设置
        'max_threads': 4,
        'chunk_size': 1000,
        'enable_cache': True,
        'cache_size_mb': 50,
    }
    
    def __init__(self):
        """初始化配置管理器"""
        self._settings = None
        self._config_cache = {}
        self._init_settings()
    
    def _init_settings(self):
        """初始化设置对象"""
        try:
            from PyQt5.QtCore import QSettings
            self._settings = QSettings("PerformanceAnalyzer", "Settings")
        except ImportError:
            # 如果PyQt5不可用，使用内存配置
            self._settings = None
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        参数:
            key: 配置键
            default: 默认值
            
        返回:
            配置值
        """
        # 首先检查缓存
        if key in self._config_cache:
            return self._config_cache[key]
        
        # 从默认配置获取默认值
        if default is None:
            default = self.DEFAULT_CONFIG.get(key)
        
        # 从设置中获取值
        if self._settings:
            value = self._settings.value(key, default)
            # 处理类型转换
            if isinstance(default, bool):
                value = str(value).lower() in ('true', '1', 'yes', 'on')
            elif isinstance(default, int):
                try:
                    value = int(value)
                except (ValueError, TypeError):
                    value = default
            elif isinstance(default, float):
                try:
                    value = float(value)
                except (ValueError, TypeError):
                    value = default
        else:
            value = default
        
        # 缓存值
        self._config_cache[key] = value
        return value
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        参数:
            key: 配置键
            value: 配置值
        """
        # 更新缓存
        self._config_cache[key] = value
        
        # 保存到设置
        if self._settings:
            self._settings.setValue(key, value)
            self._settings.sync()
    
    def get_ui_theme(self) -> str:
        """获取UI主题"""
        return self.get('ui_theme', 'modern_blue')
    
    def set_ui_theme(self, theme: str):
        """设置UI主题"""
        self.set('ui_theme', theme)
    
    def get_font_size(self) -> int:
        """获取字体大小"""
        return self.get('font_size', 9)
    
    def set_font_size(self, size: int):
        """设置字体大小"""
        self.set('font_size', size)
    
    def get_log_level(self) -> int:
        """获取日志级别"""
        return self.get('log_level', logging.INFO)
    
    def set_log_level(self, level: int):
        """设置日志级别"""
        self.set('log_level', level)
    
    def get_log_dir(self) -> str:
        """获取日志目录"""
        return self.get('log_dir', 'logs')
    
    def set_log_dir(self, log_dir: str):
        """设置日志目录"""
        self.set('log_dir', log_dir)
    
    def get_chart_config(self) -> dict:
        """获取图表配置"""
        return {
            'dpi': self.get('chart_dpi', 120),
            'width': self.get('chart_width', 12),
            'height': self.get('chart_height', 7),
            'enabled': self.get('enable_charts', True)
        }
    
    def set_chart_config(self, config: dict):
        """设置图表配置"""
        for key, value in config.items():
            if key == 'dpi':
                self.set('chart_dpi', value)
            elif key == 'width':
                self.set('chart_width', value)
            elif key == 'height':
                self.set('chart_height', value)
            elif key == 'enabled':
                self.set('enable_charts', value)
    
    def get_performance_config(self) -> dict:
        """获取性能配置"""
        return {
            'max_threads': self.get('max_threads', 4),
            'chunk_size': self.get('chunk_size', 1000),
            'enable_cache': self.get('enable_cache', True),
            'cache_size_mb': self.get('cache_size_mb', 50),
            'max_file_size_mb': self.get('max_file_size_mb', 100)
        }
    
    def set_performance_config(self, config: dict):
        """设置性能配置"""
        for key, value in config.items():
            if key in ['max_threads', 'chunk_size', 'cache_size_mb', 'max_file_size_mb']:
                self.set(key, value)
            elif key == 'enable_cache':
                self.set('enable_cache', value)
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self._config_cache.clear()
        if self._settings:
            self._settings.clear()
            self._settings.sync()
    
    def export_config(self, file_path: str):
        """
        导出配置到文件
        
        参数:
            file_path: 导出文件路径
        """
        import json
        
        config_data = {}
        for key in self.DEFAULT_CONFIG.keys():
            config_data[key] = self.get(key)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    def import_config(self, file_path: str):
        """
        从文件导入配置
        
        参数:
            file_path: 导入文件路径
        """
        import json
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        for key, value in config_data.items():
            if key in self.DEFAULT_CONFIG:
                self.set(key, value)
    
    def get_all_config(self) -> dict:
        """获取所有配置"""
        config = {}
        for key in self.DEFAULT_CONFIG.keys():
            config[key] = self.get(key)
        return config


# 全局配置管理器实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

def get_config(key: str, default: Any = None) -> Any:
    """快捷方式：获取配置值"""
    return get_config_manager().get(key, default)

def set_config(key: str, value: Any):
    """快捷方式：设置配置值"""
    get_config_manager().set(key, value)
