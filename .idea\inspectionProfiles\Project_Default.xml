<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="3">
            <item index="0" class="java.lang.String" itemvalue="openai" />
            <item index="1" class="java.lang.String" itemvalue="pillow" />
            <item index="2" class="java.lang.String" itemvalue="pydantic" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyTypeCheckerInspection" enabled="false" level="WARNING" enabled_by_default="false" />
  </profile>
</component>