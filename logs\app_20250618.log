=== 性能监控数据分析器日志 ===

设置日志系统时出错: Unknown level: 'H:\\PyWorkspace\\NewWork\\logs\\app_20250618.log'
Checking for required fonts...
2025-06-18 22:26:59 [WARNING] 启动脚本不存在，尝试直接启动模块
2025-06-18 22:26:59 [ERROR] 无法导入Nmon文件分析器模块
2025-06-18 22:27:01 [ERROR] 启动失败: 无法找到Nmon文件分析器模块
2025-06-18 22:27:03 [WARNING] 启动脚本不存在，尝试直接启动模块
2025-06-18 22:27:04 [ERROR] 注册中文字体时出错: 'FontManager' object has no attribute '_find_fonts_by_path'
2025-06-18 22:27:04 [ERROR] 注册英文字体时出错: 'FontManager' object has no attribute '_find_fonts_by_path'
2025-06-18 22:27:04 [WARNING] 没有可用的字体，回退到系统字体
2025-06-18 22:27:12 [WARNING] 启动脚本不存在，尝试直接启动模块
2025-06-18 22:27:12 [ERROR] 无法导入Nmon文件分析器模块
2025-06-18 22:27:15 [ERROR] 启动失败: 无法找到Nmon文件分析器模块
2025-06-18 22:28:04 [INFO] 日志系统初始化完成 - 文件: logs\app_20250618.log, 级别: INFO
2025-06-18 22:28:04 [INFO] 已应用新的设置
2025-06-18 22:28:06 [INFO] 启动Nmon文件分析器
2025-06-18 22:28:06 [INFO] 同步设置: 样式=modern_blue, 字体大小=9, 日志级别=INFO
2025-06-18 22:28:06 [INFO] 日志系统初始化完成 - 文件: logs\app_20250618.log, 级别: INFO
2025-06-18 22:28:06 [INFO] 同步界面设置完成
2025-06-18 22:28:06 [INFO] 当前目录: H:\PyWorkspace\NewWork
2025-06-18 22:28:06 [WARNING] 启动脚本不存在，尝试直接启动模块
2025-06-18 22:28:06 [ERROR] 无法导入Nmon文件分析器模块
2025-06-18 22:28:07 [ERROR] 启动失败: 无法找到Nmon文件分析器模块
2025-06-18 22:28:22 [INFO] 用户关闭启动器
2025-06-18 22:28:22 [INFO] 应用程序退出，退出代码: 0
2025-06-18 22:28:22 [CRITICAL] 系统退出函数被调用
2025-06-18 22:28:22 [CRITICAL] 退出时的调用堆栈:   File "H:\PyWorkspace\NewWork\main.py", line 227, in handle_exit
    stack = traceback.format_stack()

2025-06-18 22:28:22 [CRITICAL] 退出时的活动线程数: 2
2025-06-18 22:28:22 [CRITICAL] 线程: MainThread, 活动: False, 守护: False
2025-06-18 22:28:22 [CRITICAL] 线程: WatchdogThread, 活动: True, 守护: True
