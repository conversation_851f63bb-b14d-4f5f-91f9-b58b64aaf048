from ensure_fonts import FONT_INFO, download_font

def download_all_fonts():
    """Download all fonts defined in FONT_INFO"""
    print("Downloading all fonts...")
    
    success_count = 0
    fail_count = 0
    
    for font_key, font_info in FONT_INFO.items():
        print(f"Downloading {font_info['display_name']} ({font_key})...")
        if download_font(font_key):
            success_count += 1
            print(f"  Success!")
        else:
            fail_count += 1
            print(f"  Failed!")
    
    print(f"\nDownload complete: {success_count} succeeded, {fail_count} failed")

if __name__ == "__main__":
    download_all_fonts()
