"""
统一性能监控工具 - 主GUI界面

这个模块提供了一个统一的图形用户界面，集成了性能监控数据分析器和原始nmon文件分析工具。
"""

import os
import sys
import logging
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QFileDialog,
                            QProgressBar, QMessageBox, QTabWidget, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
                            QStatusBar, QFrame, QSplitter, QCheckBox, QComboBox,
                            QListWidget, QListWidgetItem, QAction, QMenu, QStackedWidget)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSize, QSettings
from PyQt5.QtGui import QFont, QIcon, QColor, QPalette

# 导入性能监控数据分析器组件
from excel_processor import ExcelProcessor
from data_analyzer import DataAnalyzer
from chart_generator import ChartGenerator
from save_results_thread import SaveResultsThread
from save_options_dialog import SaveOptionsDialog
from settings_dialog import SettingsDialog
from logger_utils import log, log_exception, get_logs, init_logger
from exception_handler import gui_exception_handler
from version import get_about_text, __version__, __app_name__
from web_analyzer_component import AdvancedAnalyzerComponent

# 导入原始nmon文件分析工具组件
from raw_nmon_analyzer import RawNmonAnalyzer
from raw_nmon_analyzer_gui import AnalysisThread as RawNmonAnalysisThread

# 导入nmon文件分析器组件
from nmon_parser import NmonParser
from nmon_analyzer_gui import AnalysisThread as NmonAnalysisThread


class UnifiedMainWindow(QMainWindow):
    """统一性能监控工具的主窗口。"""

    def __init__(self):
        """初始化主窗口。"""
        super().__init__()

        self.setWindowTitle("统一性能监控工具")
        self.setMinimumSize(1000, 700)

        # 设置应用程序样式
        self.set_style()

        # 初始化UI
        self.init_ui()

    def set_style(self):
        """设置应用程序样式。"""
        # 从设置中读取样式
        settings = QSettings("PerformanceAnalyzer", "Settings")
        style_name = settings.value("ui_style", "modern_blue", type=str)
        font_size = settings.value("font_size", 9, type=int)

        # 设置字体大小
        font = QFont()
        font.setPointSize(font_size)
        QApplication.setFont(font)

        # 设置样式
        QApplication.setStyle("Fusion")

        # 根据选择的样式设置调色板
        if style_name == "modern_blue":
            # 现代蓝模式
            palette = QPalette()
            palette.setColor(QPalette.Window, QColor(240, 245, 250))
            palette.setColor(QPalette.WindowText, QColor(35, 35, 35))
            palette.setColor(QPalette.Base, QColor(255, 255, 255))
            palette.setColor(QPalette.AlternateBase, QColor(245, 249, 252))
            palette.setColor(QPalette.ToolTipBase, QColor(255, 255, 255))
            palette.setColor(QPalette.ToolTipText, QColor(35, 35, 35))
            palette.setColor(QPalette.Text, QColor(35, 35, 35))
            palette.setColor(QPalette.Button, QColor(240, 245, 250))
            palette.setColor(QPalette.ButtonText, QColor(35, 35, 35))
            palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
            palette.setColor(QPalette.Link, QColor(42, 130, 218))
            palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
            palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
            QApplication.setPalette(palette)

            # 设置样式表
            self.setStyleSheet("""
                QToolTip { color: #333333; background-color: #f0f5fa; border: 1px solid #2a82da; }
                QTabWidget::pane { border: 1px solid #c0d0e0; }
                QTabBar::tab { background: #e0e9f0; color: #333333; padding: 5px; border: 1px solid #c0d0e0; border-bottom: none; }
                QTabBar::tab:selected { background: #ffffff; color: #2a82da; }
                QTableWidget { gridline-color: #e0e0e0; }
                QHeaderView::section { background-color: #f0f5fa; color: #333333; padding: 4px; border: 1px solid #c0d0e0; }
                QPushButton { background-color: #2a82da; color: white; border: none; padding: 5px 15px; border-radius: 3px; }
                QPushButton:hover { background-color: #3a92ea; }
                QPushButton:pressed { background-color: #1a72ca; }
                QTextEdit { background-color: #ffffff; color: #333333; border: 1px solid #c0d0e0; }
            """)

        elif style_name == "light_classic":
            # 浅色经典模式
            palette = QPalette()
            QApplication.setPalette(palette)  # 使用默认调色板

            # 设置样式表
            self.setStyleSheet("""
                QTabWidget::pane { border: 1px solid #c0c0c0; }
                QTabBar::tab { background: #f0f0f0; color: #505050; padding: 5px; border: 1px solid #c0c0c0; border-bottom: none; }
                QTabBar::tab:selected { background: #ffffff; color: #000000; }
                QTableWidget { gridline-color: #d0d0d0; }
                QHeaderView::section { background-color: #f0f0f0; color: #505050; padding: 4px; border: 1px solid #c0c0c0; }
            """)

        else:  # system_default
            # 系统默认模式
            QApplication.setPalette(QApplication.style().standardPalette())
            self.setStyleSheet("")  # 清除样式表

    def init_ui(self):
        """初始化用户界面。"""
        # 创建菜单栏
        self.create_menu_bar()

        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪。选择工具开始分析。")

        # 创建中心组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # 创建工具选择区域
        tool_selection_layout = QHBoxLayout()
        
        # 性能监控数据分析器按钮
        self.performance_analyzer_button = QPushButton("性能监控数据分析器")
        self.performance_analyzer_button.setMinimumHeight(40)
        self.performance_analyzer_button.clicked.connect(lambda: self.switch_tool(0))
        tool_selection_layout.addWidget(self.performance_analyzer_button)
        
        # 原始Nmon文件分析工具按钮
        self.raw_nmon_analyzer_button = QPushButton("原始Nmon文件分析工具")
        self.raw_nmon_analyzer_button.setMinimumHeight(40)
        self.raw_nmon_analyzer_button.clicked.connect(lambda: self.switch_tool(1))
        tool_selection_layout.addWidget(self.raw_nmon_analyzer_button)
        
        # Nmon文件分析器按钮
        self.nmon_analyzer_button = QPushButton("Nmon文件分析器")
        self.nmon_analyzer_button.setMinimumHeight(40)
        self.nmon_analyzer_button.clicked.connect(lambda: self.switch_tool(2))
        tool_selection_layout.addWidget(self.nmon_analyzer_button)
        
        # 添加工具选择区域到主布局
        main_layout.addLayout(tool_selection_layout)
        
        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(separator)
        
        # 创建堆叠部件用于切换工具
        self.tool_stack = QStackedWidget()
        
        # 添加性能监控数据分析器
        self.setup_performance_analyzer()
        
        # 添加原始nmon文件分析工具
        self.setup_raw_nmon_analyzer()
        
        # 添加nmon文件分析器
        self.setup_nmon_analyzer()
        
        # 添加堆叠部件到主布局
        main_layout.addWidget(self.tool_stack)
        
        # 默认显示性能监控数据分析器
        self.tool_stack.setCurrentIndex(0)
        self.update_button_styles(0)

    def create_menu_bar(self):
        """创建菜单栏"""
        # 创建菜单栏
        menu_bar = self.menuBar()

        # 文件菜单
        file_menu = menu_bar.addMenu("文件")

        # 退出动作
        exit_action = QAction("退出", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 设置菜单
        settings_menu = menu_bar.addMenu("设置")

        # 界面设置动作
        ui_settings_action = QAction("界面设置", self)
        ui_settings_action.triggered.connect(self.show_settings_dialog)
        settings_menu.addAction(ui_settings_action)

        # 帮助菜单
        help_menu = menu_bar.addMenu("帮助")

        # 关于动作
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)

    def setup_performance_analyzer(self):
        """设置性能监控数据分析器"""
        # 导入性能监控数据分析器
        from gui import MainWindow
        
        # 创建性能监控数据分析器实例
        self.performance_analyzer = MainWindow()
        
        # 添加到堆叠部件
        self.tool_stack.addWidget(self.performance_analyzer)

    def setup_raw_nmon_analyzer(self):
        """设置原始nmon文件分析工具"""
        # 导入原始nmon文件分析工具
        from raw_nmon_analyzer_gui import RawNmonAnalyzerApp
        
        # 创建原始nmon文件分析工具实例
        self.raw_nmon_analyzer = RawNmonAnalyzerApp()
        
        # 添加到堆叠部件
        self.tool_stack.addWidget(self.raw_nmon_analyzer)

    def setup_nmon_analyzer(self):
        """设置nmon文件分析器"""
        # 导入nmon文件分析器
        from nmon_analyzer_gui import NmonAnalyzerApp
        
        # 创建nmon文件分析器实例
        self.nmon_analyzer = NmonAnalyzerApp()
        
        # 添加到堆叠部件
        self.tool_stack.addWidget(self.nmon_analyzer)

    def switch_tool(self, index):
        """切换到指定的工具"""
        self.tool_stack.setCurrentIndex(index)
        self.update_button_styles(index)
        
        # 更新状态栏
        tool_names = ["性能监控数据分析器", "原始Nmon文件分析工具", "Nmon文件分析器"]
        self.status_bar.showMessage(f"当前工具: {tool_names[index]}")
        
        # 记录日志
        log(f"用户切换到工具: {tool_names[index]}")

    def update_button_styles(self, active_index):
        """更新按钮样式，突出显示当前选中的工具"""
        buttons = [
            self.performance_analyzer_button,
            self.raw_nmon_analyzer_button,
            self.nmon_analyzer_button
        ]
        
        # 从设置中读取样式
        settings = QSettings("PerformanceAnalyzer", "Settings")
        style_name = settings.value("ui_style", "modern_blue", type=str)
        
        # 设置按钮样式
        for i, button in enumerate(buttons):
            if i == active_index:
                if style_name == "modern_blue":
                    button.setStyleSheet("""
                        QPushButton {
                            background-color: #1a72ca;
                            color: white;
                            border: none;
                            padding: 5px 15px;
                            border-radius: 3px;
                            font-weight: bold;
                        }
                    """)
                elif style_name == "light_classic":
                    button.setStyleSheet("""
                        QPushButton {
                            background-color: #4a86e8;
                            color: white;
                            border: none;
                            padding: 5px 15px;
                            border-radius: 3px;
                            font-weight: bold;
                        }
                    """)
                else:  # system_default
                    button.setStyleSheet("""
                        QPushButton {
                            font-weight: bold;
                        }
                    """)
            else:
                if style_name == "modern_blue":
                    button.setStyleSheet("""
                        QPushButton {
                            background-color: #2a82da;
                            color: white;
                            border: none;
                            padding: 5px 15px;
                            border-radius: 3px;
                        }
                        QPushButton:hover {
                            background-color: #3a92ea;
                        }
                    """)
                elif style_name == "light_classic":
                    button.setStyleSheet("""
                        QPushButton {
                            background-color: #6c9ef0;
                            color: white;
                            border: none;
                            padding: 5px 15px;
                            border-radius: 3px;
                        }
                        QPushButton:hover {
                            background-color: #7caef2;
                        }
                    """)
                else:  # system_default
                    button.setStyleSheet("")

    def show_settings_dialog(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self)
        dialog.exec_()
        # 应用新的样式设置
        self.set_style()
        # 更新按钮样式
        self.update_button_styles(self.tool_stack.currentIndex())

    def show_about_dialog(self):
        """显示关于对话框"""
        about_text = f"""
        <h2>统一性能监控工具</h2>
        <p>版本: {__version__}</p>
        <p>这是一个集成了性能监控数据分析器和nmon文件分析工具的统一界面。</p>
        <p>可以方便地在不同工具之间切换，使用统一的界面风格。</p>
        <p>&copy; 2023 性能监控工具团队</p>
        """
        QMessageBox.about(self, "关于统一性能监控工具", about_text)

    def closeEvent(self, event):
        """处理窗口关闭事件"""
        # 记录关闭事件
        log("用户关闭主窗口", "INFO")
        
        # 确保所有子窗口也被关闭
        if hasattr(self, 'performance_analyzer'):
            self.performance_analyzer.close()
        if hasattr(self, 'raw_nmon_analyzer'):
            self.raw_nmon_analyzer.close()
        if hasattr(self, 'nmon_analyzer'):
            self.nmon_analyzer.close()
            
        # 接受关闭事件
        event.accept()


def main():
    """主函数。"""
    app = QApplication(sys.argv)
    window = UnifiedMainWindow()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
