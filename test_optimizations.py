"""
优化验证测试脚本

测试优化后的日志系统、配置管理和组件系统。
"""

import time
import sys
import os

def test_logger_optimization():
    """测试优化后的日志系统"""
    print("=" * 50)
    print("测试日志系统优化")
    print("=" * 50)

    try:
        from logger_utils import init_logger, log, get_logs, clean_logs

        # 测试初始化性能
        start_time = time.time()
        log_file = init_logger()
        init_time = time.time() - start_time

        print(f"✅ 日志系统初始化成功")
        print(f"   初始化时间: {init_time:.3f}秒")
        print(f"   日志文件: {log_file}")

        # 测试日志记录
        start_time = time.time()
        for i in range(100):
            log(f"测试日志消息 {i}", "INFO")
        log_time = time.time() - start_time

        print(f"✅ 日志记录测试完成")
        print(f"   100条日志记录时间: {log_time:.3f}秒")
        print(f"   平均每条日志: {log_time/100*1000:.2f}毫秒")

        # 测试内存缓存
        logs = get_logs()
        print(f"✅ 内存缓存测试完成")
        print(f"   缓存日志数量: {len(logs)}")

        return True

    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        return False

def test_config_manager():
    """测试配置管理系统"""
    print("\n" + "=" * 50)
    print("测试配置管理系统")
    print("=" * 50)

    try:
        from config_manager import ConfigManager, get_config_manager

        # 测试配置管理器创建
        start_time = time.time()
        config_mgr = get_config_manager()
        init_time = time.time() - start_time

        print(f"✅ 配置管理器创建成功")
        print(f"   初始化时间: {init_time:.3f}秒")

        # 测试配置读写
        start_time = time.time()

        # 设置配置
        config_mgr.set('test_key', 'test_value')
        config_mgr.set_ui_theme('dark_theme')
        config_mgr.set_font_size(12)

        # 读取配置
        test_value = config_mgr.get('test_key')
        theme = config_mgr.get_ui_theme()
        font_size = config_mgr.get_font_size()

        config_time = time.time() - start_time

        print(f"✅ 配置读写测试完成")
        print(f"   配置操作时间: {config_time:.3f}秒")
        print(f"   测试值: {test_value}")
        print(f"   主题: {theme}")
        print(f"   字体大小: {font_size}")

        # 测试配置导出
        try:
            config_mgr.export_config('test_config.json')
            print(f"✅ 配置导出测试完成")

            # 清理测试文件
            if os.path.exists('test_config.json'):
                os.remove('test_config.json')

        except Exception as e:
            print(f"⚠️  配置导出测试跳过: {e}")

        return True

    except Exception as e:
        print(f"❌ 配置管理系统测试失败: {e}")
        return False

def test_component_system():
    """测试组件系统"""
    print("\n" + "=" * 50)
    print("测试组件系统")
    print("=" * 50)

    try:
        # 初始化QApplication（如果需要）
        from PyQt5.QtWidgets import QApplication
        import sys

        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        from component_base import ComponentBase, ComponentState

        # 创建测试组件
        class TestComponent(ComponentBase):
            def __init__(self):
                super().__init__()
                self.component_id = "test_component"
                self.component_name = "测试组件"
                self.component_description = "用于测试的组件"
                self.init_called = False
                self.start_called = False
                self.stop_called = False

            def on_initialize(self):
                self.init_called = True

            def on_start(self):
                self.start_called = True

            def on_stop(self):
                self.stop_called = True

        # 测试组件创建和生命周期
        start_time = time.time()
        component = TestComponent()
        create_time = time.time() - start_time

        print(f"✅ 组件创建成功")
        print(f"   创建时间: {create_time:.3f}秒")
        print(f"   组件状态: {component.state.value}")
        print(f"   初始化调用: {component.init_called}")

        # 测试组件启动
        start_result = component.start()
        print(f"✅ 组件启动测试: {'成功' if start_result else '失败'}")
        print(f"   当前状态: {component.state.value}")
        print(f"   启动调用: {component.start_called}")

        # 测试组件停止
        stop_result = component.stop()
        print(f"✅ 组件停止测试: {'成功' if stop_result else '失败'}")
        print(f"   当前状态: {component.state.value}")
        print(f"   停止调用: {component.stop_called}")

        # 测试事件系统
        event_received = []
        def test_handler(event):
            event_received.append(event.event_type)

        component.register_event_handler('test_event', test_handler)
        component.emit_event('test_event', {'test': 'data'})

        print(f"✅ 事件系统测试完成")
        print(f"   接收到的事件: {event_received}")

        # 测试配置管理
        component.set_config('test_config', 'test_value')
        config_value = component.get_config('test_config')

        print(f"✅ 组件配置测试完成")
        print(f"   配置值: {config_value}")

        # 测试状态保存和恢复
        state = component.save_state()
        print(f"✅ 状态保存测试完成")
        print(f"   保存的状态: {state}")

        return True

    except Exception as e:
        print(f"❌ 组件系统测试失败: {e}")
        import traceback
        print(f"   错误详情: {traceback.format_exc()}")
        return False

def test_performance():
    """性能基准测试"""
    print("\n" + "=" * 50)
    print("性能基准测试")
    print("=" * 50)

    try:
        from logger_utils import log
        from config_manager import get_config_manager

        config_mgr = get_config_manager()

        # 日志性能测试
        start_time = time.time()
        for i in range(1000):
            log(f"性能测试日志 {i}", "INFO")
        log_perf = time.time() - start_time

        print(f"✅ 日志性能测试")
        print(f"   1000条日志时间: {log_perf:.3f}秒")
        print(f"   每秒日志数: {1000/log_perf:.0f}条/秒")

        # 配置访问性能测试
        start_time = time.time()
        for i in range(1000):
            config_mgr.get('ui_theme')
            config_mgr.get('font_size')
            config_mgr.get('log_level')
        config_perf = time.time() - start_time

        print(f"✅ 配置访问性能测试")
        print(f"   3000次配置访问时间: {config_perf:.3f}秒")
        if config_perf > 0:
            print(f"   每秒配置访问数: {3000/config_perf:.0f}次/秒")
        else:
            print(f"   每秒配置访问数: >1000000次/秒 (极快)")

        return True

    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("性能监控数据分析器 - 优化验证测试")
    print("测试开始时间:", time.strftime("%Y-%m-%d %H:%M:%S"))

    total_start_time = time.time()

    # 运行所有测试
    tests = [
        ("日志系统优化", test_logger_optimization),
        ("配置管理系统", test_config_manager),
        ("组件系统", test_component_system),
        ("性能基准", test_performance)
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))

    total_time = time.time() - total_start_time

    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")

    print(f"\n总体结果: {passed}/{total} 测试通过")
    print(f"总测试时间: {total_time:.3f}秒")
    print(f"测试完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

    if passed == total:
        print("\n🎉 所有优化验证测试通过！系统优化成功。")
        return 0
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关模块。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
