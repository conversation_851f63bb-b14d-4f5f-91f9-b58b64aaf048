"""
原始Nmon文件分析工具 - GUI界面

这个模块提供了一个图形用户界面，用于分析原始nmon文件。
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QFileDialog,
                            QProgressBar, QMessageBox, QTabWidget, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
                            QStatusBar, QFrame, QSplitter, QCheckBox, QComboBox,
                            QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSize
from PyQt5.QtGui import QFont, QIcon, QColor

from raw_nmon_analyzer import RawNmonAnalyzer


class AnalysisThread(QThread):
    """用于分析nmon文件的线程，不会阻塞GUI。"""

    progress_signal = pyqtSignal(int)  # 进度信号
    log_signal = pyqtSignal(str)  # 日志信号
    finished_signal = pyqtSignal(dict)  # 完成信号，传递结果
    error_signal = pyqtSignal(str)  # 错误信号

    def __init__(self, nmon_file_path, output_dir=None, include_charts=True, include_trend_analysis=True):
        """初始化分析线程。"""
        super().__init__()
        self.nmon_file_path = nmon_file_path
        self.output_dir = output_dir or os.path.dirname(nmon_file_path)
        self.include_charts = include_charts
        self.include_trend_analysis = include_trend_analysis

    def run(self):
        """运行分析线程。"""
        try:
            self.log_signal.emit(f"开始分析nmon文件: {os.path.basename(self.nmon_file_path)}")
            self.log_signal.emit(f"包含性能图表: {self.include_charts}, 包含趋势分析: {self.include_trend_analysis}")
            self.log_signal.emit(f"输出目录: {self.output_dir}")

            # 检查文件是否存在
            if not os.path.exists(self.nmon_file_path):
                self.error_signal.emit(f"错误: nmon文件不存在: {self.nmon_file_path}")
                return

            # 检查输出目录是否存在
            if not os.path.exists(self.output_dir):
                try:
                    os.makedirs(self.output_dir)
                    self.log_signal.emit(f"创建输出目录: {self.output_dir}")
                except Exception as e:
                    self.error_signal.emit(f"创建输出目录失败: {str(e)}")
                    return

            try:
                # 创建分析器
                self.log_signal.emit("创建 RawNmonAnalyzer 实例...")
                analyzer = RawNmonAnalyzer(self.nmon_file_path, self.output_dir)
                self.log_signal.emit("RawNmonAnalyzer 实例创建成功")

                # 设置进度回调
                def progress_callback(percent):
                    self.progress_signal.emit(percent)

                # 设置日志回调
                def log_callback(message):
                    self.log_signal.emit(message)

                # 设置回调函数
                analyzer.progress_callback = progress_callback
                analyzer.log_callback = log_callback

                # 设置分析选项
                self.log_signal.emit("设置分析选项...")
                if hasattr(analyzer, 'include_charts'):
                    analyzer.include_charts = self.include_charts
                    self.log_signal.emit(f"设置 include_charts = {self.include_charts}")
                else:
                    self.log_signal.emit("警告: analyzer 没有 include_charts 属性")

                if hasattr(analyzer, 'include_trend_analysis'):
                    analyzer.include_trend_analysis = self.include_trend_analysis
                    self.log_signal.emit(f"设置 include_trend_analysis = {self.include_trend_analysis}")
                else:
                    self.log_signal.emit("警告: analyzer 没有 include_trend_analysis 属性")

                # 分析nmon文件
                self.log_signal.emit("开始调用 analyze() 方法...")
                result = analyzer.analyze()
                self.log_signal.emit("分析完成，获取结果")

                # 检查结果
                if not result or not isinstance(result, dict):
                    self.error_signal.emit(f"分析结果无效: {result}")
                    return

                if 'output_file' not in result or 'analysis_result' not in result:
                    self.error_signal.emit(f"分析结果缺少必要字段: {result.keys()}")
                    return

                # 发送完成信号
                self.log_signal.emit("发送完成信号...")
                self.finished_signal.emit(result)
                self.log_signal.emit("分析线程完成")
            except KeyboardInterrupt:
                self.log_signal.emit("分析过程被用户中断")
                self.error_signal.emit("分析被用户中断")
            except Exception as e:
                self.error_signal.emit(f"分析过程中出错: {str(e)}")
                import traceback
                self.log_signal.emit(traceback.format_exc())

        except KeyboardInterrupt:
            self.log_signal.emit("分析nmon文件被用户中断")
            self.error_signal.emit("分析被用户中断")
        except Exception as e:
            self.error_signal.emit(f"分析nmon文件时出错: {str(e)}")
            import traceback
            self.log_signal.emit(traceback.format_exc())


class RawNmonAnalyzerApp(QMainWindow):
    """原始Nmon文件分析工具的主窗口。"""

    def __init__(self):
        """初始化主窗口。"""
        super().__init__()

        # 设置窗口属性
        self.setWindowTitle("原始Nmon文件分析工具")
        self.setMinimumSize(900, 700)

        # 初始化UI
        self.init_ui()

        # 初始化状态
        self.nmon_file_path = None
        self.output_dir = None
        self.analysis_thread = None
        self.analysis_result = None

    def init_ui(self):
        """初始化用户界面。"""
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 标题
        title_label = QLabel("原始Nmon文件分析工具")
        title_font = QFont("Arial", 16, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 描述
        desc_label = QLabel("选择nmon文件进行分析，生成Excel格式的解析结果和性能趋势分析")
        desc_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(desc_label)

        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line)

        # 文件选择部分
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout()

        # nmon文件选择
        nmon_file_layout = QHBoxLayout()
        nmon_file_label = QLabel("Nmon文件:")
        self.nmon_file_path_label = QLabel("未选择文件")
        self.nmon_file_path_label.setStyleSheet("background-color: #f0f0f0; padding: 5px; border-radius: 3px;")
        select_nmon_file_button = QPushButton("选择文件")
        select_nmon_file_button.clicked.connect(self.select_nmon_file)
        nmon_file_layout.addWidget(nmon_file_label)
        nmon_file_layout.addWidget(self.nmon_file_path_label, 1)
        nmon_file_layout.addWidget(select_nmon_file_button)
        file_layout.addLayout(nmon_file_layout)

        # 输出目录选择
        output_dir_layout = QHBoxLayout()
        output_dir_label = QLabel("输出目录:")
        self.output_dir_label = QLabel("默认与nmon文件相同目录")
        self.output_dir_label.setStyleSheet("background-color: #f0f0f0; padding: 5px; border-radius: 3px;")
        select_output_dir_button = QPushButton("选择目录")
        select_output_dir_button.clicked.connect(self.select_output_dir)
        output_dir_layout.addWidget(output_dir_label)
        output_dir_layout.addWidget(self.output_dir_label, 1)
        output_dir_layout.addWidget(select_output_dir_button)
        file_layout.addLayout(output_dir_layout)

        file_group.setLayout(file_layout)
        main_layout.addWidget(file_group)

        # 分析选项部分
        options_group = QGroupBox("分析选项")
        options_layout = QVBoxLayout()

        # 添加分析选项
        self.include_charts_checkbox = QCheckBox("包含性能图表")
        self.include_charts_checkbox.setChecked(True)
        options_layout.addWidget(self.include_charts_checkbox)

        self.include_trend_analysis_checkbox = QCheckBox("包含趋势分析")
        self.include_trend_analysis_checkbox.setChecked(True)
        options_layout.addWidget(self.include_trend_analysis_checkbox)

        options_group.setLayout(options_layout)
        main_layout.addWidget(options_group)

        # 处理部分
        process_group = QGroupBox("处理")
        process_layout = QVBoxLayout()

        # 处理按钮
        self.analyze_button = QPushButton("分析Nmon文件")
        self.analyze_button.setEnabled(False)
        self.analyze_button.clicked.connect(self.analyze_nmon_file)
        self.analyze_button.setMinimumHeight(40)
        self.analyze_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        process_layout.addWidget(self.analyze_button)

        # 进度条
        progress_layout = QHBoxLayout()
        progress_label = QLabel("进度:")
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(progress_label)
        progress_layout.addWidget(self.progress_bar, 1)
        process_layout.addLayout(progress_layout)

        process_group.setLayout(process_layout)
        main_layout.addWidget(process_group)

        # 结果选项卡
        results_tabs = QTabWidget()

        # 日志选项卡
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        results_tabs.addTab(self.log_text, "处理日志")

        # 摘要选项卡
        self.summary_widget = QWidget()
        summary_layout = QVBoxLayout(self.summary_widget)

        self.summary_table = QTableWidget()
        self.summary_table.setColumnCount(3)
        self.summary_table.setHorizontalHeaderLabels(["指标", "值", "单位"])
        self.summary_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        summary_layout.addWidget(self.summary_table)

        results_tabs.addTab(self.summary_widget, "摘要")

        # 系统信息选项卡
        self.system_info_widget = QWidget()
        system_info_layout = QVBoxLayout(self.system_info_widget)

        # 系统信息分类选择
        system_info_type_layout = QHBoxLayout()
        system_info_type_label = QLabel("信息类别:")
        self.system_info_type_combo = QComboBox()
        self.system_info_type_combo.addItems(["系统概览", "操作系统", "CPU", "内存", "磁盘", "网络", "其他"])
        self.system_info_type_combo.currentIndexChanged.connect(self.update_system_info_view)
        system_info_type_layout.addWidget(system_info_type_label)
        system_info_type_layout.addWidget(self.system_info_type_combo)
        system_info_type_layout.addStretch()
        system_info_layout.addLayout(system_info_type_layout)

        # 系统信息表格
        self.system_info_table = QTableWidget()
        self.system_info_table.setColumnCount(2)
        self.system_info_table.setHorizontalHeaderLabels(["项目", "详情"])
        self.system_info_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        system_info_layout.addWidget(self.system_info_table)

        results_tabs.addTab(self.system_info_widget, "系统信息")

        # 趋势分析选项卡
        self.trends_widget = QWidget()
        trends_layout = QVBoxLayout(self.trends_widget)

        self.trends_table = QTableWidget()
        self.trends_table.setColumnCount(3)
        self.trends_table.setHorizontalHeaderLabels(["指标", "趋势", "波动性"])
        self.trends_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        trends_layout.addWidget(self.trends_table)

        results_tabs.addTab(self.trends_widget, "趋势分析")

        # 详细数据选项卡
        self.details_widget = QWidget()
        details_layout = QVBoxLayout(self.details_widget)

        # 数据类型选择
        data_type_layout = QHBoxLayout()
        data_type_label = QLabel("数据类型:")
        self.data_type_combo = QComboBox()
        self.data_type_combo.addItems(["CPU", "内存", "磁盘", "网络", "系统"])
        self.data_type_combo.currentIndexChanged.connect(self.update_details_view)
        data_type_layout.addWidget(data_type_label)
        data_type_layout.addWidget(self.data_type_combo)
        data_type_layout.addStretch()
        details_layout.addLayout(data_type_layout)

        # 详细数据表格
        self.details_table = QTableWidget()
        self.details_table.setColumnCount(3)
        self.details_table.setHorizontalHeaderLabels(["指标", "值", "单位"])
        self.details_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        details_layout.addWidget(self.details_table)

        results_tabs.addTab(self.details_widget, "详细数据")

        main_layout.addWidget(results_tabs)

        # 状态栏
        self.statusBar().showMessage("就绪")

        # 初始日志
        self.log_message("原始Nmon文件分析工具已启动")
        self.log_message("请选择nmon文件进行分析")

    def select_nmon_file(self):
        """选择nmon文件。"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Nmon文件", "", "Nmon文件 (*.nmon);;所有文件 (*.*)"
        )

        if file_path:
            self.nmon_file_path = file_path
            self.nmon_file_path_label.setText(file_path)
            self.analyze_button.setEnabled(True)
            self.log_message(f"已选择nmon文件: {file_path}")

            # 重置输出目录为nmon文件所在目录
            self.output_dir = os.path.dirname(file_path)
            self.output_dir_label.setText(self.output_dir)

    def select_output_dir(self):
        """选择输出目录。"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择输出目录"
        )

        if dir_path:
            self.output_dir = dir_path
            self.output_dir_label.setText(dir_path)
            self.log_message(f"已选择输出目录: {dir_path}")

    def analyze_nmon_file(self):
        """分析nmon文件。"""
        try:
            self.log_message("开始分析nmon文件...")

            if not self.nmon_file_path:
                self.log_message("错误: 未选择nmon文件")
                QMessageBox.warning(self, "警告", "请先选择nmon文件")
                return

            # 禁用分析按钮
            self.analyze_button.setEnabled(False)

            # 重置进度条
            self.progress_bar.setValue(0)

            # 检查复选框是否存在
            if not hasattr(self, 'include_charts_checkbox') or not hasattr(self, 'include_trend_analysis_checkbox'):
                self.log_message("错误: 分析选项复选框未初始化")
                QMessageBox.critical(self, "错误", "分析选项未初始化")
                self.analyze_button.setEnabled(True)
                return

            # 获取分析选项
            include_charts = self.include_charts_checkbox.isChecked()
            include_trend_analysis = self.include_trend_analysis_checkbox.isChecked()

            self.log_message(f"分析选项: 包含性能图表={include_charts}, 包含趋势分析={include_trend_analysis}")

            # 创建并启动分析线程
            self.log_message(f"创建分析线程: 文件={self.nmon_file_path}, 输出目录={self.output_dir}")

            try:
                self.analysis_thread = AnalysisThread(
                    self.nmon_file_path,
                    self.output_dir,
                    include_charts=include_charts,
                    include_trend_analysis=include_trend_analysis
                )
                self.log_message("分析线程创建成功")

                # 连接信号
                self.analysis_thread.progress_signal.connect(self.update_progress)
                self.analysis_thread.log_signal.connect(self.log_message)
                self.analysis_thread.finished_signal.connect(self.analysis_finished)
                self.analysis_thread.error_signal.connect(self.analysis_error)

                # 启动线程
                self.log_message("启动分析线程...")
                self.analysis_thread.start()
                self.log_message("分析线程已启动")
            except KeyboardInterrupt:
                self.log_message("创建或启动分析线程被用户中断")
                QMessageBox.warning(self, "中断", "分析被用户中断")
                self.analyze_button.setEnabled(True)
                return
            except Exception as e:
                self.log_message(f"创建或启动分析线程时出错: {str(e)}")
                import traceback
                self.log_message(traceback.format_exc())
                QMessageBox.critical(self, "错误", f"创建或启动分析线程时出错: {str(e)}")
                self.analyze_button.setEnabled(True)
        except KeyboardInterrupt:
            self.log_message("分析nmon文件被用户中断")
            QMessageBox.warning(self, "中断", "分析被用户中断")
            self.analyze_button.setEnabled(True)
        except Exception as e:
            self.log_message(f"分析nmon文件时出错: {str(e)}")
            import traceback
            self.log_message(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"分析nmon文件时出错: {str(e)}")
            self.analyze_button.setEnabled(True)

    def update_progress(self, value):
        """更新进度条。"""
        self.progress_bar.setValue(value)

    def log_message(self, message):
        """添加日志消息。"""
        self.log_text.append(message)

    def analysis_finished(self, result):
        """分析完成。"""
        self.analysis_result = result

        # 更新状态
        self.statusBar().showMessage(f"分析完成，结果已保存到: {result['output_file']}")

        # 更新摘要
        self.update_summary()

        # 更新系统信息视图
        self.update_system_info_view()

        # 更新趋势分析
        self.update_trends()

        # 更新详细数据视图
        self.update_details_view()

        # 重新启用分析按钮
        self.analyze_button.setEnabled(True)

        # 显示成功消息
        QMessageBox.information(
            self,
            "分析完成",
            f"nmon文件分析完成，结果已保存到:\n{result['output_file']}"
        )

        # 询问是否打开结果文件
        reply = QMessageBox.question(
            self,
            "打开结果",
            "是否打开分析结果文件？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            # 打开结果文件
            from PyQt5.QtCore import QUrl
            from PyQt5.QtGui import QDesktopServices
            QDesktopServices.openUrl(QUrl.fromLocalFile(result['output_file']))

    def analysis_error(self, error_message):
        """分析错误。"""
        try:
            # 更新状态
            self.statusBar().showMessage("分析出错")

            # 记录错误
            self.log_message(f"错误: {error_message}")

            # 重置进度条
            self.progress_bar.setValue(0)

            # 清理线程资源
            if hasattr(self, 'analysis_thread') and self.analysis_thread is not None:
                try:
                    if self.analysis_thread.isRunning():
                        self.log_message("尝试终止分析线程...")
                        self.analysis_thread.terminate()
                        self.analysis_thread.wait(1000)  # 等待最多1秒
                        self.log_message("分析线程已终止")
                except Exception as e:
                    self.log_message(f"终止分析线程时出错: {str(e)}")

            # 重新启用分析按钮
            self.analyze_button.setEnabled(True)

            # 显示错误消息
            QMessageBox.critical(self, "分析错误", error_message)
        except Exception as e:
            print(f"处理分析错误时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            QMessageBox.critical(self, "严重错误", f"处理分析错误时出错: {str(e)}\n\n原始错误: {error_message}")

    def update_summary(self):
        """更新摘要表格。"""
        if not self.analysis_result:
            self.log_message("摘要: 分析结果为空")
            return

        analysis_result = self.analysis_result['analysis_result']
        self.log_message(f"摘要: 分析结果包含以下字段: {list(analysis_result.keys())}")

        # 清空表格
        self.summary_table.setRowCount(0)

        # 添加系统信息
        if 'system_details' in analysis_result:
            self.log_message(f"摘要: 找到系统详情 {len(analysis_result['system_details'])} 项")
            system_details = analysis_result['system_details']
            for key, value in system_details.items():
                row = self.summary_table.rowCount()
                self.summary_table.insertRow(row)
                self.summary_table.setItem(row, 0, QTableWidgetItem("系统 - " + str(key)))
                self.summary_table.setItem(row, 1, QTableWidgetItem(str(value)))
                self.summary_table.setItem(row, 2, QTableWidgetItem(""))
        else:
            self.log_message("摘要: 未找到系统详情")

        # 添加CPU信息
        if 'cpu_details' in analysis_result:
            self.log_message(f"摘要: 找到CPU详情 {len(analysis_result['cpu_details'])} 项")
            cpu_details = analysis_result['cpu_details']
            for key, value in cpu_details.items():
                row = self.summary_table.rowCount()
                self.summary_table.insertRow(row)
                self.summary_table.setItem(row, 0, QTableWidgetItem(str(key)))
                self.summary_table.setItem(row, 1, QTableWidgetItem(str(value)))
                self.summary_table.setItem(row, 2, QTableWidgetItem("%"))
        else:
            self.log_message("摘要: 未找到CPU详情")

        # 添加内存信息
        if 'memory_details' in analysis_result:
            self.log_message(f"摘要: 找到内存详情 {len(analysis_result['memory_details'])} 项")
            memory_details = analysis_result['memory_details']
            for key, value in memory_details.items():
                row = self.summary_table.rowCount()
                self.summary_table.insertRow(row)
                self.summary_table.setItem(row, 0, QTableWidgetItem(str(key)))
                self.summary_table.setItem(row, 1, QTableWidgetItem(str(value)))
                unit = "MB" if "总量" in str(key) or "空闲" in str(key) else "%"
                self.summary_table.setItem(row, 2, QTableWidgetItem(unit))
        else:
            self.log_message("摘要: 未找到内存详情")

        # 添加磁盘信息
        if 'disk_details' in analysis_result:
            self.log_message(f"摘要: 找到磁盘详情 {len(analysis_result['disk_details'])} 项")
            disk_details = analysis_result['disk_details']
            for key, value in disk_details.items():
                row = self.summary_table.rowCount()
                self.summary_table.insertRow(row)
                self.summary_table.setItem(row, 0, QTableWidgetItem(str(key)))
                self.summary_table.setItem(row, 1, QTableWidgetItem(str(value)))
                unit = "KB/s" if "速率" in str(key) else "%"
                self.summary_table.setItem(row, 2, QTableWidgetItem(unit))
        else:
            self.log_message("摘要: 未找到磁盘详情")

        # 添加网络信息
        if 'network_details' in analysis_result:
            self.log_message(f"摘要: 找到网络详情 {len(analysis_result['network_details'])} 项")
            network_details = analysis_result['network_details']
            for key, value in network_details.items():
                row = self.summary_table.rowCount()
                self.summary_table.insertRow(row)
                self.summary_table.setItem(row, 0, QTableWidgetItem(str(key)))
                self.summary_table.setItem(row, 1, QTableWidgetItem(str(value)))
                self.summary_table.setItem(row, 2, QTableWidgetItem("KB/s"))
        else:
            self.log_message("摘要: 未找到网络详情")

        # 检查摘要表格是否为空
        if self.summary_table.rowCount() == 0:
            self.log_message("警告: 摘要表格为空，添加提示行")
            self.summary_table.insertRow(0)
            self.summary_table.setItem(0, 0, QTableWidgetItem("没有找到摘要数据"))
            self.summary_table.setItem(0, 1, QTableWidgetItem("请检查日志了解详情"))
            self.summary_table.setItem(0, 2, QTableWidgetItem(""))

        self.log_message(f"摘要表格更新完成，共 {self.summary_table.rowCount()} 行")

    def update_trends(self):
        """更新趋势分析表格。"""
        if not self.analysis_result:
            return

        analysis_result = self.analysis_result['analysis_result']

        # 清空表格
        self.trends_table.setRowCount(0)

        # 添加CPU趋势
        if 'cpu_trend' in analysis_result:
            row = self.trends_table.rowCount()
            self.trends_table.insertRow(row)
            self.trends_table.setItem(row, 0, QTableWidgetItem("CPU使用率"))
            self.trends_table.setItem(row, 1, QTableWidgetItem(analysis_result['cpu_trend']['direction']))
            self.trends_table.setItem(row, 2, QTableWidgetItem(analysis_result['cpu_volatility']['level']))

        # 添加内存趋势
        if 'memory_trend' in analysis_result:
            row = self.trends_table.rowCount()
            self.trends_table.insertRow(row)
            self.trends_table.setItem(row, 0, QTableWidgetItem("内存使用率"))
            self.trends_table.setItem(row, 1, QTableWidgetItem(analysis_result['memory_trend']['direction']))
            self.trends_table.setItem(row, 2, QTableWidgetItem(analysis_result['memory_volatility']['level']))

        # 添加磁盘趋势
        if 'disk_trends' in analysis_result:
            for disk_name, trend in analysis_result['disk_trends'].items():
                row = self.trends_table.rowCount()
                self.trends_table.insertRow(row)
                self.trends_table.setItem(row, 0, QTableWidgetItem(f"磁盘 {disk_name} 使用率"))
                self.trends_table.setItem(row, 1, QTableWidgetItem(trend['direction']))
                self.trends_table.setItem(row, 2, QTableWidgetItem(analysis_result['disk_volatility'][disk_name]['level']))

        # 添加网络趋势
        if 'network_trends' in analysis_result:
            for interface_name, trends in analysis_result['network_trends'].items():
                if 'read' in trends:
                    row = self.trends_table.rowCount()
                    self.trends_table.insertRow(row)
                    self.trends_table.setItem(row, 0, QTableWidgetItem(f"网络 {interface_name} 读取"))
                    self.trends_table.setItem(row, 1, QTableWidgetItem(trends['read']['direction']))
                    self.trends_table.setItem(row, 2, QTableWidgetItem("--"))
                if 'write' in trends:
                    row = self.trends_table.rowCount()
                    self.trends_table.insertRow(row)
                    self.trends_table.setItem(row, 0, QTableWidgetItem(f"网络 {interface_name} 写入"))
                    self.trends_table.setItem(row, 1, QTableWidgetItem(trends['write']['direction']))
                    self.trends_table.setItem(row, 2, QTableWidgetItem("--"))

    def update_system_info_view(self, index=None):
        """更新系统信息视图。"""
        try:
            self.log_message("开始更新系统信息视图...")
            self.log_message(f"传入的index参数: {index}")

            # 检查分析结果是否存在
            if not hasattr(self, 'analysis_result') or not self.analysis_result:
                self.log_message("系统信息: 分析结果为空")
                # 显示提示信息
                self.system_info_table.setRowCount(0)
                row = self.system_info_table.rowCount()
                self.system_info_table.insertRow(row)
                self.system_info_table.setItem(row, 0, QTableWidgetItem("未找到分析结果"))
                self.system_info_table.setItem(row, 1, QTableWidgetItem("请先分析NMON文件"))
                return

            # 检查analysis_result是否包含'analysis_result'键
            if 'analysis_result' not in self.analysis_result:
                self.log_message("系统信息: 分析结果中没有'analysis_result'键")
                # 显示提示信息
                self.system_info_table.setRowCount(0)
                row = self.system_info_table.rowCount()
                self.system_info_table.insertRow(row)
                self.system_info_table.setItem(row, 0, QTableWidgetItem("分析结果格式错误"))
                self.system_info_table.setItem(row, 1, QTableWidgetItem("分析结果中没有'analysis_result'键"))
                return

            analysis_result = self.analysis_result['analysis_result']
            self.log_message(f"系统信息: 分析结果包含以下字段: {list(analysis_result.keys())}")

            # 检查表格是否存在
            if not hasattr(self, 'system_info_table') or self.system_info_table is None:
                self.log_message("系统信息: system_info_table不存在")
                return

            # 清空表格
            self.system_info_table.setRowCount(0)

            # 检查下拉框是否存在
            if not hasattr(self, 'system_info_type_combo') or self.system_info_type_combo is None:
                self.log_message("系统信息: system_info_type_combo不存在")
                # 显示提示信息
                row = self.system_info_table.rowCount()
                self.system_info_table.insertRow(row)
                self.system_info_table.setItem(row, 0, QTableWidgetItem("界面组件错误"))
                self.system_info_table.setItem(row, 1, QTableWidgetItem("system_info_type_combo不存在"))
                return

            # 获取信息类别
            try:
                info_type = self.system_info_type_combo.currentText()
                self.log_message(f"系统信息: 当前选择的信息类别: {info_type}")
            except Exception as e:
                self.log_message(f"获取信息类别时出错: {str(e)}")
                info_type = "系统概览"  # 默认使用系统概览
                self.log_message(f"使用默认信息类别: {info_type}")

            # 检查是否有系统信息
            if 'system_info' not in analysis_result:
                self.log_message("系统信息: 未找到系统信息数据")
                row = self.system_info_table.rowCount()
                self.system_info_table.insertRow(row)
                self.system_info_table.setItem(row, 0, QTableWidgetItem("未找到系统信息"))
                self.system_info_table.setItem(row, 1, QTableWidgetItem("请确保NMON文件包含BBBP行"))
                return

            system_info = analysis_result['system_info']
            self.log_message(f"系统信息: 找到系统信息分类: {list(system_info.keys())}")

            # 记录当前选择的信息类别
            self.log_message(f"当前处理的信息类别: {info_type}")
        except KeyboardInterrupt:
            self.log_message("系统信息: 更新被用户中断")
            # 显示中断信息
            row = self.system_info_table.rowCount()
            self.system_info_table.insertRow(row)
            self.system_info_table.setItem(row, 0, QTableWidgetItem("更新被中断"))
            self.system_info_table.setItem(row, 1, QTableWidgetItem("请重新分析或选择其他选项"))
            return
        except Exception as e:
            self.log_message(f"更新系统信息视图时出错: {str(e)}")
            import traceback
            self.log_message(traceback.format_exc())
            return

        # 根据信息类别显示不同的信息
        try:
            self.log_message(f"开始处理信息类别: {info_type}")

            if info_type == "系统概览":
                try:
                    # 显示基本系统信息
                    if 'basic_info' in system_info:
                        self._add_section_to_system_info_table("基本信息")
                        for key, value in system_info['basic_info'].items():
                            self._add_item_to_system_info_table(key, value)

                    # 显示操作系统信息
                    if 'os_info' in system_info and system_info['os_info']:
                        self._add_section_to_system_info_table("操作系统")
                        for key, value in system_info['os_info'].items():
                            if any(x in str(key).lower() for x in ['distrib', 'pretty', 'release']):
                                self._add_item_to_system_info_table(key, value)

                    # 显示CPU信息
                    if 'cpu_info' in system_info and system_info['cpu_info']:
                        self._add_section_to_system_info_table("CPU信息")
                        for key, value in system_info['cpu_info'].items():
                            if any(x in str(key).lower() for x in ['model', 'mhz', 'processor']):
                                self._add_item_to_system_info_table(key, value)

                    # 显示磁盘信息
                    if 'disk_info' in system_info and system_info['disk_info']:
                        self._add_section_to_system_info_table("磁盘信息")
                        for key, value in system_info['disk_info'].items():
                            if 'disk' in str(value) and not any(x in str(key) for x in ['1', '2', '3', '4', '5']):
                                self._add_item_to_system_info_table(key, value)
                except Exception as e:
                    self.log_message(f"显示系统概览时出错: {str(e)}")
                    import traceback
                    self.log_message(traceback.format_exc())
                    # 显示错误信息
                    row = self.system_info_table.rowCount()
                    self.system_info_table.insertRow(row)
                    self.system_info_table.setItem(row, 0, QTableWidgetItem("显示系统概览时出错"))
                    self.system_info_table.setItem(row, 1, QTableWidgetItem(str(e)))

            elif info_type == "操作系统":
                try:
                    # 显示操作系统信息
                    if 'os_info' in system_info and system_info['os_info']:
                        self._add_section_to_system_info_table("操作系统信息")
                        for key, value in sorted(system_info['os_info'].items()):
                            self._add_item_to_system_info_table(key, value)

                    # 显示内核版本
                    if 'other_info' in system_info:
                        kernel_info_found = False
                        for key, value in system_info['other_info'].items():
                            if '/proc/version' in str(key) or 'Linux version' in str(value):
                                if not kernel_info_found:
                                    self._add_section_to_system_info_table("内核版本")
                                    kernel_info_found = True
                                self._add_item_to_system_info_table(key, value)
                except Exception as e:
                    self.log_message(f"显示操作系统信息时出错: {str(e)}")
                    import traceback
                    self.log_message(traceback.format_exc())
                    # 显示错误信息
                    row = self.system_info_table.rowCount()
                    self.system_info_table.insertRow(row)
                    self.system_info_table.setItem(row, 0, QTableWidgetItem("显示操作系统信息时出错"))
                    self.system_info_table.setItem(row, 1, QTableWidgetItem(str(e)))

            elif info_type == "CPU":
                try:
                    # 显示CPU信息
                    if 'cpu_info' in system_info and system_info['cpu_info']:
                        self._add_section_to_system_info_table("CPU信息")
                        for key, value in sorted(system_info['cpu_info'].items()):
                            self._add_item_to_system_info_table(key, value)
                except Exception as e:
                    self.log_message(f"显示CPU信息时出错: {str(e)}")
                    import traceback
                    self.log_message(traceback.format_exc())
                    # 显示错误信息
                    row = self.system_info_table.rowCount()
                    self.system_info_table.insertRow(row)
                    self.system_info_table.setItem(row, 0, QTableWidgetItem("显示CPU信息时出错"))
                    self.system_info_table.setItem(row, 1, QTableWidgetItem(str(e)))

            elif info_type == "内存":
                try:
                    # 显示内存信息
                    if 'memory_info' in system_info and system_info['memory_info']:
                        self._add_section_to_system_info_table("内存信息")
                        for key, value in sorted(system_info['memory_info'].items()):
                            self._add_item_to_system_info_table(key, value)

                    # 从其他信息中提取内存信息
                    if 'other_info' in system_info:
                        mem_info_found = False
                        for key, value in system_info['other_info'].items():
                            if 'MemTotal' in str(value) or '/proc/meminfo' in str(key):
                                if not mem_info_found:
                                    self._add_section_to_system_info_table("/proc/meminfo")
                                    mem_info_found = True
                                self._add_item_to_system_info_table(key, value)
                except Exception as e:
                    self.log_message(f"显示内存信息时出错: {str(e)}")
                    import traceback
                    self.log_message(traceback.format_exc())
                    # 显示错误信息
                    row = self.system_info_table.rowCount()
                    self.system_info_table.insertRow(row)
                    self.system_info_table.setItem(row, 0, QTableWidgetItem("显示内存信息时出错"))
                    self.system_info_table.setItem(row, 1, QTableWidgetItem(str(e)))

            elif info_type == "磁盘":
                try:
                    # 显示磁盘信息
                    if 'disk_info' in system_info and system_info['disk_info']:
                        self._add_section_to_system_info_table("磁盘信息")
                        for key, value in sorted(system_info['disk_info'].items()):
                            self._add_item_to_system_info_table(key, value)
                except Exception as e:
                    self.log_message(f"显示磁盘信息时出错: {str(e)}")
                    import traceback
                    self.log_message(traceback.format_exc())
                    # 显示错误信息
                    row = self.system_info_table.rowCount()
                    self.system_info_table.insertRow(row)
                    self.system_info_table.setItem(row, 0, QTableWidgetItem("显示磁盘信息时出错"))
                    self.system_info_table.setItem(row, 1, QTableWidgetItem(str(e)))

            elif info_type == "网络":
                try:
                    # 显示网络信息
                    if 'network_info' in system_info and system_info['network_info']:
                        self._add_section_to_system_info_table("网络信息")
                        for key, value in sorted(system_info['network_info'].items()):
                            self._add_item_to_system_info_table(key, value)
                except Exception as e:
                    self.log_message(f"显示网络信息时出错: {str(e)}")
                    import traceback
                    self.log_message(traceback.format_exc())
                    # 显示错误信息
                    row = self.system_info_table.rowCount()
                    self.system_info_table.insertRow(row)
                    self.system_info_table.setItem(row, 0, QTableWidgetItem("显示网络信息时出错"))
                    self.system_info_table.setItem(row, 1, QTableWidgetItem(str(e)))

            elif info_type == "其他":
                try:
                    # 显示其他信息
                    if 'other_info' in system_info and system_info['other_info']:
                        self._add_section_to_system_info_table("其他系统信息")
                        for key, value in sorted(system_info['other_info'].items()):
                            self._add_item_to_system_info_table(key, value)
                except Exception as e:
                    self.log_message(f"显示其他信息时出错: {str(e)}")
                    import traceback
                    self.log_message(traceback.format_exc())
                    # 显示错误信息
                    row = self.system_info_table.rowCount()
                    self.system_info_table.insertRow(row)
                    self.system_info_table.setItem(row, 0, QTableWidgetItem("显示其他信息时出错"))
                    self.system_info_table.setItem(row, 1, QTableWidgetItem(str(e)))

            else:
                self.log_message(f"未知的信息类别: {info_type}")
                # 显示错误信息
                row = self.system_info_table.rowCount()
                self.system_info_table.insertRow(row)
                self.system_info_table.setItem(row, 0, QTableWidgetItem(f"未知的信息类别"))
                self.system_info_table.setItem(row, 1, QTableWidgetItem(info_type))

            # 如果表格为空，显示提示信息
            if self.system_info_table.rowCount() == 0:
                row = self.system_info_table.rowCount()
                self.system_info_table.insertRow(row)
                self.system_info_table.setItem(row, 0, QTableWidgetItem(f"没有找到{info_type}信息"))
                self.system_info_table.setItem(row, 1, QTableWidgetItem("请选择其他类别或确保NMON文件包含相关信息"))
        except Exception as e:
            self.log_message(f"处理系统信息时出错: {str(e)}")
            import traceback
            self.log_message(traceback.format_exc())
            # 显示错误信息
            self.system_info_table.setRowCount(0)
            row = self.system_info_table.rowCount()
            self.system_info_table.insertRow(row)
            self.system_info_table.setItem(row, 0, QTableWidgetItem("处理系统信息时出错"))
            self.system_info_table.setItem(row, 1, QTableWidgetItem(str(e)))

    def _add_section_to_system_info_table(self, section_name):
        """向系统信息表格添加分区标题。"""
        try:
            row = self.system_info_table.rowCount()
            self.system_info_table.insertRow(row)
            section_item = QTableWidgetItem(str(section_name))
            section_item.setBackground(QColor(230, 230, 250))  # 浅紫色背景
            font = section_item.font()
            font.setBold(True)
            section_item.setFont(font)
            self.system_info_table.setItem(row, 0, section_item)
            self.system_info_table.setItem(row, 1, QTableWidgetItem(""))
            self.system_info_table.setSpan(row, 0, 1, 2)  # 合并单元格
        except KeyboardInterrupt:
            self.log_message("添加分区标题被用户中断")
            raise  # 重新抛出异常，以便上层函数处理
        except Exception as e:
            self.log_message(f"添加分区标题时出错: {str(e)}, 标题: {section_name}")
            import traceback
            self.log_message(traceback.format_exc())

    def _add_item_to_system_info_table(self, key, value):
        """向系统信息表格添加项目。"""
        try:
            row = self.system_info_table.rowCount()
            self.system_info_table.insertRow(row)
            self.system_info_table.setItem(row, 0, QTableWidgetItem(str(key)))
            self.system_info_table.setItem(row, 1, QTableWidgetItem(str(value)))
        except KeyboardInterrupt:
            self.log_message("添加项目被用户中断")
            raise  # 重新抛出异常，以便上层函数处理
        except Exception as e:
            self.log_message(f"添加项目时出错: {str(e)}, 项目: {key}, 值: {value}")
            import traceback
            self.log_message(traceback.format_exc())

    def update_details_view(self):
        """更新详细数据视图。"""
        if not self.analysis_result:
            return

        analysis_result = self.analysis_result['analysis_result']

        # 清空表格
        self.details_table.setRowCount(0)

        # 获取选择的数据类型
        data_type = self.data_type_combo.currentText()

        if data_type == "CPU" and 'cpu_details' in analysis_result:
            details = analysis_result['cpu_details']
            for key, value in details.items():
                row = self.details_table.rowCount()
                self.details_table.insertRow(row)
                self.details_table.setItem(row, 0, QTableWidgetItem(key))
                self.details_table.setItem(row, 1, QTableWidgetItem(str(value)))
                self.details_table.setItem(row, 2, QTableWidgetItem("%"))

        elif data_type == "内存" and 'memory_details' in analysis_result:
            details = analysis_result['memory_details']
            for key, value in details.items():
                row = self.details_table.rowCount()
                self.details_table.insertRow(row)
                self.details_table.setItem(row, 0, QTableWidgetItem(key))
                self.details_table.setItem(row, 1, QTableWidgetItem(str(value)))
                unit = "MB" if "总量" in key or "空闲" in key else "%"
                self.details_table.setItem(row, 2, QTableWidgetItem(unit))

        elif data_type == "磁盘" and 'disk_details' in analysis_result:
            details = analysis_result['disk_details']
            for key, value in details.items():
                row = self.details_table.rowCount()
                self.details_table.insertRow(row)
                self.details_table.setItem(row, 0, QTableWidgetItem(key))
                self.details_table.setItem(row, 1, QTableWidgetItem(str(value)))
                unit = "KB/s" if "速率" in key else "%"
                self.details_table.setItem(row, 2, QTableWidgetItem(unit))

        elif data_type == "网络" and 'network_details' in analysis_result:
            details = analysis_result['network_details']
            for key, value in details.items():
                row = self.details_table.rowCount()
                self.details_table.insertRow(row)
                self.details_table.setItem(row, 0, QTableWidgetItem(key))
                self.details_table.setItem(row, 1, QTableWidgetItem(str(value)))
                self.details_table.setItem(row, 2, QTableWidgetItem("KB/s"))

        elif data_type == "系统" and 'system_details' in analysis_result:
            try:
                self.log_message(f"详细数据: 显示系统详情 {len(analysis_result['system_details'])} 项")
                details = analysis_result['system_details']

                # 按照字母顺序排序显示
                sorted_keys = sorted(details.keys())
                for key in sorted_keys:
                    value = details[key]
                    row = self.details_table.rowCount()
                    self.details_table.insertRow(row)
                    self.details_table.setItem(row, 0, QTableWidgetItem(str(key)))
                    self.details_table.setItem(row, 1, QTableWidgetItem(str(value)))
                    self.details_table.setItem(row, 2, QTableWidgetItem(""))

                # 如果有系统信息，也显示原始系统信息
                if 'system_info' in analysis_result:
                    system_info = analysis_result['system_info']

                    # 添加分隔行
                    row = self.details_table.rowCount()
                    self.details_table.insertRow(row)
                    self.details_table.setItem(row, 0, QTableWidgetItem("------- 原始系统信息 -------"))
                    self.details_table.setItem(row, 1, QTableWidgetItem(""))
                    self.details_table.setItem(row, 2, QTableWidgetItem(""))

                    # 添加各类系统信息
                    for category, info in system_info.items():
                        if isinstance(info, dict) and info:
                            # 添加分类标题
                            row = self.details_table.rowCount()
                            self.details_table.insertRow(row)
                            self.details_table.setItem(row, 0, QTableWidgetItem(f"--- {category} ---"))
                            self.details_table.setItem(row, 1, QTableWidgetItem(""))
                            self.details_table.setItem(row, 2, QTableWidgetItem(""))

                            # 添加分类下的信息
                            for info_key, info_value in info.items():
                                row = self.details_table.rowCount()
                                self.details_table.insertRow(row)
                                self.details_table.setItem(row, 0, QTableWidgetItem(str(info_key)))
                                self.details_table.setItem(row, 1, QTableWidgetItem(str(info_value)))
                                self.details_table.setItem(row, 2, QTableWidgetItem(""))
            except Exception as e:
                self.log_message(f"显示系统详情时出错: {str(e)}")
                import traceback
                self.log_message(traceback.format_exc())


def main():
    """主函数。"""
    app = QApplication(sys.argv)
    window = RawNmonAnalyzerApp()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
