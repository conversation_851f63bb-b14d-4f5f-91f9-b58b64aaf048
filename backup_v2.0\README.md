# 性能监控数据分析器

一个用于分析Excel文件中的CPU、内存和磁盘使用率数据的工具。通过直观的图形界面，轻松处理和可视化性能监控数据。

## 功能

- 从包含CPU、内存和磁盘使用率信息的Excel文件中提取和分析数据
- 处理目录中的多个文件
- 生成包含关键指标的摘要报告
- 为CPU、内存和磁盘使用率创建可视化图表
- 用户友好的图形界面
- 多种界面主题（现代蓝、浅色经典、系统默认）
- 内嵌中文字体，确保跨平台一致性
- 多种保存模式（自动模式、完整模式、批处理模式）

## 系统要求

- Python 3.6 或更高版本
- PyQt5 图形界面库
- pandas 和 numpy 用于数据处理
- matplotlib 用于图表生成
- openpyxl 用于 Excel 文件处理
- 其他依赖项详见 `requirements.txt`

## 兼容性

- Windows 7/8/10/11
- macOS 10.12 或更高版本
- 主流 Linux 发行版（Ubuntu、Fedora、Debian 等）
- 支持高分辨率显示器
- 自动适应不同屏幕大小

## 安装

1. 克隆或下载此仓库
2. 安装所需的依赖项：

```bash
pip install -r requirements.txt
```

## 使用方法

运行应用程序：

```bash
python main.py
```

### 处理单个文件

1. 点击“选择文件”选择Excel文件
2. 点击“处理文件”分析数据
3. 使用“选择输出文件”选择输出文件
4. 点击“保存结果”保存分析结果和图表

### 处理多个文件

1. 点击“选择目录”选择包含Excel文件的目录
2. 点击“处理目录”分析目录中的所有Excel文件
3. 使用“选择输出文件”选择输出文件
4. 点击“保存结果”保存分析结果和图表

### 保存模式

应用程序提供三种保存模式：

1. **自动模式**：自动生成所有图表和摘要数据，并保存到指定文件
2. **完整模式**：生成详细的分析报告，包含所有数据和图表
3. **批处理模式**：适用于处理大量文件，优化内存使用

### 界面设置

可以通过“设置”菜单自定义界面：

1. 点击“设置”菜单
2. 选择界面主题（现代蓝、深色模式、浅色经典、系统默认）
3. 调整字体大小
4. 自定义图表字体

## 输入文件格式

输入的Excel文件应包含以下工作表：

- **CPU_ALL**: 包含带有“CPU%”列的CPU使用率数据
- **MEM**: 包含带有“memtotal”、“memfree”、“cached”和“buffers”列的内存使用率数据
- **DISKBUSY**: 包含不同磁盘列的磁盘使用率数据

## 输出

程序生成一个Excel文件，其中包含：

- 一个包含每个输入文件的平均CPU、内存和最大磁盘使用率的摘要表
- 可视化CPU、内存和磁盘使用率数据的图表
- 根据选择的保存模式生成不同级别的详细分析

## 内嵌字体系统

本应用程序包含内嵌字体系统，确保图表在不同系统上的一致性：

- 自动下载并安装必要的字体
- 支持中文字体定制，包括思源黑体和思源宋体
- 无需依赖系统字体，提高了跨平台兼容性

## 日志系统

应用程序包含完整的日志系统：

- 自动记录应用程序运行时的关键信息
- 支持多个日志级别（INFO、WARNING、ERROR、DEBUG）
- 日志文件保存在 `logs` 目录中，便于排查问题

## 许可证

本项目采用MIT许可证 - 详情请参见LICENSE文件。

## 更新日志

### 2025年4月更新

- 添加多种界面主题（现代蓝、深色模式、浅色经典、系统默认）
- 实现内嵌字体系统，提高跨平台兼容性
- 添加中文字体设置功能
- 优化按钮点击反馈，提升用户体验
- 添加多种保存模式（自动模式、完整模式、批处理模式）
- 改进日志系统，提供更详细的运行信息
