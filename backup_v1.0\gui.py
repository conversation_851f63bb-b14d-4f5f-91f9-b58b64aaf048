import os
import sys
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QFileDialog,
                            QProgressBar, QMessageBox, QTabWidget, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
                            QProgressDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QDateTime
from PyQt5.QtGui import QFont, QIcon, QColor, QPalette

from excel_processor import ExcelProcessor
from data_analyzer import DataAnalyzer
from chart_generator import ChartGenerator

class ProcessingThread(QThread):
    """用于处理Excel文件的线程，不会阻塞GUI。"""

    progress_signal = pyqtSignal(int, int)  # (当前, 总数)
    result_signal = pyqtSignal(list)
    error_signal = pyqtSignal(str)
    finished_signal = pyqtSignal()

    def __init__(self, mode, file_path=None, directory_path=None):
        """
        初始化处理线程。

        参数:
            mode (str): 'file'（文件）或 'directory'（目录）
            file_path (str, 可选): 单个文件的路径
            directory_path (str, 可选): 目录的路径
        """
        super().__init__()
        self.mode = mode
        self.file_path = file_path
        self.directory_path = directory_path
        self.processor = ExcelProcessor()

    def run(self):
        """运行处理线程。"""
        try:
            if self.mode == 'file':
                # 处理单个文件
                try:
                    # 初始化进度显示
                    self.progress_signal.emit(0, 1)

                    # 检查文件格式
                    if not self._check_file_format(self.file_path):
                        self.error_signal.emit(f"文件格式错误: {os.path.basename(self.file_path)} 不是有效的性能监控数据文件。")
                        return

                    result = self.processor.process_file(self.file_path)
                    # 单文件处理完成，设置进度为100%
                    self.progress_signal.emit(1, 1)
                    self.result_signal.emit([result])
                except Exception as e:
                    self.error_signal.emit(f"处理文件时出错: {str(e)}")
                    return

            elif self.mode == 'directory':
                # 获取目录中的所有Excel文件
                files = [f for f in os.listdir(self.directory_path)
                        if f.endswith(('.xlsx', '.xls')) and not f.startswith('~$')]
                total_files = len(files)

                if total_files == 0:
                    self.error_signal.emit("在选定目录中没有找到Excel文件。")
                    return

                # 发送日志信息
                self.error_signal.emit(f"找到 {total_files} 个Excel文件，开始处理...")

                results = []
                processed_files = 0
                skipped_files = 0
                error_files = 0

                for i, file in enumerate(files):
                    file_path = os.path.join(self.directory_path, file)
                    try:
                        # 检查文件格式
                        if not self._check_file_format(file_path):
                            self.error_signal.emit(f"跳过文件: {file} - 不是有效的性能监控数据文件")
                            skipped_files += 1
                            continue

                        # 处理文件
                        self.error_signal.emit(f"正在处理: {file}")
                        result = self.processor.process_file(file_path)
                        results.append(result)
                        processed_files += 1
                        self.error_signal.emit(f"成功处理: {file}")
                    except Exception as e:
                        self.error_signal.emit(f"处理 {file} 时出错: {str(e)}")
                        error_files += 1

                    # 更新进度
                    self.progress_signal.emit(i + 1, total_files)

                # 确保进度显示为100%
                self.progress_signal.emit(total_files, total_files)

                # 发送处理结果摘要
                summary = f"处理完成: 共 {total_files} 个文件, 成功 {processed_files} 个, 跳过 {skipped_files} 个, 错误 {error_files} 个"
                self.error_signal.emit(summary)

                if results:
                    self.result_signal.emit(results)
                else:
                    self.error_signal.emit("没有成功处理任何文件，无法生成报告。")
                    return

            self.finished_signal.emit()

        except Exception as e:
            self.error_signal.emit(f"处理错误: {str(e)}")

    def _check_file_format(self, file_path):
        """检查文件是否为有效的性能监控数据文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False

            # 检查文件是否为Excel文件
            if not file_path.endswith(('.xlsx', '.xls')):
                return False

            # 检查文件是否包含必要的工作表
            xls = pd.ExcelFile(file_path)
            required_sheets = ['CPU_ALL', 'MEM', 'DISKBUSY', 'ZZZZ']
            for sheet in required_sheets:
                if sheet not in xls.sheet_names:
                    return False

            return True
        except Exception:
            return False


class SaveResultsThread(QThread):
    """用于保存结果的线程，不会阻塞GUI。"""

    progress_signal = pyqtSignal(str)  # 进度信息
    error_signal = pyqtSignal(str)    # 错误信息
    success_signal = pyqtSignal(str)  # 成功信息
    finished_signal = pyqtSignal()    # 完成信号

    def __init__(self, results, output_path):
        """
        初始化保存结果线程。

        参数:
            results (list): 要保存的结果列表
            output_path (str): 输出文件路径
        """
        super().__init__()
        self.results = results
        self.output_path = output_path
        self.processor = ExcelProcessor()
        self.chart_generator = ChartGenerator()

    def run(self):
        """运行保存结果线程。"""
        try:
            # 发送进度信息
            self.progress_signal.emit("开始保存结果...")

            # 设置处理器结果
            self.processor.results = self.results

            # 保存摘要
            self.progress_signal.emit("保存摘要数据...")
            self.processor.save_summary(self.output_path)
            self.progress_signal.emit("摘要数据保存成功")

            # 添加图表
            self.progress_signal.emit("生成并添加图表...")
            result, logs = self.chart_generator.add_charts_to_excel(self.results, self.output_path)

            # 将图表生成过程中的日志添加到日志窗口
            for log_message in logs:
                self.progress_signal.emit(log_message)

            if result:
                # 发送成功信息
                success_message = f"结果已成功保存到 {self.output_path}"
                self.success_signal.emit(success_message)
            else:
                # 发送警告信息
                warning_message = f"图表生成失败，但摘要数据已保存到 {self.output_path}"
                self.error_signal.emit(warning_message)

        except Exception as e:
            # 发送错误信息
            import traceback
            error_message = f"保存结果时出错: {str(e)}"
            self.error_signal.emit(error_message)
            self.progress_signal.emit(f"错误详情: {traceback.format_exc()}")

        finally:
            # 发送完成信号
            self.finished_signal.emit()


class MainWindow(QMainWindow):
    """用于Excel分析器应用程序的主窗口。"""

    def __init__(self):
        """初始化主窗口。"""
        super().__init__()

        self.setWindowTitle("性能监控数据分析器")
        self.setMinimumSize(800, 600)

        # 设置应用程序样式
        self.set_style()

        # 初始化组件
        self.processor = ExcelProcessor()
        self.analyzer = DataAnalyzer()
        self.chart_generator = ChartGenerator()

        # 初始化UI
        self.init_ui()

        # 数据存储
        self.results = []
        self.output_path = None

    def set_style(self):
        """设置应用程序样式。"""
        # 设置色彩调色板
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(53, 53, 53))
        palette.setColor(QPalette.WindowText, Qt.white)
        palette.setColor(QPalette.Base, QColor(25, 25, 25))
        palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
        palette.setColor(QPalette.ToolTipBase, Qt.white)
        palette.setColor(QPalette.ToolTipText, Qt.white)
        palette.setColor(QPalette.Text, Qt.white)
        palette.setColor(QPalette.Button, QColor(53, 53, 53))
        palette.setColor(QPalette.ButtonText, Qt.white)
        palette.setColor(QPalette.BrightText, Qt.red)
        palette.setColor(QPalette.Link, QColor(42, 130, 218))
        palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.HighlightedText, Qt.black)

        QApplication.setPalette(palette)

        # 设置样式表
        QApplication.setStyle("Fusion")

    def init_ui(self):
        """初始化用户界面。"""
        # 创建中心组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 标题
        title_label = QLabel("性能监控数据分析器")
        title_font = QFont("Arial", 16, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 输入部分
        input_group = QGroupBox("输入")
        input_layout = QVBoxLayout()

        # 文件选择
        file_layout = QHBoxLayout()
        self.file_path_label = QLabel("未选择文件")
        select_file_button = QPushButton("选择文件")
        select_file_button.clicked.connect(self.select_file)
        file_layout.addWidget(QLabel("文件:"))
        file_layout.addWidget(self.file_path_label, 1)
        file_layout.addWidget(select_file_button)
        input_layout.addLayout(file_layout)

        # 目录选择
        dir_layout = QHBoxLayout()
        self.dir_path_label = QLabel("未选择目录")
        select_dir_button = QPushButton("选择目录")
        select_dir_button.clicked.connect(self.select_directory)
        dir_layout.addWidget(QLabel("目录:"))
        dir_layout.addWidget(self.dir_path_label, 1)
        dir_layout.addWidget(select_dir_button)
        input_layout.addLayout(dir_layout)

        input_group.setLayout(input_layout)
        main_layout.addWidget(input_group)

        # 处理部分
        processing_group = QGroupBox("处理")
        processing_layout = QVBoxLayout()

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        processing_layout.addWidget(self.progress_bar)

        # 处理按钮
        button_layout = QHBoxLayout()
        self.process_file_button = QPushButton("处理文件")
        self.process_file_button.clicked.connect(self.process_file)
        self.process_file_button.setEnabled(False)

        self.process_dir_button = QPushButton("处理目录")
        self.process_dir_button.clicked.connect(self.process_directory)
        self.process_dir_button.setEnabled(False)

        button_layout.addWidget(self.process_file_button)
        button_layout.addWidget(self.process_dir_button)
        processing_layout.addLayout(button_layout)

        processing_group.setLayout(processing_layout)
        main_layout.addWidget(processing_group)

        # 输出部分
        output_group = QGroupBox("输出")
        output_layout = QVBoxLayout()

        # 输出文件选择
        output_file_layout = QHBoxLayout()
        self.output_path_label = QLabel("未选择输出文件")
        select_output_button = QPushButton("选择输出文件")
        select_output_button.clicked.connect(self.select_output_file)
        output_file_layout.addWidget(QLabel("输出:"))
        output_file_layout.addWidget(self.output_path_label, 1)
        output_file_layout.addWidget(select_output_button)
        output_layout.addLayout(output_file_layout)

        # 保存按钮
        self.save_button = QPushButton("保存结果")
        self.save_button.clicked.connect(self.save_results)
        self.save_button.setEnabled(False)
        output_layout.addWidget(self.save_button)

        output_group.setLayout(output_layout)
        main_layout.addWidget(output_group)

        # 结果选项卡组件
        self.results_tabs = QTabWidget()

        # 摘要选项卡
        self.summary_table = QTableWidget()
        self.results_tabs.addTab(self.summary_table, "摘要")

        # 日志选项卡
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.results_tabs.addTab(self.log_text, "日志")

        main_layout.addWidget(self.results_tabs, 1)  # 给它更多空间

        # 状态栏
        self.statusBar().showMessage("就绪")

    def select_file(self):
        """打开文件对话框选择Excel文件。"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Excel文件", "", "Excel文件 (*.xlsx *.xls)"
        )

        if file_path:
            self.file_path_label.setText(file_path)
            self.process_file_button.setEnabled(True)
            self.log_message(f"已选择文件: {file_path}")

    def select_directory(self):
        """打开目录对话框选择包含Excel文件的文件夹。"""
        directory_path = QFileDialog.getExistingDirectory(
            self, "选择包含Excel文件的目录"
        )

        if directory_path:
            self.dir_path_label.setText(directory_path)
            self.process_dir_button.setEnabled(True)
            self.log_message(f"已选择目录: {directory_path}")

    def select_output_file(self):
        """打开文件对话框选择输出Excel文件。"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "选择输出Excel文件", "", "Excel文件 (*.xlsx)"
        )

        if file_path:
            if not file_path.endswith('.xlsx'):
                file_path += '.xlsx'
            self.output_path_label.setText(file_path)
            self.output_path = file_path
            self.log_message(f"已选择输出文件: {file_path}")

            # 如果有结果，启用保存按钮
            if self.results:
                self.save_button.setEnabled(True)

    def process_file(self):
        """处理选定的Excel文件。"""
        file_path = self.file_path_label.text()

        if file_path == "未选择文件":
            QMessageBox.warning(self, "警告", "请先选择文件。")
            return

        self.log_message(f"正在处理文件: {file_path}")
        self.statusBar().showMessage("正在处理文件...")
        self.progress_bar.setValue(0)

        # 处理期间禁用按钮
        self.process_file_button.setEnabled(False)
        self.process_dir_button.setEnabled(False)

        # 启动处理线程
        self.processing_thread = ProcessingThread('file', file_path=file_path)
        self.processing_thread.progress_signal.connect(self.update_progress)
        self.processing_thread.result_signal.connect(self.handle_results)
        self.processing_thread.error_signal.connect(self.handle_error)
        self.processing_thread.finished_signal.connect(self.processing_finished)
        self.processing_thread.start()

    def process_directory(self):
        """处理选定目录中的所有Excel文件。"""
        directory_path = self.dir_path_label.text()

        if directory_path == "未选择目录":
            QMessageBox.warning(self, "警告", "请先选择目录。")
            return

        self.log_message(f"正在处理目录: {directory_path}")
        self.statusBar().showMessage("正在处理目录...")
        self.progress_bar.setValue(0)

        # 处理期间禁用按钮
        self.process_file_button.setEnabled(False)
        self.process_dir_button.setEnabled(False)

        # 启动处理线程
        self.processing_thread = ProcessingThread('directory', directory_path=directory_path)
        self.processing_thread.progress_signal.connect(self.update_progress)
        self.processing_thread.result_signal.connect(self.handle_results)
        self.processing_thread.error_signal.connect(self.handle_error)
        self.processing_thread.finished_signal.connect(self.processing_finished)
        self.processing_thread.start()

    def update_progress(self, current, total):
        """更新进度条。"""
        # 确保进度计算正确，特别是对于最后一个文件
        progress = min(100, int(current / total * 100))
        self.progress_bar.setValue(progress)
        self.statusBar().showMessage(f"正在处理: {current}/{total} 文件")

    def handle_results(self, results):
        """处理处理结果。"""
        self.results = results
        self.log_message(f"成功处理了 {len(results)} 个文件。")

        # 更新摘要表格
        self.update_summary_table(results)

        # 如果设置了输出路径，启用保存按钮
        if self.output_path:
            self.save_button.setEnabled(True)

    def handle_error(self, error_message):
        """处理错误。"""
        # 将错误消息添加到日志
        self.log_message(error_message)

        # 只有严重错误才弹出对话框
        if error_message.startswith("处理错误") or \
           error_message.startswith("文件格式错误") or \
           error_message.startswith("在选定目录中没有找到") or \
           error_message.startswith("没有成功处理任何文件"):
            QMessageBox.critical(self, "错误", error_message)

        # 自动切换到日志选项卡
        self.results_tabs.setCurrentIndex(1)

    def processing_finished(self):
        """处理完成时的操作。"""
        # 重新启用按钮
        if self.file_path_label.text() != "未选择文件":
            self.process_file_button.setEnabled(True)
        if self.dir_path_label.text() != "未选择目录":
            self.process_dir_button.setEnabled(True)

        self.statusBar().showMessage("处理完成")

    def update_summary_table(self, results):
        """使用结果更新摘要表格。"""
        # 清空表格
        self.summary_table.clear()

        # 设置表格属性
        self.summary_table.setRowCount(len(results))
        self.summary_table.setColumnCount(6)
        self.summary_table.setHorizontalHeaderLabels([
            "文件", "IP地址", "CPU使用率 (%)", "内存使用率 (%)", "磁盘繁忙度 (%)", "最大磁盘列"
        ])

        # 填充表格
        for row, result in enumerate(results):
            self.summary_table.setItem(row, 0, QTableWidgetItem(result['File']))
            self.summary_table.setItem(row, 1, QTableWidgetItem(str(result['IP Address'])))
            self.summary_table.setItem(row, 2, QTableWidgetItem(str(result['CPU Usage (%)'])))
            self.summary_table.setItem(row, 3, QTableWidgetItem(str(result['Memory Usage (%)'])))
            self.summary_table.setItem(row, 4, QTableWidgetItem(str(result['Disk Busy (%)'])))
            # 添加最大磁盘列名
            self.summary_table.setItem(row, 5, QTableWidgetItem(str(result['Max Disk Column']) if result['Max Disk Column'] else ""))

        # 调整列宽以适应内容
        self.summary_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

    def save_results(self):
        """将结果保存到Excel文件。"""
        if not self.results:
            QMessageBox.warning(self, "警告", "没有结果可保存。")
            return

        if not self.output_path:
            QMessageBox.warning(self, "警告", "请先选择输出文件。")
            return

        try:
            # 创建处理器实例
            processor = ExcelProcessor()
            processor.results = self.results

            # 保存摘要
            self.log_message("正在保存摘要数据...")
            processor.save_summary(self.output_path)
            self.log_message("摘要数据保存成功")

            # 添加图表
            self.log_message("正在生成图表...")
            result, logs = self.chart_generator.add_charts_to_excel(self.results, self.output_path)

            # 将图表生成过程中的日志添加到日志窗口
            for log_message in logs:
                self.log_message(log_message)

            if result:
                self.log_message(f"结果已保存到 {self.output_path}")
                QMessageBox.information(self, "成功", f"结果已保存到 {self.output_path}")
            else:
                self.log_message(f"图表生成失败，但摘要数据已保存到 {self.output_path}")
                QMessageBox.warning(self, "警告", f"图表生成失败，但摘要数据已保存。\n\n请查看日志了解详情。")

        except Exception as e:
            error_message = f"保存结果时出错: {str(e)}"
            self.log_message(f"错误: {error_message}")
            QMessageBox.critical(self, "错误", error_message)

            # 自动切换到日志选项卡
            self.results_tabs.setCurrentIndex(1)

    def log_message(self, message):
        """将消息添加到日志。"""
        # 添加时间戳
        timestamp = QDateTime.currentDateTime().toString('yyyy-MM-dd hh:mm:ss')

        # 根据消息类型设置不同的颜色
        if message.startswith("错误") or message.startswith("处理") and "出错" in message:
            # 错误消息显示为红色
            formatted_message = f"<span style='color:red;'>[{timestamp}] {message}</span>"
        elif message.startswith("警告") or message.startswith("跳过"):
            # 警告消息显示为橙色
            formatted_message = f"<span style='color:orange;'>[{timestamp}] {message}</span>"
        elif message.startswith("成功") or "处理完成" in message:
            # 成功消息显示为绿色
            formatted_message = f"<span style='color:green;'>[{timestamp}] {message}</span>"
        elif "开始处理" in message or "正在处理" in message:
            # 处理信息显示为蓝色
            formatted_message = f"<span style='color:blue;'>[{timestamp}] {message}</span>"
        else:
            # 其他消息保持默认颜色
            formatted_message = f"[{timestamp}] {message}"

        # 添加到日志文本框
        self.log_text.append(formatted_message)

        # 滚动到最新消息
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
