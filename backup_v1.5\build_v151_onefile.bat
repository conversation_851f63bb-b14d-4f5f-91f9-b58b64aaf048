@echo off
echo Building optimized single-file executable for Performance Analyzer V1.5.1...
echo.

REM Clean previous build files
echo Cleaning previous build files...
rmdir /s /q build
rmdir /s /q dist
echo.

REM Run PyInstaller with the optimized spec file
echo Running PyInstaller with optimized settings...
python -m PyInstaller performance_analyzer_onefile.spec --clean
echo.

if %ERRORLEVEL% == 0 (
    echo Build completed successfully!
    echo Executable is located in the dist directory: dist\性能监控数据分析器_V1.5.1.exe
) else (
    echo Build failed with error code %ERRORLEVEL%.
)

echo.
pause
