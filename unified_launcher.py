"""
统一性能监控工具启动器

这个模块提供了一个简单的启动器界面，用于启动不同的性能监控工具。
"""

import os
import sys
import subprocess
import logging
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QMessageBox,
                            QGroupBox, QFrame, QAction, QGridLayout, QSplashScreen,
                            QDialog, QFileDialog, QLineEdit, QFormLayout, QDialogButtonBox)
from settings_dialog import SettingsDialog
from PyQt5.QtCore import Qt, QSettings, QTimer, QSize
from PyQt5.QtGui import QFont, QIcon, QColor, QPalette, QPixmap

from logger_utils import log, log_exception, init_logger, clean_logs
from version import __version__, __app_name__, __release_date__, __author__


class UnifiedLauncher(QMainWindow):
    """统一性能监控工具启动器的主窗口。"""

    def __init__(self):
        """初始化主窗口。"""
        try:
            super().__init__()
            log("初始化UnifiedLauncher类", "INFO")

            # 显示启动画面
            self.show_splash_screen()

            self.setWindowTitle(f"性能监控工具集 v{__version__}")
            self.setMinimumSize(700, 500)
            self.setMaximumSize(900, 600)
            log("设置窗口属性完成", "INFO")

            # 设置图标
            try:
                if os.path.exists("icons/app_icon.ico"):
                    self.setWindowIcon(QIcon("icons/app_icon.ico"))
            except Exception as icon_error:
                log(f"设置图标时出错: {icon_error}", "WARNING")

            # 设置应用程序样式
            self.set_style()
            log("设置样式完成", "INFO")

            # 初始化UI
            self.init_ui()
            log("初始化UI完成", "INFO")

            # 清理旧日志
            QTimer.singleShot(2000, self.clean_old_logs)
        except Exception as e:
            log(f"UnifiedLauncher初始化时出错: {e}", "ERROR")
            # 尝试显示错误消息
            try:
                QMessageBox.critical(None, "初始化错误", f"启动器初始化时出错: {e}")
            except:
                pass
            raise

    def show_splash_screen(self):
        """显示启动画面"""
        try:
            # 尝试加载启动画面图片
            splash_path = "icons/splash.png"
            if not os.path.exists(splash_path):
                # 如果没有启动画面图片，创建一个空白图片
                pixmap = QPixmap(500, 300)
                pixmap.fill(Qt.white)
            else:
                pixmap = QPixmap(splash_path)

            # 创建启动画面
            self.splash = QSplashScreen(pixmap)

            # 设置启动画面消息
            self.splash.showMessage(f"正在启动性能监控工具集 v{__version__}...",
                                  Qt.AlignBottom | Qt.AlignCenter, Qt.black)

            # 显示启动画面
            self.splash.show()

            # 处理事件，确保启动画面显示
            QApplication.processEvents()

            # 设置定时器关闭启动画面
            QTimer.singleShot(2000, self.splash.close)
        except Exception as e:
            log(f"显示启动画面时出错: {e}", "WARNING")
            # 启动画面出错不应该阻止程序启动

    def clean_old_logs(self):
        """清理旧日志文件"""
        try:
            # 获取日志目录
            settings = QSettings("PerformanceAnalyzer", "Settings")
            log_dir = settings.value("log_dir", "logs", type=str)

            # 清理超过30天的日志
            clean_logs(days=30, log_dir=log_dir)
            log("旧日志清理完成", "INFO")
        except Exception as e:
            log(f"清理旧日志时出错: {e}", "WARNING")

    def set_style(self):
        """设置应用程序样式。"""
        # 从设置中读取样式
        settings = QSettings("PerformanceAnalyzer", "Settings")
        style_name = settings.value("ui_style", "modern_blue", type=str)
        font_size = settings.value("font_size", 9, type=int)

        # 设置字体大小
        font = QFont()
        font.setPointSize(font_size)
        QApplication.setFont(font)

        # 设置样式
        QApplication.setStyle("Fusion")

        # 根据选择的样式设置调色板
        if style_name == "modern_blue":
            # 现代蓝模式
            palette = QPalette()
            palette.setColor(QPalette.Window, QColor(240, 245, 250))
            palette.setColor(QPalette.WindowText, QColor(35, 35, 35))
            palette.setColor(QPalette.Base, QColor(255, 255, 255))
            palette.setColor(QPalette.AlternateBase, QColor(245, 249, 252))
            palette.setColor(QPalette.ToolTipBase, QColor(255, 255, 255))
            palette.setColor(QPalette.ToolTipText, QColor(35, 35, 35))
            palette.setColor(QPalette.Text, QColor(35, 35, 35))
            palette.setColor(QPalette.Button, QColor(240, 245, 250))
            palette.setColor(QPalette.ButtonText, QColor(35, 35, 35))
            palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
            palette.setColor(QPalette.Link, QColor(42, 130, 218))
            palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
            palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
            QApplication.setPalette(palette)

            # 设置样式表
            self.setStyleSheet("""
                QToolTip { color: #333333; background-color: #f0f5fa; border: 1px solid #2a82da; }
                QGroupBox { background-color: #ffffff; border: 1px solid #c0d0e0; border-radius: 5px; margin-top: 1ex; }
                QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; }
                QPushButton { background-color: #2a82da; color: white; border: none; padding: 10px 20px; border-radius: 3px; font-size: 14px; }
                QPushButton:hover { background-color: #3a92ea; }
                QPushButton:pressed { background-color: #1a72ca; }
            """)

        elif style_name == "light_classic":
            # 浅色经典模式
            palette = QPalette()
            QApplication.setPalette(palette)  # 使用默认调色板

            # 设置样式表
            self.setStyleSheet("""
                QGroupBox { background-color: #f8f8f8; border: 1px solid #c0c0c0; border-radius: 5px; margin-top: 1ex; }
                QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; }
                QPushButton { padding: 8px 16px; border-radius: 3px; font-size: 13px; }
            """)

        else:  # system_default
            # 系统默认模式
            QApplication.setPalette(QApplication.style().standardPalette())
            self.setStyleSheet("""
                QGroupBox { margin-top: 1ex; }
                QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; }
                QPushButton { padding: 8px 16px; font-size: 13px; }
            """)

    def init_ui(self):
        """初始化用户界面。"""
        try:
            # 创建菜单栏
            self.create_menu_bar()
            log("创建菜单栏完成", "INFO")

            # 创建中心组件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            log("创建中心组件完成", "INFO")

            # 主布局
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)
            log("设置主布局完成", "INFO")

            # 添加标题和版本信息
            header_layout = QHBoxLayout()

            # 尝试加载图标
            logo_label = QLabel()
            try:
                if os.path.exists("icons/app_logo.png"):
                    logo_pixmap = QPixmap("icons/app_logo.png")
                    logo_label.setPixmap(logo_pixmap.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation))
                    header_layout.addWidget(logo_label)
            except Exception as logo_error:
                log(f"加载图标时出错: {logo_error}", "WARNING")

            # 标题和版本信息
            title_version_layout = QVBoxLayout()

            title_label = QLabel(f"性能监控工具集")
            title_label.setAlignment(Qt.AlignCenter)
            title_font = QFont()
            title_font.setPointSize(18)
            title_font.setBold(True)
            title_label.setFont(title_font)
            title_version_layout.addWidget(title_label)

            version_label = QLabel(f"v{__version__} - 发布日期: {__release_date__}")
            version_label.setAlignment(Qt.AlignCenter)
            version_font = QFont()
            version_font.setPointSize(10)
            version_label.setFont(version_font)
            title_version_layout.addWidget(version_label)

            header_layout.addLayout(title_version_layout, 1)
            main_layout.addLayout(header_layout)
            log("添加标题和版本信息完成", "INFO")

            # 添加说明
            description_label = QLabel("请选择要启动的工具，所有工具共享相同的界面风格和设置，提供一致的用户体验。")
            description_label.setAlignment(Qt.AlignCenter)
            description_font = QFont()
            description_font.setPointSize(10)
            description_label.setFont(description_font)
            main_layout.addWidget(description_label)
            log("添加说明完成", "INFO")

            # 添加分隔线
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setFrameShadow(QFrame.Sunken)
            main_layout.addWidget(separator)
            log("添加分隔线完成", "INFO")
        except Exception as e:
            log(f"init_ui方法出错: {e}", "ERROR")
            # 尝试显示错误消息
            try:
                QMessageBox.critical(self, "UI初始化错误", f"UI初始化时出错: {e}")
            except:
                pass
            raise

        try:
            # 创建工具按钮组
            tools_group = QGroupBox("可用工具")
            tools_layout = QGridLayout(tools_group)
            tools_layout.setSpacing(15)
            log("创建工具按钮组完成", "INFO")

            # 工具按钮样式
            button_style = """
                QPushButton {
                    background-color: #2a82da;
                    color: white;
                    border: none;
                    padding: 15px 25px;
                    border-radius: 5px;
                    font-size: 14px;
                    font-weight: bold;
                    text-align: center;
                }
                QPushButton:hover {
                    background-color: #3a92ea;
                }
                QPushButton:pressed {
                    background-color: #1a72ca;
                }
            """

            # 性能监控数据分析器按钮
            self.performance_analyzer_button = QPushButton("性能监控数据分析器")
            self.performance_analyzer_button.setMinimumHeight(80)
            self.performance_analyzer_button.setStyleSheet(button_style)
            self.performance_analyzer_button.setToolTip("启动性能监控数据分析器\n\n用于分析Excel文件中的CPU、内存和磁盘使用率数据\n支持图表生成和数据分析")
            self.performance_analyzer_button.clicked.connect(self.launch_performance_analyzer)

            # 尝试设置图标
            try:
                if os.path.exists("icons/performance_analyzer.png"):
                    self.performance_analyzer_button.setIcon(QIcon("icons/performance_analyzer.png"))
                    self.performance_analyzer_button.setIconSize(QSize(32, 32))
            except Exception as icon_error:
                log(f"设置性能分析器图标时出错: {icon_error}", "WARNING")

            tools_layout.addWidget(self.performance_analyzer_button, 0, 0)
            log("添加性能监控数据分析器按钮完成", "INFO")

            # 统一Nmon文件分析器按钮
            self.unified_nmon_analyzer_button = QPushButton("Nmon文件分析器")
            self.unified_nmon_analyzer_button.setMinimumHeight(80)
            self.unified_nmon_analyzer_button.setStyleSheet(button_style)
            self.unified_nmon_analyzer_button.setToolTip("启动Nmon文件分析器\n\n用于分析nmon文件并生成图表和Excel报告\n支持原始数据分析和趋势分析")
            self.unified_nmon_analyzer_button.clicked.connect(self.launch_unified_nmon_analyzer)

            # 尝试设置图标
            try:
                if os.path.exists("icons/nmon_analyzer.png"):
                    self.unified_nmon_analyzer_button.setIcon(QIcon("icons/nmon_analyzer.png"))
                    self.unified_nmon_analyzer_button.setIconSize(QSize(32, 32))
            except Exception as icon_error:
                log(f"设置Nmon分析器图标时出错: {icon_error}", "WARNING")

            tools_layout.addWidget(self.unified_nmon_analyzer_button, 0, 1)
            log("添加统一Nmon文件分析器按钮完成", "INFO")

            # 日志设置按钮
            self.log_settings_button = QPushButton("日志设置")
            self.log_settings_button.setMinimumHeight(60)
            self.log_settings_button.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-size: 13px;
                }
                QPushButton:hover {
                    background-color: #5CBF60;
                }
                QPushButton:pressed {
                    background-color: #3C9F40;
                }
            """)
            self.log_settings_button.setToolTip("配置日志设置\n\n设置日志目录和日志级别\n清理旧日志文件")
            self.log_settings_button.clicked.connect(self.show_log_settings_dialog)

            # 尝试设置图标
            try:
                if os.path.exists("icons/log_settings.png"):
                    self.log_settings_button.setIcon(QIcon("icons/log_settings.png"))
                    self.log_settings_button.setIconSize(QSize(24, 24))
            except Exception as icon_error:
                log(f"设置日志设置图标时出错: {icon_error}", "WARNING")

            tools_layout.addWidget(self.log_settings_button, 1, 0)
            log("添加日志设置按钮完成", "INFO")

            # 界面设置按钮
            self.ui_settings_button = QPushButton("界面设置")
            self.ui_settings_button.setMinimumHeight(60)
            self.ui_settings_button.setStyleSheet("""
                QPushButton {
                    background-color: #FF9800;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-size: 13px;
                }
                QPushButton:hover {
                    background-color: #FFA820;
                }
                QPushButton:pressed {
                    background-color: #E08800;
                }
            """)
            self.ui_settings_button.setToolTip("配置界面设置\n\n设置界面风格和字体大小\n所有工具共享相同的设置")
            self.ui_settings_button.clicked.connect(self.show_settings_dialog)

            # 尝试设置图标
            try:
                if os.path.exists("icons/ui_settings.png"):
                    self.ui_settings_button.setIcon(QIcon("icons/ui_settings.png"))
                    self.ui_settings_button.setIconSize(QSize(24, 24))
            except Exception as icon_error:
                log(f"设置界面设置图标时出错: {icon_error}", "WARNING")

            tools_layout.addWidget(self.ui_settings_button, 1, 1)
            log("添加界面设置按钮完成", "INFO")

            # 添加工具组到主布局
            main_layout.addWidget(tools_group)
            log("添加工具组到主布局完成", "INFO")

            # 添加状态信息
            status_layout = QHBoxLayout()

            # 添加当前时间
            self.time_label = QLabel()
            self.update_time()
            status_layout.addWidget(self.time_label)

            # 添加定时器更新时间
            self.time_timer = QTimer()
            self.time_timer.timeout.connect(self.update_time)
            self.time_timer.start(1000)  # 每秒更新一次

            # 添加版权信息
            copyright_label = QLabel(f"© 2025 {__author__}")
            copyright_label.setAlignment(Qt.AlignRight)
            status_layout.addWidget(copyright_label, 1)

            main_layout.addLayout(status_layout)
            log("添加状态信息完成", "INFO")
        except Exception as e:
            log(f"创建工具按钮时出错: {e}", "ERROR")
            # 尝试显示错误消息
            try:
                QMessageBox.critical(self, "创建按钮错误", f"创建工具按钮时出错: {e}")
            except:
                pass

    def update_time(self):
        """更新当前时间"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"当前时间: {current_time}")

    def show_log_settings_dialog(self):
        """显示日志设置对话框"""
        try:
            dialog = QDialog(self)
            dialog.setWindowTitle("日志设置")
            dialog.setMinimumWidth(400)

            layout = QFormLayout(dialog)

            # 日志目录输入
            settings = QSettings("PerformanceAnalyzer", "Settings")
            current_log_dir = settings.value("log_dir", "logs", type=str)

            log_dir_input = QLineEdit(current_log_dir)
            layout.addRow("日志目录:", log_dir_input)

            # 浏览按钮
            browse_button = QPushButton("浏览...")
            browse_button.clicked.connect(lambda: self.browse_log_dir(log_dir_input))
            layout.addRow("", browse_button)

            # 清理日志按钮
            clean_logs_button = QPushButton("清理旧日志")
            clean_logs_button.clicked.connect(lambda: self.clean_logs_action(current_log_dir))
            layout.addRow("", clean_logs_button)

            # 按钮盒
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.accepted.connect(dialog.accept)
            button_box.rejected.connect(dialog.reject)
            layout.addRow(button_box)

            # 显示对话框
            if dialog.exec_() == QDialog.Accepted:
                # 保存设置
                new_log_dir = log_dir_input.text().strip()
                if new_log_dir and new_log_dir != current_log_dir:
                    settings.setValue("log_dir", new_log_dir)
                    log(f"日志目录已更新为: {new_log_dir}", "INFO")

                    # 确保日志目录存在
                    if not os.path.exists(new_log_dir):
                        try:
                            os.makedirs(new_log_dir)
                            log(f"创建日志目录: {new_log_dir}", "INFO")
                        except Exception as e:
                            log(f"创建日志目录失败: {e}", "ERROR")

                    # 重新初始化日志
                    log_level = settings.value("log_level", logging.INFO, type=int)
                    init_logger(log_level=log_level, log_dir=new_log_dir)
                    log("日志系统已重新初始化", "INFO")
        except Exception as e:
            log(f"显示日志设置对话框时出错: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"显示日志设置对话框时出错: {e}")

    def browse_log_dir(self, input_field):
        """浏览选择日志目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择日志目录", input_field.text())
        if dir_path:
            input_field.setText(dir_path)

    def clean_logs_action(self, log_dir):
        """清理日志文件"""
        try:
            reply = QMessageBox.question(
                self,
                "清理日志",
                "是否要清理超过30天的日志文件？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                clean_logs(days=30, log_dir=log_dir)
                log("日志文件清理完成", "INFO")
                QMessageBox.information(self, "清理完成", "日志文件清理完成")
        except Exception as e:
            log(f"清理日志文件时出错: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"清理日志文件时出错: {e}")

    def launch_performance_analyzer(self):
        """启动性能监控数据分析器"""
        try:
            log("启动性能监控数据分析器", "INFO")
            # 在启动前同步界面设置
            self.sync_ui_settings()
            log("同步界面设置完成", "INFO")

            # 获取当前目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            log(f"当前目录: {current_dir}", "INFO")

            # 检查启动脚本是否存在
            script_name = "run_performance_analyzer.py"
            script_path = os.path.join(current_dir, script_name)

            # 如果不存在，尝试其他可能的路径
            if not os.path.exists(script_path):
                # 尝试当前工作目录
                script_path = os.path.join(os.getcwd(), script_name)

                if not os.path.exists(script_path):
                    # 尝试直接使用脚本名（如果在PATH中）
                    script_path = script_name

                    # 如果还是找不到，尝试直接启动模块
                    if not os.path.exists(script_path):
                        log(f"启动脚本不存在，尝试直接启动模块", "WARNING")
                        # 直接启动模块
                        try:
                            from gui import MainWindow
                            self.show_launch_message("性能监控数据分析器")
                            # 创建新进程启动模块
                            subprocess.Popen([sys.executable, "-c", """
                            import sys
                            from PyQt5.QtWidgets import QApplication
                            from gui import MainWindow
                            app = QApplication(sys.argv)
                            window = MainWindow()
                            window.show()
                            sys.exit(app.exec_())
                            """])
                            log("直接启动模块成功", "INFO")
                            return
                        except ImportError:
                            log("无法导入性能监控数据分析器模块", "ERROR")
                            self.show_error_message("启动失败", "无法找到性能监控数据分析器模块")
                            return

            log(f"使用启动脚本: {script_path}", "INFO")

            # 使用子进程启动性能监控数据分析器
            # 创建一个新的Python进程来运行性能监控数据分析器
            # 这样可以避免在同一个进程中运行多个PyQt应用的问题
            log(f"尝试启动脚本: {sys.executable} {script_path}", "INFO")
            subprocess.Popen([sys.executable, script_path])
            log("进程启动成功", "INFO")

            self.show_launch_message("性能监控数据分析器")
        except Exception as e:
            log(f"启动性能监控数据分析器失败: {e}", "ERROR")
            # 记录详细的异常信息
            import traceback
            log(f"异常详情: {''.join(traceback.format_exc())}", "ERROR")
            self.show_error_message("启动性能监控数据分析器失败", str(e))

    def launch_unified_nmon_analyzer(self):
        """启动统一Nmon文件分析器"""
        try:
            log("启动Nmon文件分析器", "INFO")
            # 在启动前同步界面设置
            self.sync_ui_settings()
            log("同步界面设置完成", "INFO")

            # 获取当前目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            log(f"当前目录: {current_dir}", "INFO")

            # 检查启动脚本是否存在
            script_name = "run_unified_nmon_analyzer.py"
            script_path = os.path.join(current_dir, script_name)

            # 如果不存在，尝试其他可能的路径
            if not os.path.exists(script_path):
                # 尝试当前工作目录
                script_path = os.path.join(os.getcwd(), script_name)

                if not os.path.exists(script_path):
                    # 尝试直接使用脚本名（如果在PATH中）
                    script_path = script_name

                    # 如果还是找不到，尝试直接启动模块
                    if not os.path.exists(script_path):
                        log(f"启动脚本不存在，尝试直接启动模块", "WARNING")
                        # 直接启动模块
                        try:
                            from unified_nmon_analyzer import UnifiedNmonAnalyzerApp
                            self.show_launch_message("Nmon文件分析器")
                            # 创建新进程启动模块
                            subprocess.Popen([sys.executable, "-c", """
                            import sys
                            from PyQt5.QtWidgets import QApplication
                            from unified_nmon_analyzer import UnifiedNmonAnalyzerApp
                            app = QApplication(sys.argv)
                            window = UnifiedNmonAnalyzerApp()
                            window.show()
                            sys.exit(app.exec_())
                            """])
                            log("直接启动模块成功", "INFO")
                            return
                        except ImportError:
                            log("无法导入Nmon文件分析器模块", "ERROR")
                            self.show_error_message("启动失败", "无法找到Nmon文件分析器模块")
                            return

            log(f"使用启动脚本: {script_path}", "INFO")

            # 启动进程
            log(f"尝试启动脚本: {sys.executable} {script_path}", "INFO")
            subprocess.Popen([sys.executable, script_path])
            log("进程启动成功", "INFO")

            self.show_launch_message("Nmon文件分析器")
        except Exception as e:
            log(f"启动Nmon文件分析器失败: {e}", "ERROR")
            # 记录详细的异常信息
            import traceback
            log(f"异常详情: {''.join(traceback.format_exc())}", "ERROR")
            self.show_error_message("启动Nmon文件分析器失败", str(e))

    def show_launch_message(self, tool_name):
        """显示启动成功消息"""
        QMessageBox.information(self, "启动成功", f"{tool_name}已成功启动。")

    def show_error_message(self, title, error_message):
        """显示错误消息"""
        QMessageBox.critical(self, title, f"错误: {error_message}")
        log(f"{title}: {error_message}", "ERROR")

    def create_menu_bar(self):
        """创建菜单栏"""
        menu_bar = self.menuBar()

        # 文件菜单
        file_menu = menu_bar.addMenu("文件")

        # 退出动作
        exit_action = QAction("退出", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 设置菜单
        settings_menu = menu_bar.addMenu("设置")

        # 界面设置动作
        ui_settings_action = QAction("界面设置", self)
        ui_settings_action.triggered.connect(self.show_settings_dialog)
        settings_menu.addAction(ui_settings_action)

        # 帮助菜单
        help_menu = menu_bar.addMenu("帮助")

        # 关于动作
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)

    def sync_ui_settings(self):
        """同步界面和日志设置到所有工具"""
        try:
            # 获取当前设置
            settings = QSettings("PerformanceAnalyzer", "Settings")
            style_name = settings.value("ui_style", "modern_blue", type=str)
            font_size = settings.value("font_size", 9, type=int)
            log_level = settings.value("log_level", logging.INFO, type=int)

            # 记录同步的设置
            log_level_name = logging.getLevelName(log_level)
            log(f"同步设置: 样式={style_name}, 字体大小={font_size}, 日志级别={log_level_name}")

            # 确保设置已同步到磁盘
            settings.sync()

            # 更新当前启动器的日志级别
            from logger_utils import init_logger
            init_logger(log_level=log_level)
        except Exception as e:
            log(f"同步设置时出错: {str(e)}", "ERROR")

    def show_settings_dialog(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self)
        if dialog.exec_():
            # 应用新的样式和日志设置
            self.set_style()

            # 同步设置到所有工具
            self.sync_ui_settings()

            log("已应用新的设置")

    def show_about_dialog(self):
        """显示关于对话框"""
        about_text = f"""
        <h2>性能监控工具集 v{__version__}</h2>
        <p>发布日期: {__release_date__}</p>
        <p>这是一个集成了多种性能监控工具的统一界面，提供一致的用户体验。</p>

        <h3>包含的工具:</h3>
        <ul>
            <li><b>性能监控数据分析器</b> - 用于分析Excel文件中的CPU、内存和磁盘使用率数据</li>
            <li><b>Nmon文件分析器</b> - 用于分析nmon文件并生成图表和Excel报告</li>
        </ul>

        <h3>v3.5版本新特性:</h3>
        <ul>
            <li>优化的统一入口界面，提供更好的用户体验</li>
            <li>改进的工具切换和管理功能</li>
            <li>新增日志目录设置功能，可自定义日志保存位置</li>
            <li>优化的日志系统，减少系统缓存使用</li>
            <li>修复了原始Nmon文件分析器中的KeyError错误</li>
            <li>改进的界面风格和主题设置</li>
        </ul>

        <p>&copy; 2025 {__author__}</p>
        """
        QMessageBox.about(self, "关于性能监控工具集", about_text)

    def closeEvent(self, event):
        """处理窗口关闭事件"""
        log("用户关闭启动器", "INFO")
        event.accept()


def main():
    """主函数。"""
    # 初始化日志
    init_logger(None)

    app = QApplication(sys.argv)
    window = UnifiedLauncher()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
