"""
测试保存选项对话框和成功消息

这个脚本用于测试保存选项对话框和成功消息的显示。
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox, QDialog
from PyQt5.QtCore import QTimer

from save_options_dialog import SaveOptionsDialog

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试保存选项对话框和成功消息")
        self.setGeometry(100, 100, 500, 300)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建按钮
        self.show_dialog_button = QPushButton("显示保存选项对话框")
        self.show_dialog_button.clicked.connect(self.show_save_options)
        layout.addWidget(self.show_dialog_button)
        
        self.show_success_button = QPushButton("显示成功消息")
        self.show_success_button.clicked.connect(self.show_success_message)
        layout.addWidget(self.show_success_button)
        
        self.show_both_button = QPushButton("显示对话框和成功消息")
        self.show_both_button.clicked.connect(self.show_both)
        layout.addWidget(self.show_both_button)
    
    def show_save_options(self):
        """显示保存选项对话框"""
        dialog = SaveOptionsDialog(file_count=25)
        result = dialog.exec_()
        
        if result == QDialog.Accepted:
            selected_mode = dialog.get_selected_mode()
            batch_size = dialog.get_batch_size()
            print(f"选择的模式: {selected_mode}, 批次大小: {batch_size}")
    
    def show_success_message(self):
        """显示成功消息"""
        QMessageBox.information(self, "成功", "结果已成功保存到 test.xlsx")
    
    def show_both(self):
        """显示对话框和成功消息"""
        dialog = SaveOptionsDialog(file_count=25)
        result = dialog.exec_()
        
        if result == QDialog.Accepted:
            selected_mode = dialog.get_selected_mode()
            batch_size = dialog.get_batch_size()
            print(f"选择的模式: {selected_mode}, 批次大小: {batch_size}")
            
            # 使用定时器延迟显示成功消息，模拟实际情况
            QTimer.singleShot(500, self.show_success_message)

def main():
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
