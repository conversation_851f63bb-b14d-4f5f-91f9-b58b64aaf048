from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QRadioButton, QPushButton, QButtonGroup, QGroupBox)
from PyQt5.QtCore import Qt

class SaveOptionsDialog(QDialog):
    """用于选择保存选项的对话框"""

    def __init__(self, parent=None, file_count=0):
        """
        初始化保存选项对话框

        参数:
            parent: 父窗口
            file_count: 文件数量
        """
        super().__init__(parent)
        self.file_count = file_count
        self.selected_mode = 'auto'  # 默认选项

        self.setWindowTitle("保存选项")
        self.setMinimumWidth(400)

        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()

        # 添加说明标签
        if self.file_count > 20:
            info_text = f"检测到大量文件 ({self.file_count}个)，请选择保存方式:"
        else:
            info_text = f"选择保存方式 (当前文件数: {self.file_count}个):"

        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 创建选项组
        options_group = QGroupBox("保存选项")
        options_layout = QVBoxLayout()

        # 创建单选按钮
        self.auto_radio = QRadioButton("自动模式 (根据文件数量自动选择最佳方式)")
        self.summary_radio = QRadioButton("仅保存摘要 (不生成图表，速度最快)")
        self.full_radio = QRadioButton("完整模式 (生成所有图表，适用于少量文件)")
        self.batch_radio = QRadioButton("批处理模式 (分批生成图表，适用于大量文件)")

        # 设置默认选项
        if self.file_count > 20:
            self.batch_radio.setChecked(True)
            self.selected_mode = 'batch'
        else:
            self.auto_radio.setChecked(True)
            self.selected_mode = 'auto'

        # 添加单选按钮到布局
        options_layout.addWidget(self.auto_radio)
        options_layout.addWidget(self.summary_radio)
        options_layout.addWidget(self.full_radio)
        options_layout.addWidget(self.batch_radio)

        # 创建按钮组
        self.button_group = QButtonGroup()
        self.button_group.addButton(self.auto_radio, 1)
        self.button_group.addButton(self.summary_radio, 2)
        self.button_group.addButton(self.full_radio, 3)
        self.button_group.addButton(self.batch_radio, 4)
        self.button_group.buttonClicked.connect(self.on_button_clicked)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # 添加说明文本
        description_label = QLabel(
            "说明:\n"
            "- 自动模式: 根据文件数量自动选择最佳保存方式\n"
            "- 仅保存摘要: 只保存数据摘要，不生成图表，速度最快\n"
            "- 完整模式: 生成所有图表，适用于少量文件\n"
            "- 批处理模式: 分批生成图表，适用于大量文件"
        )
        description_label.setWordWrap(True)
        layout.addWidget(description_label)

        # 添加按钮
        button_layout = QHBoxLayout()
        ok_button = QPushButton("确定")
        cancel_button = QPushButton("取消")

        # 使用自定义方法处理确定按钮点击
        ok_button.clicked.connect(self.on_ok_clicked)
        cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def on_button_clicked(self, button):
        """处理按钮点击事件"""
        if button == self.auto_radio:
            self.selected_mode = 'auto'
        elif button == self.summary_radio:
            self.selected_mode = 'summary_only'
        elif button == self.full_radio:
            self.selected_mode = 'full'
        elif button == self.batch_radio:
            self.selected_mode = 'batch'

    def on_ok_clicked(self):
        """处理确定按钮点击事件"""
        # 先记录当前选择的模式
        mode = self.selected_mode
        # 然后接受对话框
        self.accept()

    def get_selected_mode(self):
        """获取选择的保存模式"""
        return self.selected_mode
