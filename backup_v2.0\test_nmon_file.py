"""
测试nmon文件解析

这个脚本用于测试nmon_parser.py对nmon文件的解析功能。
"""

import os
import sys
import pandas as pd
from nmon_parser import NmonParser

def analyze_nmon_file(nmon_file_path, output_dir=None):
    """
    分析nmon文件并生成Excel报告。
    
    参数:
        nmon_file_path: nmon文件路径
        output_dir: 输出目录，默认为nmon文件所在目录
        
    返回:
        str: 输出文件路径
    """
    print(f"分析nmon文件: {nmon_file_path}")
    
    # 设置输出目录
    if output_dir is None:
        output_dir = os.path.dirname(nmon_file_path)
        
    # 创建解析器
    parser = NmonParser(nmon_file_path)
    
    # 解析nmon文件
    result = parser.parse()
    
    # 打印基本信息
    print(f"\n基本信息:")
    print(f"主机名: {result['hostname']}")
    print(f"操作系统: {result['os_version']}")
    print(f"日期: {result['date']}")
    print(f"时间: {result['time']}")
    print(f"采样间隔(秒): {result['interval_seconds']}")
    
    # 打印时间戳信息
    print(f"\n时间戳数量: {len(result['timestamps'])}")
    
    # 打印数据计数
    print(f"\n数据计数:")
    print(f"CPU数据: {len(result['cpu_data'])}")
    print(f"内存数据: {len(result['memory_data'])}")
    print(f"磁盘数据: {len(result['disk_data'])}")
    print(f"网络数据: {len(result['network_data'])}")
    
    # 生成Excel报告
    output_file = os.path.join(
        output_dir, 
        os.path.splitext(os.path.basename(nmon_file_path))[0] + "_analyzed.xlsx"
    )
    
    # 提取时间序列数据
    timestamps = [ts_info['timestamp'] for ts_id, ts_info in sorted(result['timestamps'].items())]
    
    # 创建数据帧
    dfs = {}
    
    # CPU数据
    if result['cpu_data']:
        cpu_data = {
            '时间戳': timestamps,
            'CPU使用率(%)': [data['busy'] for _, data in sorted(result['cpu_data'].items())],
            'CPU用户态(%)': [data['user'] for _, data in sorted(result['cpu_data'].items())],
            'CPU系统态(%)': [data['sys'] for _, data in sorted(result['cpu_data'].items())],
            'CPU等待IO(%)': [data['wait'] for _, data in sorted(result['cpu_data'].items())],
            'CPU空闲(%)': [data['idle'] for _, data in sorted(result['cpu_data'].items())]
        }
        dfs['CPU'] = pd.DataFrame(cpu_data)
    
    # 内存数据
    if result['memory_data']:
        memory_data = {
            '时间戳': timestamps,
            '内存总量(MB)': [data['total'] for _, data in sorted(result['memory_data'].items())],
            '内存空闲(MB)': [data['free'] for _, data in sorted(result['memory_data'].items())],
            '内存使用率(%)': [((data['total'] - data['free']) / data['total'] * 100) if data['total'] > 0 else 0 
                         for _, data in sorted(result['memory_data'].items())]
        }
        dfs['内存'] = pd.DataFrame(memory_data)
    
    # 磁盘数据
    disk_names = set()
    for _, disks in result['disk_data'].items():
        disk_names.update(disks.keys())
    
    for disk_name in disk_names:
        disk_data = {
            '时间戳': timestamps,
            f'使用率(%)': [],
            f'读取(KB/s)': [],
            f'写入(KB/s)': []
        }
        
        for ts_id, _ in sorted(result['timestamps'].items()):
            if ts_id in result['disk_data'] and disk_name in result['disk_data'][ts_id]:
                data = result['disk_data'][ts_id][disk_name]
                disk_data[f'使用率(%)'].append(data['busy'])
                disk_data[f'读取(KB/s)'].append(data['read'])
                disk_data[f'写入(KB/s)'].append(data['write'])
            else:
                disk_data[f'使用率(%)'].append(0)
                disk_data[f'读取(KB/s)'].append(0)
                disk_data[f'写入(KB/s)'].append(0)
                
        dfs[f'磁盘_{disk_name}'] = pd.DataFrame(disk_data)
    
    # 网络数据
    interface_names = set()
    for _, interfaces in result['network_data'].items():
        interface_names.update(interfaces.keys())
    
    for interface_name in interface_names:
        net_data = {
            '时间戳': timestamps,
            f'读取(KB/s)': [],
            f'写入(KB/s)': []
        }
        
        for ts_id, _ in sorted(result['timestamps'].items()):
            if ts_id in result['network_data'] and interface_name in result['network_data'][ts_id]:
                data = result['network_data'][ts_id][interface_name]
                net_data[f'读取(KB/s)'].append(data['read'])
                net_data[f'写入(KB/s)'].append(data['write'])
            else:
                net_data[f'读取(KB/s)'].append(0)
                net_data[f'写入(KB/s)'].append(0)
                
        dfs[f'网络_{interface_name}'] = pd.DataFrame(net_data)
    
    # 写入Excel文件
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        for sheet_name, df in dfs.items():
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    print(f"\n分析完成，结果已保存到: {output_file}")
    return output_file

def main():
    """主函数。"""
    if len(sys.argv) > 1:
        nmon_file = sys.argv[1]
        output_dir = sys.argv[2] if len(sys.argv) > 2 else None
        
        try:
            analyze_nmon_file(nmon_file, output_dir)
        except Exception as e:
            print(f"错误: {str(e)}")
            import traceback
            print(traceback.format_exc())
            sys.exit(1)
    else:
        print("用法: python test_nmon_file.py <nmon文件路径> [输出目录]")

if __name__ == "__main__":
    main()
