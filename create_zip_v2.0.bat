@echo off
echo Creating ZIP archive for Performance Analyzer V2.0...
echo.

REM Set the source directory and output file
set SOURCE_DIR=dist\性能监控数据分析器_V2.0
set OUTPUT_FILE=dist\性能监控数据分析器_V2.0.zip

REM Create the ZIP archive
powershell -command "Compress-Archive -Path '%SOURCE_DIR%\*' -DestinationPath '%OUTPUT_FILE%' -Force"

if %ERRORLEVEL% == 0 (
    echo ZIP archive created successfully!
    echo Archive location: %OUTPUT_FILE%
) else (
    echo Failed to create ZIP archive. Error code: %ERRORLEVEL%
)

echo.
pause
