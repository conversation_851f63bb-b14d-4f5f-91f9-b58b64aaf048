"""
Backup script to create a backup of the current source code as version 2.0.
"""

import os
import shutil
import datetime

def backup_source_code():
    """Backup all source code files to the backup_v2.0 directory."""
    # Source directory (current directory)
    source_dir = os.getcwd()

    # Backup directory
    backup_dir = os.path.join(source_dir, 'backup_v2.0')

    # Ensure backup directory exists
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)

    # Files to backup (Python files and other important files)
    extensions_to_backup = ['.py', '.md', '.txt', '.spec', '.bat', '.json']

    # Directories to exclude
    dirs_to_exclude = ['__pycache__', 'build', 'dist', 'backup_v1.0', 'backup_v1.1', 'backup_v1.5', '.idea', 'logs']

    # Files to exclude
    files_to_exclude = ['backup_source_v2.0.py']

    # Count of backed up files
    backed_up_count = 0

    # Create version info file
    version_info_path = os.path.join(backup_dir, 'version_info.txt')
    with open(version_info_path, 'w', encoding='utf-8') as f:
        f.write(f"Version: 2.0\n")
        f.write(f"Backup Date: {datetime.datetime.now().strftime('%Y-%m-%d')}\n")
        f.write(f"Description: Stable release with unified interface and improved performance.\n\n")
        f.write(f"Files included in this backup:\n")

    # Walk through the directory tree
    for root, dirs, files in os.walk(source_dir):
        # Skip excluded directories
        dirs[:] = [d for d in dirs if d not in dirs_to_exclude]

        # Process each file
        for file in files:
            # Skip excluded files
            if file in files_to_exclude:
                continue

            # Check if the file has an extension to backup
            _, ext = os.path.splitext(file)
            if ext.lower() in extensions_to_backup:
                # Source file path
                source_file = os.path.join(root, file)

                # Get the relative path from the source directory
                rel_path = os.path.relpath(source_file, source_dir)

                # Destination file path
                dest_file = os.path.join(backup_dir, rel_path)

                # Create destination directory if it doesn't exist
                dest_dir = os.path.dirname(dest_file)
                if not os.path.exists(dest_dir):
                    os.makedirs(dest_dir)

                # Copy the file
                shutil.copy2(source_file, dest_file)
                backed_up_count += 1

                # Append to version info file
                with open(version_info_path, 'a', encoding='utf-8') as f:
                    f.write(f"- {rel_path}\n")

                print(f"Backed up: {rel_path}")

    # Also backup the icons directory
    icons_dir = os.path.join(source_dir, 'icons')
    if os.path.exists(icons_dir):
        icons_backup_dir = os.path.join(backup_dir, 'icons')
        if not os.path.exists(icons_backup_dir):
            os.makedirs(icons_backup_dir)

        for icon_file in os.listdir(icons_dir):
            source_icon = os.path.join(icons_dir, icon_file)
            if os.path.isfile(source_icon):
                dest_icon = os.path.join(icons_backup_dir, icon_file)
                shutil.copy2(source_icon, dest_icon)
                backed_up_count += 1
                print(f"Backed up: icons/{icon_file}")

                # Append to version info file
                with open(version_info_path, 'a', encoding='utf-8') as f:
                    f.write(f"- icons/{icon_file}\n")

    # Also backup the fonts directory
    fonts_dir = os.path.join(source_dir, 'fonts')
    if os.path.exists(fonts_dir):
        fonts_backup_dir = os.path.join(backup_dir, 'fonts')
        if not os.path.exists(fonts_backup_dir):
            os.makedirs(fonts_backup_dir)

        for font_file in os.listdir(fonts_dir):
            source_font = os.path.join(fonts_dir, font_file)
            if os.path.isfile(source_font):
                dest_font = os.path.join(fonts_backup_dir, font_file)
                shutil.copy2(source_font, dest_font)
                backed_up_count += 1
                print(f"Backed up: fonts/{font_file}")

                # Append to version info file
                with open(version_info_path, 'a', encoding='utf-8') as f:
                    f.write(f"- fonts/{font_file}\n")

    # Create a README file for the backup
    readme_path = os.path.join(backup_dir, 'README_BACKUP.md')
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(f"# 性能监控数据分析器 V2.0 - 源代码备份\n\n")
        f.write(f"这是性能监控数据分析器 V2.0 版本的完整源代码备份。\n\n")
        f.write(f"## 备份内容\n\n")
        f.write(f"此备份包含以下内容：\n\n")
        f.write(f"1. 所有Python源代码文件\n")
        f.write(f"2. 内嵌字体文件（fonts目录）\n")
        f.write(f"3. 应用图标文件（icons目录）\n")
        f.write(f"4. 配置和构建文件\n")
        f.write(f"5. 版本信息文件\n\n")
        f.write(f"## 版本特性\n\n")
        f.write(f"V2.0版本包含以下主要特性和改进：\n\n")
        f.write(f"- 统一的界面设计，提供一致的用户体验\n")
        f.write(f"- 优化的性能监控数据分析功能\n")
        f.write(f"- 改进的nmon文件分析器\n")
        f.write(f"- 增强的原始nmon文件分析器\n")
        f.write(f"- 优化的图表生成和显示\n")
        f.write(f"- 改进的保存选项和结果导出\n")
        f.write(f"- 更好的错误处理和日志记录\n\n")
        f.write(f"## 恢复备份\n\n")
        f.write(f"如需从此备份恢复源代码，只需将所有文件复制到一个新的工作目录即可。\n\n")
        f.write(f"## 构建可执行文件\n\n")
        f.write(f"要从源代码构建可执行文件，请运行：\n\n")
        f.write(f"```\n")
        f.write(f"python -m PyInstaller performance_analyzer.spec\n")
        f.write(f"```\n\n")
        f.write(f"构建完成后，可执行文件将位于`dist/性能监控数据分析器_V2.0`目录中。\n\n")
        f.write(f"## 备份日期\n\n")
        f.write(f"备份日期：{datetime.datetime.now().strftime('%Y-%m-%d')}\n")

    # Create a build script for v2.0
    build_script_path = os.path.join(backup_dir, 'build_v20.bat')
    with open(build_script_path, 'w', encoding='utf-8') as f:
        f.write(f"@echo off\n")
        f.write(f"echo Building executable for Performance Analyzer V2.0...\n")
        f.write(f"echo.\n\n")
        f.write(f"REM Clean previous build files\n")
        f.write(f"echo Cleaning previous build files...\n")
        f.write(f"rmdir /s /q build\n")
        f.write(f"rmdir /s /q dist\n")
        f.write(f"echo.\n\n")
        f.write(f"REM Run PyInstaller with the spec file\n")
        f.write(f"echo Running PyInstaller...\n")
        f.write(f"python -m PyInstaller performance_analyzer_v2.spec\n")
        f.write(f"echo.\n\n")
        f.write(f"if %ERRORLEVEL% == 0 (\n")
        f.write(f"    echo Build completed successfully!\n")
        f.write(f"    echo Executable is located in the dist\\性能监控数据分析器_V2.0 directory.\n")
        f.write(f") else (\n")
        f.write(f"    echo Build failed with error code %ERRORLEVEL%.\n")
        f.write(f")\n\n")
        f.write(f"echo.\n")
        f.write(f"pause\n")

    # Create a spec file for v2.0
    spec_file_path = os.path.join(backup_dir, 'performance_analyzer_v2.spec')
    with open(spec_file_path, 'w', encoding='utf-8') as f:
        f.write(f"# -*- mode: python ; coding: utf-8 -*-\n\n")
        f.write(f"import os\n")
        f.write(f"block_cipher = None\n\n")
        f.write(f"a = Analysis(\n")
        f.write(f"    ['main.py'],\n")
        f.write(f"    pathex=[],\n")
        f.write(f"    binaries=[],\n")
        f.write(f"    datas=[\n")
        f.write(f"        ('fonts', 'fonts'),  # Include the fonts directory\n")
        f.write(f"        ('icons', 'icons'),  # Include the icons directory\n")
        f.write(f"        ('README.md', '.'),  # Include the readme file\n")
        f.write(f"    ],\n")
        f.write(f"    hiddenimports=[\n")
        f.write(f"        'pandas',\n")
        f.write(f"        'numpy',\n")
        f.write(f"        'matplotlib',\n")
        f.write(f"        'openpyxl',\n")
        f.write(f"        'PyQt5',\n")
        f.write(f"    ],\n")
        f.write(f"    hookspath=[],\n")
        f.write(f"    hooksconfig={{}},\n")
        f.write(f"    runtime_hooks=[],\n")
        f.write(f"    excludes=[],\n")
        f.write(f"    win_no_prefer_redirects=False,\n")
        f.write(f"    win_private_assemblies=False,\n")
        f.write(f"    cipher=block_cipher,\n")
        f.write(f"    noarchive=False,\n")
        f.write(f")\n\n")
        f.write(f"pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)\n\n")
        f.write(f"exe = EXE(\n")
        f.write(f"    pyz,\n")
        f.write(f"    a.scripts,\n")
        f.write(f"    [],\n")
        f.write(f"    exclude_binaries=True,\n")
        f.write(f"    name='性能监控数据分析器_V2.0',\n")
        f.write(f"    debug=False,\n")
        f.write(f"    bootloader_ignore_signals=False,\n")
        f.write(f"    strip=False,\n")
        f.write(f"    upx=True,\n")
        f.write(f"    console=False,\n")
        f.write(f"    disable_windowed_traceback=False,\n")
        f.write(f"    argv_emulation=False,\n")
        f.write(f"    target_arch=None,\n")
        f.write(f"    codesign_identity=None,\n")
        f.write(f"    entitlements_file=None,\n")
        f.write(f"    icon='icons/app_icon.ico' if os.path.exists('icons/app_icon.ico') else None,\n")
        f.write(f")\n\n")
        f.write(f"coll = COLLECT(\n")
        f.write(f"    exe,\n")
        f.write(f"    a.binaries,\n")
        f.write(f"    a.zipfiles,\n")
        f.write(f"    a.datas,\n")
        f.write(f"    strip=False,\n")
        f.write(f"    upx=True,\n")
        f.write(f"    upx_exclude=[],\n")
        f.write(f"    name='性能监控数据分析器_V2.0',\n")
        f.write(f")\n")

    print(f"\nBackup completed successfully!")
    print(f"Total files backed up: {backed_up_count}")
    print(f"Backup directory: {backup_dir}")
    print(f"Version info file: {version_info_path}")
    print(f"README file: {readme_path}")
    print(f"Build script: {build_script_path}")
    print(f"Spec file: {spec_file_path}")

if __name__ == "__main__":
    backup_source_code()
