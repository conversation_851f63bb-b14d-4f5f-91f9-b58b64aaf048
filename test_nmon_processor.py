"""
Nmon处理器测试脚本

这个脚本用于测试nmon_processor和nmon_processor_gui的功能。
"""

import os
import sys
import argparse
from nmon_processor import NmonProcessor
from nmon_processor_integration import process_nmon_file

def test_processor(nmon_file, interval_minutes=30, delay_minutes=0):
    """测试nmon_processor的功能。"""
    print(f"测试nmon_processor: {nmon_file}")
    print(f"采样间隔: {interval_minutes}分钟")
    print(f"延迟时间: {delay_minutes}分钟")
    
    # 创建处理器
    processor = NmonProcessor(nmon_file, interval_minutes, delay_minutes)
    
    # 处理nmon文件
    result = processor.process()
    
    # 打印结果
    print("\n处理结果:")
    print(f"CPU使用率: {result['cpu_usage']}")
    print(f"内存使用率: {result['mem_usage']}")
    print(f"Swap使用率: {result['swap_usage']}")
    print(f"PageOut页数: {result['page_out']}")
    print(f"SwapOut页数: {result['swap_out']}")
    
    print("\n网络接口:")
    for interface, values in result['network'].items():
        print(f"{interface}: {values}")
    
    print("\n磁盘忙碌度:")
    for disk, values in result['disk_busy'].items():
        print(f"{disk}: {values}")
    
    print("\n磁盘传输:")
    for disk, values in result['disk_xfer'].items():
        print(f"{disk}: {values}")
    
    return result

def test_integration(nmon_file, interval_minutes=30, delay_minutes=0, output_dir=None, format='excel'):
    """测试nmon_processor_integration的功能。"""
    print(f"测试nmon_processor_integration: {nmon_file}")
    print(f"采样间隔: {interval_minutes}分钟")
    print(f"延迟时间: {delay_minutes}分钟")
    print(f"输出目录: {output_dir or '默认'}")
    print(f"输出格式: {format}")
    
    # 处理nmon文件
    result = process_nmon_file(nmon_file, interval_minutes, delay_minutes, output_dir, format)
    
    # 打印结果
    print("\n处理结果:")
    if 'excel_file' in result:
        print(f"Excel文件: {result['excel_file']}")
    
    if 'cpu_file' in result:
        print(f"CPU数据文件: {result['cpu_file']}")
        print(f"内存数据文件: {result['mem_file']}")
        print(f"网络数据文件: {result['net_file']}")
        print(f"磁盘数据文件: {result['disk_file']}")
    
    return result

def main():
    """主函数。"""
    parser = argparse.ArgumentParser(description='测试nmon处理器')
    parser.add_argument('-f', '--file', type=str, required=True, help='nmon文件路径')
    parser.add_argument('-m', '--minutes', type=float, default=30, help='采样时间间隔（分钟），默认为30')
    parser.add_argument('-d', '--delay', type=float, default=0, help='开始处理前的延迟时间（分钟），默认为0')
    parser.add_argument('-o', '--output', type=str, help='输出目录，默认为nmon文件所在目录')
    parser.add_argument('-t', '--type', type=str, choices=['processor', 'integration', 'both'], default='both', help='测试类型，默认为both')
    parser.add_argument('-fmt', '--format', type=str, choices=['excel', 'csv', 'analyzer'], default='excel', help='输出格式，默认为excel')
    
    args = parser.parse_args()
    
    try:
        if args.type in ['processor', 'both']:
            test_processor(args.file, args.minutes, args.delay)
        
        if args.type in ['integration', 'both']:
            test_integration(args.file, args.minutes, args.delay, args.output, args.format)
        
        print("\n测试完成")
    
    except Exception as e:
        print(f"测试出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
