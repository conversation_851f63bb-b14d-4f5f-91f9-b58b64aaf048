from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QComboBox, QPushButton, QGroupBox, QFileDialog,
                             QLineEdit, QFormLayout, QTabWidget, QWidget,
                             QMessageBox)
from PyQt5.QtCore import Qt, QSettings
from PyQt5.QtGui import QFont
import os
import logging

class SettingsDialog(QDialog):
    """设置对话框，用于配置应用程序设置"""

    def __init__(self, parent=None):
        """初始化设置对话框"""
        super().__init__(parent)

        self.setWindowTitle("应用程序设置")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)

        # 加载设置
        self.settings = QSettings("PerformanceAnalyzer", "Settings")

        # 初始化UI
        self.init_ui()

        # 加载当前设置
        self.load_settings()

    def init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout()

        # 创建选项卡
        tab_widget = QTabWidget()

        # 常规设置选项卡
        general_tab = QWidget()
        general_layout = QVBoxLayout(general_tab)

        # 日志设置组
        log_group = QGroupBox("日志设置")
        log_layout = QFormLayout()

        # 日志级别选择
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItem("调试 (DEBUG)", logging.DEBUG)
        self.log_level_combo.addItem("信息 (INFO)", logging.INFO)
        self.log_level_combo.addItem("警告 (WARNING)", logging.WARNING)
        self.log_level_combo.addItem("错误 (ERROR)", logging.ERROR)
        self.log_level_combo.addItem("严重 (CRITICAL)", logging.CRITICAL)

        log_layout.addRow("日志级别:", self.log_level_combo)
        log_group.setLayout(log_layout)
        general_layout.addWidget(log_group)

        # 临时目录设置组
        temp_dir_group = QGroupBox("临时目录设置")
        temp_dir_layout = QVBoxLayout()

        temp_dir_form = QFormLayout()
        self.temp_dir_edit = QLineEdit()
        self.temp_dir_edit.setReadOnly(True)
        temp_dir_form.addRow("临时目录:", self.temp_dir_edit)
        temp_dir_layout.addLayout(temp_dir_form)

        temp_dir_button_layout = QHBoxLayout()
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_temp_dir)
        self.default_button = QPushButton("恢复默认")
        self.default_button.clicked.connect(self.reset_temp_dir)

        temp_dir_button_layout.addWidget(self.browse_button)
        temp_dir_button_layout.addWidget(self.default_button)
        temp_dir_layout.addLayout(temp_dir_button_layout)

        temp_dir_group.setLayout(temp_dir_layout)
        general_layout.addWidget(temp_dir_group)

        # 添加说明
        info_label = QLabel("注意: 更改这些设置将在应用程序重启后生效。")
        info_label.setStyleSheet("color: #666; font-style: italic;")
        general_layout.addWidget(info_label)

        # 添加弹性空间
        general_layout.addStretch()

        # 添加常规选项卡
        tab_widget.addTab(general_tab, "常规设置")

        # 界面设置选项卡
        ui_tab = QWidget()
        ui_layout = QVBoxLayout(ui_tab)

        # 界面样式设置组
        style_group = QGroupBox("界面样式")
        style_layout = QFormLayout()

        # 样式选择
        self.style_combo = QComboBox()
        self.style_combo.addItem("现代蓝 (Modern Blue)", "modern_blue")
        self.style_combo.addItem("浅色经典 (Light Classic)", "light_classic")
        self.style_combo.addItem("系统默认 (System Default)", "system_default")

        style_layout.addRow("界面样式:", self.style_combo)
        style_group.setLayout(style_layout)
        ui_layout.addWidget(style_group)

        # 字体设置组
        font_group = QGroupBox("字体设置")
        font_layout = QVBoxLayout()

        # 字体大小设置
        font_size_form = QFormLayout()
        self.font_size_combo = QComboBox()
        for size in range(8, 17):
            self.font_size_combo.addItem(f"{size}pt", size)
        font_size_form.addRow("界面字体大小:", self.font_size_combo)
        font_layout.addLayout(font_size_form)

        # 图表字体设置按钮
        chart_font_layout = QHBoxLayout()
        self.chart_font_button = QPushButton("图表字体设置")
        self.chart_font_button.clicked.connect(self.show_font_settings)
        chart_font_layout.addWidget(self.chart_font_button)
        font_layout.addLayout(chart_font_layout)

        font_group.setLayout(font_layout)
        ui_layout.addWidget(font_group)

        # 添加说明
        ui_info_label = QLabel("注意: 界面样式更改将在应用程序重启后完全生效。")
        ui_info_label.setStyleSheet("color: #666; font-style: italic;")
        ui_layout.addWidget(ui_info_label)

        # 添加弹性空间
        ui_layout.addStretch()

        # 添加界面选项卡
        tab_widget.addTab(ui_tab, "界面设置")

        main_layout.addWidget(tab_widget)

        # 按钮布局
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("确定")
        self.cancel_button = QPushButton("取消")
        self.apply_button = QPushButton("应用")

        self.ok_button.clicked.connect(self.accept_settings)
        self.cancel_button.clicked.connect(self.reject)
        self.apply_button.clicked.connect(self.apply_settings)

        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.apply_button)

        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def load_settings(self):
        """加载当前设置"""
        # 加载日志级别
        log_level = self.settings.value("log_level", logging.INFO, type=int)
        index = self.log_level_combo.findData(log_level)
        if index >= 0:
            self.log_level_combo.setCurrentIndex(index)

        # 加载临时目录
        import tempfile
        default_temp_dir = tempfile.gettempdir()
        temp_dir = self.settings.value("temp_directory", default_temp_dir, type=str)
        self.temp_dir_edit.setText(temp_dir)

        # 加载界面样式
        style = self.settings.value("ui_style", "modern_blue", type=str)
        index = self.style_combo.findData(style)
        if index >= 0:
            self.style_combo.setCurrentIndex(index)

        # 加载字体大小
        font_size = self.settings.value("font_size", 9, type=int)
        index = self.font_size_combo.findData(font_size)
        if index >= 0:
            self.font_size_combo.setCurrentIndex(index)

    def browse_temp_dir(self):
        """浏览并选择临时目录"""
        current_dir = self.temp_dir_edit.text()
        if not os.path.isdir(current_dir):
            current_dir = os.path.expanduser("~")

        dir_path = QFileDialog.getExistingDirectory(
            self, "选择临时目录", current_dir,
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )

        if dir_path:
            self.temp_dir_edit.setText(dir_path)

    def reset_temp_dir(self):
        """重置临时目录为系统默认"""
        import tempfile
        self.temp_dir_edit.setText(tempfile.gettempdir())

    def save_settings(self):
        """保存设置"""
        # 保存日志级别
        log_level = self.log_level_combo.currentData()
        self.settings.setValue("log_level", log_level)

        # 保存临时目录
        temp_dir = self.temp_dir_edit.text()
        self.settings.setValue("temp_directory", temp_dir)

        # 保存界面样式
        style = self.style_combo.currentData()
        self.settings.setValue("ui_style", style)

        # 保存字体大小
        font_size = self.font_size_combo.currentData()
        self.settings.setValue("font_size", font_size)

        # 同步设置到磁盘
        self.settings.sync()

    def accept_settings(self):
        """接受并保存设置"""
        self.save_settings()
        self.accept()

    def apply_settings(self):
        """应用设置但不关闭对话框"""
        self.save_settings()

        # 通知父窗口应用设置
        if self.parent():
            if hasattr(self.parent(), 'apply_settings'):
                self.parent().apply_settings()

    def show_font_settings(self):
        """显示图表字体设置对话框"""
        try:
            from font_settings_dialog import FontSettingsDialog
            font_dialog = FontSettingsDialog(self)
            if font_dialog.exec_() == QDialog.Accepted:
                # 如果用户点击了确定，则应用字体设置
                if self.parent():
                    if hasattr(self.parent(), 'apply_font_settings'):
                        self.parent().apply_font_settings()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示字体设置对话框时出错: {str(e)}")
