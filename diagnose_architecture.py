#!/usr/bin/env python3
"""
程序架构诊断脚本

这个脚本用于诊断程序架构问题，检查依赖关系和模块完整性。
"""

import os
import sys
import importlib.util
from pathlib import Path

def check_file_exists(filename):
    """检查文件是否存在"""
    exists = os.path.exists(filename)
    status = "✅" if exists else "❌"
    print(f"{status} {filename}")
    return exists

def check_module_import(module_name, filename=None):
    """检查模块是否可以导入"""
    try:
        if filename and os.path.exists(filename):
            spec = importlib.util.spec_from_file_location(module_name, filename)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            print(f"✅ {module_name} (可导入)")
            return True
        else:
            __import__(module_name)
            print(f"✅ {module_name} (可导入)")
            return True
    except Exception as e:
        print(f"❌ {module_name} (导入失败: {e})")
        return False

def check_dependencies():
    """检查Python依赖"""
    print("\n🔍 检查Python依赖:")
    dependencies = [
        "PyQt5",
        "PyQt5.QtWidgets",
        "PyQt5.QtCore", 
        "PyQt5.QtGui",
        "pandas",
        "numpy",
        "matplotlib",
        "openpyxl"
    ]
    
    all_ok = True
    for dep in dependencies:
        if not check_module_import(dep):
            all_ok = False
    
    return all_ok

def check_core_files():
    """检查核心文件"""
    print("\n📁 检查核心文件:")
    core_files = [
        "main.py",
        "unified_launcher.py", 
        "version.py",
        "logger_utils.py",
        "config_manager.py",
        "component_base.py"
    ]
    
    all_ok = True
    for file in core_files:
        if not check_file_exists(file):
            all_ok = False
    
    return all_ok

def check_gui_modules():
    """检查GUI模块"""
    print("\n🖥️ 检查GUI模块:")
    gui_files = [
        ("gui.py", "性能监控数据分析器主界面"),
        ("raw_nmon_analyzer_gui.py", "Nmon文件分析器界面"),
        ("settings_dialog.py", "设置对话框"),
        ("font_settings_dialog.py", "字体设置对话框"),
        ("save_options_dialog.py", "保存选项对话框")
    ]
    
    all_ok = True
    for filename, description in gui_files:
        exists = check_file_exists(filename)
        if exists:
            print(f"   └─ {description}")
        else:
            all_ok = False
    
    return all_ok

def check_data_processing_modules():
    """检查数据处理模块"""
    print("\n📊 检查数据处理模块:")
    data_files = [
        ("excel_processor.py", "Excel文件处理器"),
        ("excel_generator.py", "Excel报告生成器"),
        ("nmon_parser.py", "Nmon文件解析器"),
        ("raw_nmon_analyzer.py", "原始Nmon文件分析器"),
        ("data_analyzer.py", "数据分析器"),
        ("batch_processor.py", "批处理器")
    ]
    
    all_ok = True
    for filename, description in data_files:
        exists = check_file_exists(filename)
        if exists:
            print(f"   └─ {description}")
        else:
            all_ok = False
    
    return all_ok

def check_visualization_modules():
    """检查可视化模块"""
    print("\n📈 检查可视化模块:")
    viz_files = [
        ("chart_generator.py", "图表生成器"),
        ("chart_optimizer.py", "图表优化器"),
        ("optimized_plotter.py", "优化绘图器")
    ]
    
    all_ok = True
    for filename, description in viz_files:
        exists = check_file_exists(filename)
        if exists:
            print(f"   └─ {description}")
        else:
            all_ok = False
    
    return all_ok

def check_startup_scripts():
    """检查启动脚本"""
    print("\n🚀 检查启动脚本:")
    startup_files = [
        ("run_performance_analyzer.py", "性能分析器启动脚本"),
        ("run_unified_nmon_analyzer.py", "Nmon分析器启动脚本"),
        ("run_raw_nmon_analyzer.py", "原始Nmon分析器启动脚本")
    ]
    
    all_ok = True
    for filename, description in startup_files:
        exists = check_file_exists(filename)
        if exists:
            print(f"   └─ {description}")
        else:
            all_ok = False
    
    return all_ok

def check_import_chains():
    """检查导入链"""
    print("\n🔗 检查关键模块导入:")
    
    # 检查主要模块的导入
    modules_to_check = [
        ("logger_utils", "logger_utils.py"),
        ("config_manager", "config_manager.py"),
        ("component_base", "component_base.py"),
        ("version", "version.py")
    ]
    
    all_ok = True
    for module_name, filename in modules_to_check:
        if not check_module_import(module_name, filename):
            all_ok = False
    
    return all_ok

def analyze_architecture():
    """分析程序架构"""
    print("🏗️ 程序架构分析报告")
    print("=" * 50)
    
    # 检查各个组件
    checks = [
        ("Python依赖", check_dependencies),
        ("核心文件", check_core_files),
        ("GUI模块", check_gui_modules),
        ("数据处理模块", check_data_processing_modules),
        ("可视化模块", check_visualization_modules),
        ("启动脚本", check_startup_scripts),
        ("模块导入", check_import_chains)
    ]
    
    results = {}
    for check_name, check_func in checks:
        results[check_name] = check_func()
    
    # 生成总结报告
    print("\n📋 总结报告:")
    print("=" * 50)
    
    all_passed = True
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {check_name}")
        if not result:
            all_passed = False
    
    print(f"\n🎯 整体状态: {'✅ 架构完整' if all_passed else '❌ 存在问题'}")
    
    # 提供修复建议
    if not all_passed:
        print("\n🔧 修复建议:")
        print("-" * 30)
        
        if not results["Python依赖"]:
            print("• 安装缺失的Python依赖包")
            print("  pip install PyQt5 pandas numpy matplotlib openpyxl")
        
        if not results["核心文件"]:
            print("• 检查核心文件是否完整，可能需要重新下载项目文件")
        
        if not results["GUI模块"]:
            print("• 检查GUI模块文件，确保界面组件完整")
        
        if not results["启动脚本"]:
            print("• 创建缺失的启动脚本文件")
        
        if not results["模块导入"]:
            print("• 检查模块语法错误，修复导入问题")
    
    return all_passed

def main():
    """主函数"""
    print("性能监控数据分析器 - 架构诊断工具")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python版本: {sys.version}")
    print()
    
    # 执行架构分析
    success = analyze_architecture()
    
    # 返回适当的退出代码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
