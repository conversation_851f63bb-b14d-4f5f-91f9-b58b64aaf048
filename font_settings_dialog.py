from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QComboBox, QPushButton, QGroupBox, QFormLayout,
                             QFrame, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QFontDatabase
import os
from ensure_fonts import (get_available_fonts, FONT_INFO, load_font_settings,
                         save_font_settings, download_font)
from logger_utils import log

class FontSettingsDialog(QDialog):
    """字体设置对话框，用于选择图表使用的字体"""

    def __init__(self, parent=None):
        """初始化字体设置对话框"""
        super().__init__(parent)

        self.setWindowTitle("图表字体设置")
        self.setMinimumWidth(600)  # 增加宽度
        self.setMinimumHeight(600)  # 增加高度
        self.resize(700, 650)  # 设置默认大小

        # 应用与主窗口相同的样式
        self.apply_main_window_style()

        # 加载当前字体设置
        self.chinese_font, self.english_font = load_font_settings()

        # 获取可用字体
        self.available_fonts = get_available_fonts()

        # 初始化UI
        self.init_ui()

        # 加载当前设置
        self.load_settings()

    def apply_main_window_style(self):
        """应用与主窗口相同的样式"""
        from PyQt5.QtCore import QSettings

        # 从设置中读取样式
        settings = QSettings("PerformanceAnalyzer", "Settings")
        style_name = settings.value("ui_style", "modern_blue", type=str)
        font_size = settings.value("font_size", 9, type=int)

        # 设置字体大小
        font = QFont()
        font.setPointSize(font_size)
        self.setFont(font)

        # 根据选择的样式设置样式表
        if style_name == "modern_blue":
            # 现代蓝模式
            self.setStyleSheet("""
                QDialog { background-color: #f0f5fa; color: #333333; }
                QLabel { color: #333333; }
                QGroupBox { border: 1px solid #c0d0e0; border-radius: 5px; margin-top: 10px; font-weight: bold; color: #333333; }
                QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px; }
                QComboBox { background-color: white; color: #333333; border: 1px solid #c0d0e0; padding: 5px; }
                QComboBox::drop-down { border: 0px; }
                QComboBox::down-arrow { image: url(down_arrow.png); width: 14px; height: 14px; }
                QComboBox QAbstractItemView { background-color: white; color: #333333; selection-background-color: #e0e9f0; }
                QTextEdit { background-color: white; color: #333333; }
            """)
        elif style_name == "light_classic":
            # 浅色经典模式
            self.setStyleSheet("""
                QGroupBox { border: 1px solid #c0c0c0; border-radius: 5px; margin-top: 10px; font-weight: bold; }
                QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px; }
                QComboBox { background-color: white; border: 1px solid #c0c0c0; padding: 5px; }
                QComboBox::drop-down { border: 0px; }
                QComboBox::down-arrow { image: url(down_arrow.png); width: 14px; height: 14px; }
                QComboBox QAbstractItemView { background-color: white; selection-background-color: #f0f0f0; }
            """)
        else:  # system_default
            # 系统默认模式
            self.setStyleSheet("")  # 清除样式表

    def init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout(self)

        # 标题和说明
        header_layout = QVBoxLayout()

        # 标题
        title_label = QLabel("图表字体设置")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #333;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)

        # 说明文本
        description = QLabel("选择图表中使用的中文字体。所有字体都已内嵌到程序中，无需依赖系统字体。")
        description.setStyleSheet("font-size: 14px; color: #333; margin-top: 5px; font-weight: normal;")
        description.setWordWrap(True)
        description.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(description)

        # 添加水平分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #cccccc;")
        header_layout.addWidget(separator)

        main_layout.addLayout(header_layout)

        # 中文字体设置
        chinese_group = QGroupBox("中文字体")
        chinese_layout = QVBoxLayout()

        # 中文字体选择
        chinese_form = QFormLayout()
        chinese_form.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)
        chinese_form.setFormAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        chinese_form.setVerticalSpacing(10)  # 增加垂直间距

        # 创建带样式的标签
        font_label = QLabel("选择中文字体:")
        font_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #333333;")

        self.chinese_combo = QComboBox()
        self.chinese_combo.setMinimumHeight(30)  # 增加高度使其更易点击
        self.populate_font_combo(self.chinese_combo, 'chinese')
        chinese_form.addRow(font_label, self.chinese_combo)
        chinese_layout.addLayout(chinese_form)

        # 中文字体预览
        preview_group = QGroupBox("字体预览")
        preview_layout = QVBoxLayout()

        # 创建更详细的预览文本
        preview_text = (
            "中文字体预览 - 字体演示\n"
            "你好，世界！\n"
            "这是一段用于测试中文字体显示效果的文本。\n"
            "数字: 0123456789  符号: !@#$%^&*()"
        )

        self.chinese_preview = QLabel(preview_text.replace("\\n", "\n"))
        self.chinese_preview.setStyleSheet(
            "font-size: 16px; "
            "padding: 15px; "
            "background-color: white; "
            "color: black; "
            "border: 1px solid #cccccc; "
            "border-radius: 5px; "
            "min-height: 120px;"
        )
        self.chinese_preview.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.chinese_preview.setWordWrap(True)
        preview_layout.addWidget(self.chinese_preview)

        # 添加字体信息标签
        self.chinese_font_info = QLabel("字体信息: 未选择")
        self.chinese_font_info.setStyleSheet(
            "font-size: 14px; "
            "color: #333333; "
            "padding: 5px; "
            "font-weight: bold;"
        )
        preview_layout.addWidget(self.chinese_font_info)

        preview_group.setLayout(preview_layout)
        chinese_layout.addWidget(preview_group)

        # 下载更多中文字体按钮
        download_chinese_button = QPushButton("下载更多中文字体")
        # 设置按钮样式
        download_chinese_button.setStyleSheet(
            "QPushButton { "
            "padding: 8px 15px; "
            "background-color: #FF9800; "
            "color: white; "
            "border: none; "
            "border-radius: 4px; "
            "font-size: 14px; "
            "font-weight: bold; "
            "margin-top: 10px; "
            "} "
            "QPushButton:hover { "
            "background-color: #FFA726; "
            "} "
            "QPushButton:pressed { "
            "background-color: #FB8C00; "
            "}"
        )
        download_chinese_button.setCursor(Qt.PointingHandCursor)  # 鼠标悬停时显示手型光标
        download_chinese_button.setIcon(self.style().standardIcon(self.style().SP_ArrowDown))
        download_chinese_button.clicked.connect(self.download_more_chinese_fonts)
        chinese_layout.addWidget(download_chinese_button)

        chinese_group.setLayout(chinese_layout)
        main_layout.addWidget(chinese_group)

        # 注意: 已移除英文字体设置部分

        # 添加水平分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #cccccc;")
        main_layout.addWidget(separator)

        # 说明区域
        info_box = QGroupBox("注意事项")
        info_layout = QVBoxLayout()

        info_label = QLabel("更改字体设置将影响所有新生成的图表。已生成的图表不会受影响。\n"
                           "所有字体都已内嵌到程序中，可以在不同系统上正常显示中文字符。")
        info_label.setStyleSheet("color: #333333; font-size: 14px; padding: 10px; background-color: #f5f5f5; border-radius: 5px;")
        info_label.setWordWrap(True)
        info_layout.addWidget(info_label)

        info_box.setLayout(info_layout)
        main_layout.addWidget(info_box)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 创建按钮并设置样式
        self.ok_button = QPushButton("确定")
        # 设置按钮样式
        self.ok_button.setStyleSheet(
            "QPushButton { "
            "padding: 8px 16px; "
            "background-color: #4CAF50; "
            "color: white; "
            "border: none; "
            "border-radius: 4px; "
            "font-size: 14px; "
            "min-width: 100px; "
            "} "
            "QPushButton:hover { "
            "background-color: #66BB6A; "
            "} "
            "QPushButton:pressed { "
            "background-color: #43A047; "
            "}"
        )
        self.ok_button.setCursor(Qt.PointingHandCursor)

        self.cancel_button = QPushButton("取消")
        # 设置按钮样式
        self.cancel_button.setStyleSheet(
            "QPushButton { "
            "padding: 8px 16px; "
            "background-color: #f0f0f0; "
            "color: #333; "
            "border: 1px solid #cccccc; "
            "border-radius: 4px; "
            "font-size: 14px; "
            "min-width: 100px; "
            "} "
            "QPushButton:hover { "
            "background-color: #e0e0e0; "
            "} "
            "QPushButton:pressed { "
            "background-color: #d0d0d0; "
            "}"
        )
        self.cancel_button.setCursor(Qt.PointingHandCursor)

        self.apply_button = QPushButton("应用")
        # 设置按钮样式
        self.apply_button.setStyleSheet(
            "QPushButton { "
            "padding: 8px 16px; "
            "background-color: #2196F3; "
            "color: white; "
            "border: none; "
            "border-radius: 4px; "
            "font-size: 14px; "
            "min-width: 100px; "
            "} "
            "QPushButton:hover { "
            "background-color: #42A5F5; "
            "} "
            "QPushButton:pressed { "
            "background-color: #1E88E5; "
            "}"
        )
        self.apply_button.setCursor(Qt.PointingHandCursor)

        # 连接信号
        self.ok_button.clicked.connect(self.accept_settings)
        self.cancel_button.clicked.connect(self.reject)
        self.apply_button.clicked.connect(self.apply_settings)

        # 添加到布局
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.apply_button)
        button_layout.addWidget(self.ok_button)

        main_layout.addLayout(button_layout)

        # 连接信号
        self.chinese_combo.currentIndexChanged.connect(self.update_chinese_preview)

    def populate_font_combo(self, combo, font_type):
        """填充字体下拉框"""
        combo.clear()

        # 添加可用字体
        for font_key, font_info in self.available_fonts.items():
            if font_info['type'] == font_type:
                combo.addItem(font_info['display_name'], font_key)

        # 添加未下载的字体
        for font_key, font_info in FONT_INFO.items():
            if font_info['type'] == font_type and font_key not in self.available_fonts:
                combo.addItem(f"{font_info['display_name']} (未下载)", font_key)

    def load_settings(self):
        """加载当前设置"""
        # 预先初始化预览区域，防止重叠
        self.init_preview_areas()

        # 设置中文字体
        self.set_combo_value(self.chinese_combo, self.chinese_font)

        # 更新预览
        self.update_chinese_preview()

    def init_preview_areas(self):
        """初始化预览区域，防止重叠"""
        # 初始化中文预览区域
        default_font = QFont()
        default_font.setPointSize(12)

        # 设置默认字体
        self.chinese_preview.setFont(default_font)

        # 设置默认信息
        self.chinese_font_info.setText("字体信息: 加载中...")

    def set_combo_value(self, combo, value):
        """设置下拉框的值"""
        index = combo.findData(value)
        if index >= 0:
            combo.setCurrentIndex(index)

    def update_chinese_preview(self):
        """更新中文字体预览"""
        font_key = self.chinese_combo.currentData()
        if font_key in self.available_fonts:
            font_info = self.available_fonts[font_key]
            font_path = font_info['path']

            # 更新字体信息标签
            style_text = "无衡线体" if font_info['style'] == 'sans-serif' else "衡线体"
            self.chinese_font_info.setText(f"字体信息: {font_info['display_name']} ({style_text})")

            # 设置预览字体
            font_id = QFontDatabase.addApplicationFont(font_path)
            if font_id >= 0:
                font_family = QFontDatabase.applicationFontFamilies(font_id)[0]
                font = QFont(font_family, 16)
                self.chinese_preview.setFont(font)
        else:
            self.chinese_font_info.setText("字体信息: 未选择或未下载")

    def download_more_chinese_fonts(self):
        """下载更多中文字体"""
        self.download_fonts('chinese')

    def download_fonts(self, font_type):
        """下载指定类型的字体"""
        # 获取未下载的字体
        undownloaded_fonts = []
        for font_key, font_info in FONT_INFO.items():
            if font_info['type'] == font_type and font_key not in self.available_fonts:
                undownloaded_fonts.append(font_key)

        if not undownloaded_fonts:
            QMessageBox.information(self, "提示", "所有中文字体已下载")
            return

        # 下载字体
        for font_key in undownloaded_fonts:
            download_font(font_key)

        # 刷新可用字体列表
        self.available_fonts = get_available_fonts()

        # 刷新下拉框
        self.populate_font_combo(self.chinese_combo, 'chinese')
        self.set_combo_value(self.chinese_combo, self.chinese_font)

    def get_selected_fonts(self):
        """获取选择的字体"""
        chinese_font = self.chinese_combo.currentData()

        # 检查是否需要下载字体
        if chinese_font not in self.available_fonts:
            if download_font(chinese_font):
                self.available_fonts = get_available_fonts()
            else:
                # 如果下载失败，使用默认字体
                from ensure_fonts import DEFAULT_FONTS
                chinese_font = DEFAULT_FONTS['chinese']
                QMessageBox.warning(self, "警告", "下载中文字体失败，将使用默认字体。")

        # 使用默认英文字体
        from ensure_fonts import DEFAULT_FONTS
        english_font = DEFAULT_FONTS['english']

        return chinese_font, english_font

    def save_settings(self):
        """保存设置"""
        chinese_font, english_font = self.get_selected_fonts()
        save_font_settings(chinese_font, english_font)

        # 更新当前设置
        self.chinese_font = chinese_font
        self.english_font = english_font

    def accept_settings(self):
        """接受并保存设置"""
        self.save_settings()
        self.accept()

    def apply_settings(self):
        """应用设置但不关闭对话框"""
        self.save_settings()

        # 通知父窗口应用设置
        if self.parent():
            if hasattr(self.parent(), 'apply_font_settings'):
                self.parent().apply_font_settings()
