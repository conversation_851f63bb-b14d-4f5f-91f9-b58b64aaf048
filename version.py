"""
Version information for the application.
"""

__version__ = "3.5.0"
__app_name__ = "性能监控数据分析器"
__release_date__ = "2025-05-15"
__author__ = "Performance Analyzer Team"

def get_version_info():
    """Return a formatted version string."""
    return f"{__app_name__} v{__version__}"

def get_about_text():
    """Return the about text for the application."""
    return f"""
{__app_name__} v{__version__}
发布日期: {__release_date__}

一个用于分析Excel文件中的CPU、内存和磁盘使用率数据的工具。
通过直观的图形界面，轻松处理和可视化性能监控数据。

V3.5版本特性：
- 优化的统一入口界面，提供更好的用户体验
- 改进的工具切换和管理功能
- 新增日志目录设置功能，可自定义日志保存位置
- 优化的日志系统，减少系统缓存使用
- 修复了原始Nmon文件分析器中的KeyError错误
- 改进的界面风格和主题设置
- 更好的错误处理和异常捕获
- 整合的帮助系统和文档

© 2025 {__author__}
"""
