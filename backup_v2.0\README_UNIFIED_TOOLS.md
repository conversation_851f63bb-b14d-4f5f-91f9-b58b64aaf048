# 性能监控工具集

这个项目集成了三个性能监控工具，提供了统一的启动界面和一致的用户体验。

## 包含的工具

1. **性能监控数据分析器** - 用于分析Excel文件中的CPU、内存和磁盘使用率数据
2. **原始Nmon文件分析工具** - 用于分析原始nmon文件并生成详细报告
3. **Nmon文件分析器** - 用于分析nmon文件并生成Excel报告

## 特点

- 统一的界面风格和用户体验
- 简单直观的工具选择界面
- 共享相同的主题和样式设置
- 灵活的工具调用方式

## 使用方法

### 启动统一工具集

```bash
python main.py
# 或
python unified_launcher.py
```

这将打开统一启动器界面，您可以从中选择要启动的工具。

### 直接启动各个工具

您也可以直接启动各个工具：

- 性能监控数据分析器: `python run_performance_analyzer.py`
- 原始Nmon文件分析工具: `python run_raw_nmon_analyzer.py`
- Nmon文件分析器: `python run_nmon_analyzer.py`

## 统一设置

所有工具共享相同的设置，包括：

### 界面风格

1. **现代蓝** - 默认风格，使用蓝色调和现代化的界面元素
2. **浅色经典** - 传统的浅色界面
3. **系统默认** - 使用操作系统的默认样式

### 日志级别

1. **调试 (DEBUG)** - 显示所有日志信息，包括详细的调试信息
2. **信息 (INFO)** - 显示一般信息、警告和错误（默认）
3. **警告 (WARNING)** - 只显示警告和错误
4. **错误 (ERROR)** - 只显示错误
5. **严重 (CRITICAL)** - 只显示严重错误

### 设置管理

您可以通过统一启动器的“设置”菜单访问设置对话框。修改设置后，所有工具将自动共享相同的设置，包括界面风格和日志级别。

即使您直接启动单个工具，也会使用统一的设置。

## 系统要求

- Python 3.6 或更高版本
- PyQt5 图形界面库
- pandas 和 numpy 用于数据处理
- matplotlib 用于图表生成
- openpyxl 用于 Excel 文件处理

## 开发说明

### 文件结构

- `main.py` - 主入口点，启动统一工具集
- `unified_launcher.py` - 统一启动器实现
- `run_performance_analyzer.py` - 性能监控数据分析器独立入口
- `run_raw_nmon_analyzer.py` - 原始Nmon文件分析工具入口
- `run_nmon_analyzer.py` - Nmon文件分析器入口
- `gui.py` - 性能监控数据分析器GUI
- `raw_nmon_analyzer_gui.py` - 原始Nmon文件分析工具GUI
- `nmon_analyzer_gui.py` - Nmon文件分析器GUI
- `settings_dialog.py` - 统一设置对话框

### 添加新工具

如果需要添加新的工具，请按照以下步骤操作：

1. 创建新工具的GUI和功能实现
2. 在`unified_launcher.py`中添加新工具的启动按钮和启动方法
3. 确保新工具使用相同的样式设置机制
4. 创建独立的启动脚本供直接访问

## 故障排除

如果遇到工具启动问题，请检查：

1. 确保所有依赖项已正确安装
2. 检查日志文件中的错误信息
3. 确保Python路径设置正确

## 贡献

欢迎提交问题报告和改进建议。
