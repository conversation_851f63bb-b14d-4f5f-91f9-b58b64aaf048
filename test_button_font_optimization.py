#!/usr/bin/env python3
"""
按钮字体优化测试脚本

专门测试按钮中文字体的显示效果和优化结果。
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QLabel, QGroupBox, 
                            QSlider, QSpinBox, QFormLayout, QComboBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class ButtonFontTestWindow(QMainWindow):
    """按钮字体测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("按钮中文字体优化测试")
        self.setMinimumSize(900, 700)
        
        # 初始化UI
        self.init_ui()
        
        # 应用样式
        self.apply_styles()
        
    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("按钮中文字体优化测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # 控制面板
        control_group = self.create_control_panel()
        main_layout.addWidget(control_group)
        
        # 按钮测试区域
        button_test_group = self.create_button_test_area()
        main_layout.addWidget(button_test_group)
        
        # 对比测试区域
        comparison_group = self.create_comparison_area()
        main_layout.addWidget(comparison_group)
        
    def create_control_panel(self):
        """创建控制面板"""
        group = QGroupBox("控制面板")
        layout = QFormLayout(group)
        
        # 主题选择
        self.theme_combo = QComboBox()
        try:
            from ui_style_manager import UIStyleManager
            style_manager = UIStyleManager()
            for theme_key, theme_info in style_manager.THEMES.items():
                self.theme_combo.addItem(theme_info['name'], theme_key)
        except ImportError:
            self.theme_combo.addItem("默认主题", "default")
        
        self.theme_combo.currentTextChanged.connect(self.change_theme)
        layout.addRow("主题:", self.theme_combo)
        
        # 字体大小调节
        self.font_size_slider = QSlider(Qt.Horizontal)
        self.font_size_slider.setRange(8, 20)
        self.font_size_slider.setValue(11)
        self.font_size_slider.valueChanged.connect(self.change_font_size)
        
        self.font_size_spinbox = QSpinBox()
        self.font_size_spinbox.setRange(8, 20)
        self.font_size_spinbox.setValue(11)
        self.font_size_spinbox.valueChanged.connect(self.font_size_slider.setValue)
        self.font_size_slider.valueChanged.connect(self.font_size_spinbox.setValue)
        
        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(self.font_size_slider)
        font_size_layout.addWidget(self.font_size_spinbox)
        layout.addRow("基础字体大小:", font_size_layout)
        
        return group
    
    def create_button_test_area(self):
        """创建按钮测试区域"""
        group = QGroupBox("按钮样式测试 - 中文字体优化")
        layout = QVBoxLayout(group)
        
        # 不同尺寸的按钮
        size_layout = QVBoxLayout()
        
        # 小按钮
        small_layout = QHBoxLayout()
        small_layout.addWidget(QLabel("小按钮:"))
        self.small_buttons = []
        for text in ["确定", "取消", "应用", "重置"]:
            btn = QPushButton(text)
            self.small_buttons.append(btn)
            small_layout.addWidget(btn)
        small_layout.addStretch()
        size_layout.addLayout(small_layout)
        
        # 普通按钮
        normal_layout = QHBoxLayout()
        normal_layout.addWidget(QLabel("普通按钮:"))
        self.normal_buttons = []
        for text in ["性能监控数据分析器", "Nmon文件分析器", "日志设置", "界面设置"]:
            btn = QPushButton(text)
            self.normal_buttons.append(btn)
            normal_layout.addWidget(btn)
        normal_layout.addStretch()
        size_layout.addLayout(normal_layout)
        
        # 大按钮
        large_layout = QHBoxLayout()
        large_layout.addWidget(QLabel("大按钮:"))
        self.large_buttons = []
        for text in ["开始分析", "保存结果", "导出报告", "生成图表"]:
            btn = QPushButton(text)
            self.large_buttons.append(btn)
            large_layout.addWidget(btn)
        large_layout.addStretch()
        size_layout.addLayout(large_layout)
        
        # 超大按钮
        xlarge_layout = QHBoxLayout()
        xlarge_layout.addWidget(QLabel("超大按钮:"))
        self.xlarge_buttons = []
        for text in ["启动性能监控", "批量处理文件"]:
            btn = QPushButton(text)
            self.xlarge_buttons.append(btn)
            xlarge_layout.addWidget(btn)
        xlarge_layout.addStretch()
        size_layout.addLayout(xlarge_layout)
        
        layout.addLayout(size_layout)
        
        # 不同类型的按钮
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("按钮类型:"))
        
        self.type_buttons = []
        button_configs = [
            ("主要操作", "primary"),
            ("次要操作", "secondary"), 
            ("强调操作", "accent"),
            ("成功操作", "success"),
            ("警告操作", "warning"),
            ("错误操作", "error"),
            ("信息提示", "info")
        ]
        
        for text, btn_type in button_configs:
            btn = QPushButton(text)
            btn.setProperty("button_type", btn_type)
            self.type_buttons.append(btn)
            type_layout.addWidget(btn)
        
        type_layout.addStretch()
        layout.addLayout(type_layout)
        
        return group
    
    def create_comparison_area(self):
        """创建对比测试区域"""
        group = QGroupBox("优化前后对比")
        layout = QVBoxLayout(group)
        
        # 优化前的按钮 (使用旧样式)
        old_layout = QHBoxLayout()
        old_layout.addWidget(QLabel("优化前:"))
        
        self.old_style_buttons = []
        for text in ["性能监控数据分析器", "Nmon文件分析器", "日志设置"]:
            btn = QPushButton(text)
            # 应用旧样式
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #2a82da;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: 500;
                    text-align: center;
                    min-height: 20px;
                }
                QPushButton:hover {
                    background-color: #3a92ea;
                }
            """)
            self.old_style_buttons.append(btn)
            old_layout.addWidget(btn)
        old_layout.addStretch()
        layout.addLayout(old_layout)
        
        # 优化后的按钮 (使用新样式)
        new_layout = QHBoxLayout()
        new_layout.addWidget(QLabel("优化后:"))
        
        self.new_style_buttons = []
        for text in ["性能监控数据分析器", "Nmon文件分析器", "日志设置"]:
            btn = QPushButton(text)
            self.new_style_buttons.append(btn)
            new_layout.addWidget(btn)
        new_layout.addStretch()
        layout.addLayout(new_layout)
        
        return group
    
    def apply_styles(self):
        """应用样式"""
        try:
            from ui_style_manager import get_style_manager, apply_widget_style
            
            # 应用窗口样式
            apply_widget_style(self, 'main_window')
            
            style_manager = get_style_manager()
            
            # 应用按钮样式
            for btn in self.small_buttons:
                btn.setStyleSheet(style_manager.get_button_style('primary', 'small'))
            
            for btn in self.normal_buttons:
                btn.setStyleSheet(style_manager.get_button_style('primary', 'normal'))
            
            for btn in self.large_buttons:
                btn.setStyleSheet(style_manager.get_button_style('primary', 'large'))
            
            for btn in self.xlarge_buttons:
                btn.setStyleSheet(style_manager.get_button_style('primary', 'xlarge'))
            
            # 应用不同类型的按钮样式
            for btn in self.type_buttons:
                btn_type = btn.property("button_type")
                btn.setStyleSheet(style_manager.get_button_style(btn_type, 'normal'))
            
            # 应用新样式到对比按钮
            for btn in self.new_style_buttons:
                btn.setStyleSheet(style_manager.get_button_style('primary', 'normal'))
            
            print("✅ 按钮样式应用成功")
            
        except Exception as e:
            print(f"❌ 按钮样式应用失败: {e}")
    
    def change_theme(self):
        """切换主题"""
        try:
            from ui_style_manager import get_style_manager
            
            theme_key = self.theme_combo.currentData()
            if theme_key and theme_key != "default":
                style_manager = get_style_manager()
                style_manager.set_theme(theme_key)
                
                # 重新应用样式
                self.apply_styles()
                
                theme_name = self.theme_combo.currentText()
                print(f"✅ 切换到主题: {theme_name}")
                
        except Exception as e:
            print(f"❌ 切换主题失败: {e}")
    
    def change_font_size(self, size):
        """改变字体大小"""
        try:
            from ui_style_manager import get_style_manager
            
            style_manager = get_style_manager()
            style_manager.set_font_size(size)
            
            # 重新应用全局样式
            style_manager.apply_global_style()
            
            # 重新应用按钮样式
            self.apply_styles()
            
            print(f"✅ 字体大小调整为: {size}")
            
        except Exception as e:
            print(f"❌ 字体大小调整失败: {e}")

def test_button_font_optimization():
    """测试按钮字体优化"""
    print("🔤 测试按钮字体优化")
    print("=" * 50)
    
    try:
        from ui_style_manager import UIStyleManager, get_style_manager
        
        # 测试样式管理器
        style_manager = UIStyleManager()
        print("✅ 样式管理器创建成功")
        
        # 测试字体大小设置
        original_size = style_manager.get_current_font_size()
        print(f"✅ 当前字体大小: {original_size}")
        
        # 测试按钮样式生成
        sizes = ['small', 'normal', 'large', 'xlarge']
        for size in sizes:
            style = style_manager.get_button_style('primary', size)
            if style and 'font-size' in style:
                print(f"✅ {size} 按钮样式生成成功")
            else:
                print(f"❌ {size} 按钮样式生成失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 按钮字体优化测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔤 按钮中文字体优化测试")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print()
    
    # 测试字体优化
    optimization_test = test_button_font_optimization()
    
    # 创建测试应用
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = ButtonFontTestWindow()
    window.show()
    
    print("\n💡 测试说明:")
    print("-" * 40)
    print("1. 观察不同尺寸按钮的中文字体显示效果")
    print("2. 对比优化前后的字体大小和清晰度")
    print("3. 测试不同主题下的字体显示")
    print("4. 调节字体大小观察实时效果")
    print("5. 检查中文字符的间距和对齐")
    
    print(f"\n📊 测试结果: {'✅ 通过' if optimization_test else '❌ 失败'}")
    print("\n🚀 启动测试窗口...")
    print("关闭窗口以结束测试")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
