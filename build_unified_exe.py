"""
构建统一性能监控工具可执行文件脚本

这个脚本用于将统一性能监控工具打包成可执行文件。
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime

try:
    import PyInstaller.__main__
except ImportError:
    print("错误: 未安装PyInstaller，请先安装: pip install pyinstaller")
    sys.exit(1)

# 版本号
VERSION = "3.5.0"

# 构建目录
BUILD_DIR = "build"
DIST_DIR = "dist"
OUTPUT_DIR = os.path.join(DIST_DIR, f"性能监控工具集_v{VERSION}")

# 清理旧的构建文件
def clean_build():
    """清理旧的构建文件"""
    print("清理旧的构建文件...")

    # 删除build目录
    if os.path.exists(BUILD_DIR):
        shutil.rmtree(BUILD_DIR)
        print(f"已删除 {BUILD_DIR} 目录")

    # 删除dist目录
    if os.path.exists(DIST_DIR):
        shutil.rmtree(DIST_DIR)
        print(f"已删除 {DIST_DIR} 目录")

    # 删除spec文件
    for file in os.listdir("."):
        if file.endswith(".spec"):
            os.remove(file)
            print(f"已删除 {file}")

# 创建图标目录
def create_icon_dir():
    """创建图标目录"""
    if not os.path.exists("icons"):
        os.makedirs("icons")
        print("已创建icons目录")

# 构建可执行文件
def build_exe():
    """构建可执行文件"""
    print(f"开始构建统一性能监控工具 v{VERSION}...")

    # 构建参数
    args = [
        "main.py",  # 主脚本
        "--name=性能监控工具集",  # 可执行文件名称
        "--onefile",  # 单文件模式
        "--windowed",  # 窗口模式，不显示控制台
        "--icon=icons/app_icon.ico",  # 图标文件
        "--version-file=file_version_info.txt",  # 版本信息文件
        "--clean",  # 清理临时文件
        "--noconfirm",  # 不确认覆盖
    ]

    # 添加隐藏导入
    hidden_imports = [
        "--hidden-import=pandas",
        "--hidden-import=numpy",
        "--hidden-import=openpyxl",
        "--hidden-import=matplotlib",
        "--hidden-import=PyQt5",
        "--hidden-import=logger_utils",
        "--hidden-import=version",
    ]
    args.extend(hidden_imports)

    # 添加数据文件
    datas = [
        "--add-data=icons;icons",
        "--add-data=version.py;.",
        "--add-data=logger_utils.py;.",
        "--add-data=settings_dialog.py;.",
        "--add-data=unified_launcher.py;.",
        "--add-data=run_performance_analyzer.py;.",
        "--add-data=run_unified_nmon_analyzer.py;.",
        "--add-data=file_version_info.txt;.",
    ]
    args.extend(datas)

    # 运行PyInstaller
    PyInstaller.__main__.run(args)

    print("构建完成!")

# 创建发布包
def create_release():
    """创建发布包"""
    print("创建发布包...")

    # 创建输出目录
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    # 复制可执行文件
    exe_file = os.path.join(DIST_DIR, "性能监控工具集.exe")
    if os.path.exists(exe_file):
        shutil.copy(exe_file, OUTPUT_DIR)
        print(f"已复制可执行文件到 {OUTPUT_DIR}")
    else:
        print(f"错误: 找不到可执行文件 {exe_file}")
        return False

    # 创建logs目录
    logs_dir = os.path.join(OUTPUT_DIR, "logs")
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)
        print(f"已创建logs目录: {logs_dir}")

    # 创建icons目录并复制图标
    icons_dir = os.path.join(OUTPUT_DIR, "icons")
    if not os.path.exists(icons_dir):
        os.makedirs(icons_dir)
        print(f"已创建icons目录: {icons_dir}")

    # 复制图标文件
    if os.path.exists("icons"):
        for icon_file in os.listdir("icons"):
            if icon_file.endswith((".ico", ".png")):
                shutil.copy(os.path.join("icons", icon_file), icons_dir)
                print(f"已复制图标文件: {icon_file}")

    # 创建README.txt
    readme_content = f"""性能监控工具集 v{VERSION}

发布日期: {datetime.now().strftime('%Y-%m-%d')}

使用说明:
1. 双击"性能监控工具集.exe"启动程序
2. 在主界面选择要使用的工具
3. 所有工具共享相同的界面风格和设置，提供一致的用户体验

包含的工具:
- 性能监控数据分析器 - 用于分析Excel文件中的CPU、内存和磁盘使用率数据
- Nmon文件分析器 - 用于分析nmon文件并生成图表和Excel报告

v3.5版本新特性:
- 优化的统一入口界面，提供更好的用户体验
- 改进的工具切换和管理功能
- 新增日志目录设置功能，可自定义日志保存位置
- 优化的日志系统，减少系统缓存使用
- 修复了原始Nmon文件分析器中的KeyError错误
- 改进的界面风格和主题设置

日志文件位于logs目录中。
"""

    readme_file = os.path.join(OUTPUT_DIR, "README.txt")
    with open(readme_file, "w", encoding="utf-8") as f:
        f.write(readme_content)
    print(f"已创建README文件: {readme_file}")

    # 创建压缩包
    zip_file = f"{OUTPUT_DIR}.zip"
    shutil.make_archive(OUTPUT_DIR, 'zip', DIST_DIR, os.path.basename(OUTPUT_DIR))
    print(f"已创建发布压缩包: {zip_file}")

    return True

def main():
    """主函数"""
    # 检查图标文件
    if not os.path.exists("icons/app_icon.ico"):
        print("警告: 找不到图标文件icons/app_icon.ico，将使用默认图标")
        create_icon_dir()

    # 清理旧的构建文件
    clean_build()

    # 构建可执行文件
    build_exe()

    # 创建发布包
    if create_release():
        print(f"统一性能监控工具 v{VERSION} 构建成功!")
        print(f"可执行文件位于: {OUTPUT_DIR}")
        print(f"压缩包位于: {OUTPUT_DIR}.zip")
    else:
        print("构建失败!")

if __name__ == "__main__":
    main()
