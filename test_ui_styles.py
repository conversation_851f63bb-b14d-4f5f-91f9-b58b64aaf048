#!/usr/bin/env python3
"""
UI样式系统测试脚本

测试新的统一样式管理系统的功能和效果。
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QLineEdit,
                            QComboBox, QGroupBox, QTabWidget, QTableWidget,
                            QProgressBar, QTextEdit, QFormLayout)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

def test_ui_style_manager():
    """测试UI样式管理器"""
    print("🎨 测试UI样式管理器")
    print("=" * 50)

    try:
        from ui_style_manager import UIStyleManager, get_style_manager, apply_global_style

        # 测试样式管理器创建
        style_manager = UIStyleManager()
        print("✅ 样式管理器创建成功")

        # 测试主题获取
        themes = style_manager.THEMES
        print(f"✅ 可用主题数量: {len(themes)}")
        for theme_key, theme_info in themes.items():
            print(f"   - {theme_key}: {theme_info['name']}")

        # 测试颜色获取
        colors = style_manager.get_theme_colors('modern_blue')
        print(f"✅ 现代蓝主题颜色数量: {len(colors)}")

        # 测试样式生成
        button_style = style_manager.get_button_style('primary', 'normal')
        print("✅ 按钮样式生成成功")

        input_style = style_manager.get_input_style()
        print("✅ 输入框样式生成成功")

        # 测试全局样式应用
        apply_global_style()
        print("✅ 全局样式应用成功")

        return True

    except Exception as e:
        print(f"❌ 样式管理器测试失败: {e}")
        return False

class StyleTestWindow(QMainWindow):
    """样式测试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("UI样式系统测试")
        self.setMinimumSize(800, 600)

        # 初始化UI
        self.init_ui()

        # 应用样式
        self.apply_styles()

        # 设置主题切换定时器
        self.theme_timer = QTimer()
        self.theme_timer.timeout.connect(self.cycle_themes)
        self.current_theme_index = 0

    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # 标题
        title_label = QLabel("UI样式系统测试")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 控制区域
        control_group = QGroupBox("控制面板")
        control_layout = QHBoxLayout(control_group)

        # 主题选择
        self.theme_combo = QComboBox()
        try:
            from ui_style_manager import UIStyleManager
            style_manager = UIStyleManager()
            for theme_key, theme_info in style_manager.THEMES.items():
                self.theme_combo.addItem(theme_info['name'], theme_key)
        except ImportError:
            self.theme_combo.addItem("默认主题", "default")

        self.theme_combo.currentTextChanged.connect(self.change_theme)
        control_layout.addWidget(QLabel("主题:"))
        control_layout.addWidget(self.theme_combo)

        # 自动切换按钮
        self.auto_switch_button = QPushButton("开始自动切换")
        self.auto_switch_button.clicked.connect(self.toggle_auto_switch)
        control_layout.addWidget(self.auto_switch_button)

        control_layout.addStretch()
        main_layout.addWidget(control_group)

        # 创建选项卡
        tab_widget = QTabWidget()

        # 按钮测试选项卡
        button_tab = self.create_button_test_tab()
        tab_widget.addTab(button_tab, "按钮测试")

        # 输入控件测试选项卡
        input_tab = self.create_input_test_tab()
        tab_widget.addTab(input_tab, "输入控件")

        # 表格测试选项卡
        table_tab = self.create_table_test_tab()
        tab_widget.addTab(table_tab, "表格显示")

        main_layout.addWidget(tab_widget)

        # 状态栏
        self.status_label = QLabel("就绪 - 选择主题或开始自动切换")
        main_layout.addWidget(self.status_label)

    def create_button_test_tab(self):
        """创建按钮测试选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 不同类型的按钮
        button_group = QGroupBox("按钮类型测试")
        button_layout = QVBoxLayout(button_group)

        # 主要按钮
        primary_layout = QHBoxLayout()
        primary_layout.addWidget(QLabel("主要按钮:"))

        self.primary_small = QPushButton("小按钮")
        self.primary_normal = QPushButton("普通按钮")
        self.primary_large = QPushButton("大按钮")
        self.primary_xlarge = QPushButton("超大按钮")

        primary_layout.addWidget(self.primary_small)
        primary_layout.addWidget(self.primary_normal)
        primary_layout.addWidget(self.primary_large)
        primary_layout.addWidget(self.primary_xlarge)
        primary_layout.addStretch()
        button_layout.addLayout(primary_layout)

        # 次要按钮
        secondary_layout = QHBoxLayout()
        secondary_layout.addWidget(QLabel("次要按钮:"))

        self.secondary_button = QPushButton("次要按钮")
        self.accent_button = QPushButton("强调按钮")
        self.success_button = QPushButton("成功按钮")
        self.warning_button = QPushButton("警告按钮")
        self.error_button = QPushButton("错误按钮")

        secondary_layout.addWidget(self.secondary_button)
        secondary_layout.addWidget(self.accent_button)
        secondary_layout.addWidget(self.success_button)
        secondary_layout.addWidget(self.warning_button)
        secondary_layout.addWidget(self.error_button)
        secondary_layout.addStretch()
        button_layout.addLayout(secondary_layout)

        layout.addWidget(button_group)
        layout.addStretch()

        return widget

    def create_input_test_tab(self):
        """创建输入控件测试选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 输入控件组
        input_group = QGroupBox("输入控件测试")
        input_layout = QFormLayout(input_group)

        # 文本输入
        self.line_edit = QLineEdit("示例文本")
        input_layout.addRow("单行输入:", self.line_edit)

        # 下拉框
        self.combo_box = QComboBox()
        self.combo_box.addItems(["选项1", "选项2", "选项3"])
        input_layout.addRow("下拉选择:", self.combo_box)

        # 多行文本
        self.text_edit = QTextEdit()
        self.text_edit.setPlainText("这是多行文本输入框\n可以输入多行内容\n测试样式效果")
        self.text_edit.setMaximumHeight(100)
        input_layout.addRow("多行输入:", self.text_edit)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(65)
        input_layout.addRow("进度条:", self.progress_bar)

        layout.addWidget(input_group)
        layout.addStretch()

        return widget

    def create_table_test_tab(self):
        """创建表格测试选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 表格
        table_group = QGroupBox("表格显示测试")
        table_layout = QVBoxLayout(table_group)

        self.table_widget = QTableWidget(5, 4)
        self.table_widget.setHorizontalHeaderLabels(["列1", "列2", "列3", "列4"])

        # 填充测试数据
        from PyQt5.QtWidgets import QTableWidgetItem
        for row in range(5):
            for col in range(4):
                item = QTableWidgetItem(f"数据{row+1}-{col+1}")
                self.table_widget.setItem(row, col, item)

        table_layout.addWidget(self.table_widget)
        layout.addWidget(table_group)

        return widget

    def apply_styles(self):
        """应用样式"""
        try:
            from ui_style_manager import get_style_manager, apply_widget_style

            # 应用窗口样式
            apply_widget_style(self, 'main_window')

            style_manager = get_style_manager()

            # 应用按钮样式
            self.primary_small.setStyleSheet(style_manager.get_button_style('primary', 'small'))
            self.primary_normal.setStyleSheet(style_manager.get_button_style('primary', 'normal'))
            self.primary_large.setStyleSheet(style_manager.get_button_style('primary', 'large'))
            self.primary_xlarge.setStyleSheet(style_manager.get_button_style('primary', 'xlarge'))

            self.secondary_button.setStyleSheet(style_manager.get_button_style('secondary', 'normal'))
            self.accent_button.setStyleSheet(style_manager.get_button_style('accent', 'normal'))
            self.success_button.setStyleSheet(style_manager.get_button_style('success', 'normal'))
            self.warning_button.setStyleSheet(style_manager.get_button_style('warning', 'normal'))
            self.error_button.setStyleSheet(style_manager.get_button_style('error', 'normal'))

            self.auto_switch_button.setStyleSheet(style_manager.get_button_style('info', 'normal'))

            print("✅ 样式应用成功")

        except Exception as e:
            print(f"❌ 样式应用失败: {e}")

    def change_theme(self):
        """切换主题"""
        try:
            from ui_style_manager import get_style_manager

            theme_key = self.theme_combo.currentData()
            if theme_key:
                style_manager = get_style_manager()
                style_manager.set_theme(theme_key)

                # 重新应用样式
                self.apply_styles()

                theme_name = self.theme_combo.currentText()
                self.status_label.setText(f"已切换到主题: {theme_name}")
                print(f"✅ 切换到主题: {theme_name}")

        except Exception as e:
            print(f"❌ 切换主题失败: {e}")

    def toggle_auto_switch(self):
        """切换自动切换模式"""
        if self.theme_timer.isActive():
            self.theme_timer.stop()
            self.auto_switch_button.setText("开始自动切换")
            self.status_label.setText("自动切换已停止")
        else:
            self.theme_timer.start(3000)  # 每3秒切换一次
            self.auto_switch_button.setText("停止自动切换")
            self.status_label.setText("自动切换已开始 (每3秒切换)")

    def cycle_themes(self):
        """循环切换主题"""
        if self.theme_combo.count() > 0:
            self.current_theme_index = (self.current_theme_index + 1) % self.theme_combo.count()
            self.theme_combo.setCurrentIndex(self.current_theme_index)

def test_style_application():
    """测试样式应用"""
    print("\n🖼️ 测试样式应用")
    print("=" * 50)

    try:
        app = QApplication(sys.argv)

        # 创建测试窗口
        window = StyleTestWindow()
        window.show()

        print("✅ 测试窗口创建成功")
        print("✅ 样式应用测试完成")
        print("\n💡 提示:")
        print("   - 可以在窗口中切换不同主题")
        print("   - 可以开启自动切换模式观察效果")
        print("   - 观察不同控件的样式变化")

        return app, window

    except Exception as e:
        print(f"❌ 样式应用测试失败: {e}")
        return None, None

def main():
    """主函数"""
    print("🎨 UI样式系统测试")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print()

    # 测试样式管理器
    manager_test = test_ui_style_manager()

    # 测试样式应用
    app, window = test_style_application()

    # 总结
    print("\n📋 测试总结")
    print("=" * 50)
    print(f"样式管理器测试: {'✅ 通过' if manager_test else '❌ 失败'}")
    print(f"样式应用测试: {'✅ 通过' if app and window else '❌ 失败'}")

    if app and window:
        print("\n🚀 启动测试窗口...")
        print("关闭窗口以结束测试")
        sys.exit(app.exec_())
    else:
        print("\n❌ 测试失败，无法启动测试窗口")
        sys.exit(1)

if __name__ == "__main__":
    main()
