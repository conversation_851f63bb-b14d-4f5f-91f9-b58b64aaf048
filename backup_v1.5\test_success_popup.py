import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import QSettings

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试成功弹窗")
        
        # 设置深色模式
        settings = QSettings("PerformanceAnalyzer", "Settings")
        settings.setValue("ui_style", "dark_mode")
        
        # 创建按钮
        self.button = QPushButton("显示成功弹窗")
        self.button.clicked.connect(self.show_success)
        
        # 创建布局
        layout = QVBoxLayout()
        layout.addWidget(self.button)
        
        # 创建中心部件
        central_widget = QWidget()
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)
    
    def show_success(self):
        QMessageBox.information(self, "成功", "结果已成功保存到 test.xlsx")

def main():
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
