"""
Version information for the application.
"""

__version__ = "1.5.1"
__app_name__ = "性能监控数据分析器"
__release_date__ = "2023-07-15"
__author__ = "Performance Analyzer Team"

def get_version_info():
    """Return a formatted version string."""
    return f"{__app_name__} v{__version__}"

def get_about_text():
    """Return the about text for the application."""
    return f"""
{__app_name__} v{__version__}
发布日期: {__release_date__}

一个用于分析Excel文件中的CPU、内存和磁盘使用率数据的工具。
通过直观的图形界面，轻松处理和可视化性能监控数据。

© 2025 {__author__}
"""
