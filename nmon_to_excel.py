import pandas as pd
from datetime import datetime, timedelta

def parse_nmon(file_path):
    data = {
        'CPU_cores': {},
        'CPU_ALL': {'headers': [], 'data': []},
        'MEM': {'headers': [], 'data': []},  # 新增MEM数据结构
        'timestamps': {},
        'start_time': None,
        'interval': 1
    }
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
                
            # 获取采样间隔
            if line.startswith('AAA,interval') and not data['interval']:
                parts = line.split(',')
                if len(parts) >= 3:
                    try:
                        data['interval'] = int(parts[2])
                    except:
                        pass
                    
            # 从ZZZZ行提取起始时间
            if line.startswith('ZZZZ') and not data['start_time']:
                parts = line.split(',')
                if len(parts) >= 4:
                    try:
                        date_part = parts[2]
                        time_part = parts[3]
                        data['start_time'] = datetime.strptime(f"{date_part} {time_part}", "%d-%b-%Y %H:%M:%S")
                    except:
                        data['start_time'] = datetime.now()
                    
            # 处理CPU数据
            if line.startswith('CPU'):
                parts = line.split(',')
                if parts[0] == 'CPU_ALL':
                    if not data['CPU_ALL']['headers']:
                        data['CPU_ALL']['headers'] = parts[:9]
                    else:
                        data['CPU_ALL']['data'].append(parts[:9])
                elif parts[0].startswith('CPU') and not parts[0] == 'CPU_ALL':
                    cpu_id = parts[0]
                    if cpu_id not in data['CPU_cores']:
                        data['CPU_cores'][cpu_id] = {'headers': parts[:7], 'data': []}
                    else:
                        # 处理T标签并关联时间戳
                        if parts[1].startswith('T'):
                            t_label = parts[1]
                            if t_label not in data['timestamps']:
                                # 计算相对时间
                                t_num = int(t_label[1:])
                                delta = timedelta(seconds=(t_num-1)*data['interval'])
                                data['timestamps'][t_label] = data['start_time'] + delta
                            # 替换T标签为实际时间戳
                            parts[1] = data['timestamps'][t_label].strftime("%Y-%m-%d %H:%M:%S")
                        data['CPU_cores'][cpu_id]['data'].append(parts[:7])
            
            # 处理CPU_ALL数据
            if line.startswith('CPU_ALL'):
                parts = line.split(',')
                if not data['CPU_ALL']['headers']:
                    data['CPU_ALL']['headers'] = ['Timestamp'] + parts[2:9]  # 确保列数一致
                else:
                    if parts[1].startswith('T'):
                        t_label = parts[1]
                        if t_label not in data['timestamps']:
                            t_num = int(t_label[1:])
                            delta = timedelta(seconds=(t_num-1)*data['interval'])
                            data['timestamps'][t_label] = data['start_time'] + delta
                        # 确保数据列数与标题列数匹配
                        data['CPU_ALL']['data'].append([data['timestamps'][t_label].strftime("%Y-%m-%d %H:%M:%S")] + parts[2:9])
            
            # 处理MEM数据
            if line.startswith('MEM'):
                parts = line.split(',')
                if not data['MEM']['headers']:
                    data['MEM']['headers'] = ['Timestamp'] + parts[1:]  # 保留所有MEM列
                else:
                    if parts[0].startswith('T'):
                        t_label = parts[0]
                        if t_label not in data['timestamps']:
                            t_num = int(t_label[1:])
                            delta = timedelta(seconds=(t_num-1)*data['interval'])
                            data['timestamps'][t_label] = data['start_time'] + delta
                        # 保留所有MEM数据列
                        data['MEM']['data'].append([data['timestamps'][t_label].strftime("%Y-%m-%d %H:%M:%S")] + parts[1:])

    return data

def create_excel_report(data, output_file):
    writer = pd.ExcelWriter(output_file, engine='xlsxwriter')
    
    # 修改这里：使用start_time而不是timestamp
    timestamp_str = data['start_time'].strftime("%Y-%m-%d %H:%M:%S") if data['start_time'] else "N/A"
    
    # 处理每个CPU核心数据
    for cpu_id, cpu_data in data['CPU_cores'].items():
        cpu_df = pd.DataFrame(cpu_data['data'], columns=cpu_data['headers'])
        cpu_df.to_excel(writer, sheet_name=cpu_id, index=False)
    
    # 处理CPU汇总数据
    cpu_all_df = pd.DataFrame(data['CPU_ALL']['data'], columns=data['CPU_ALL']['headers'])
    cpu_all_df.to_excel(writer, sheet_name='CPU_Summary', index=False)
    
    # 处理MEM数据
    # 处理MEM数据
    mem_df = pd.DataFrame(data['MEM']['data'], columns=data['MEM']['headers'])
    mem_df.to_excel(writer, sheet_name='Memory', index=False)
    
    # 添加时间戳工作表
    timestamp_df = pd.DataFrame({'Collection Time': [timestamp_str]})
    timestamp_df.to_excel(writer, sheet_name='Metadata', index=False)
    
    writer.close()

if __name__ == '__main__':
    nmon_file = 'DZLS_5vu_b-06-ATMV-db01_20250416170414.nmon'
    excel_file = 'cpu_performance_report.xlsx'
    
    parsed_data = parse_nmon(nmon_file)
    create_excel_report(parsed_data, excel_file)
    print(f"CPU性能报告已生成: {excel_file}")