=== 性能监控数据分析器日志 ===

2025-04-23 23:50:46 [INFO] 日志系统初始化成功，日志文件: H:\PyWorkspace\NewWork\logs\app_20250423.log
2025-04-23 23:50:46 [INFO] 守护线程启动
2025-04-23 23:50:46 [INFO] 守护线程已启动: WatchdogThread
2025-04-23 23:50:46 [INFO] 应用程序开始运行
2025-04-23 23:50:51 [DEBUG] 守护线程仍在运行
2025-04-23 23:50:57 [DEBUG] 守护线程仍在运行
2025-04-23 23:51:02 [DEBUG] 守护线程仍在运行
2025-04-23 23:51:03 [INFO] 已选择目录: H:/PyWorkspace/Work/Work
2025-04-23 23:51:05 [INFO] 正在处理目录: H:/PyWorkspace/Work/Work
2025-04-23 23:51:05 [INFO] 找到 3 个Excel文件，开始处理...
2025-04-23 23:51:05 [WARNING] 跳过文件: 1.xlsx - 不是有效的性能监控数据文件
2025-04-23 23:51:05 [INFO] 正在处理: *********92_b-06-ATMV-db01_20250416200406.nmon.xlsx
2025-04-23 23:51:06 [INFO] 磁盘繁忙度: 98.66% (磁盘: dm-4)
2025-04-23 23:51:06 [DEBUG] ZZZZ表格列名: ['T0001', datetime.time(20, 30, 7), datetime.datetime(2025, 4, 16, 0, 0), datetime.datetime(2025, 4, 16, 20, 30, 7)]
2025-04-23 23:51:06 [DEBUG] ZZZZ表格前3行:
   T0001  20:30:07 2025-04-16 00:00:00 2025-04-16 20:30:07
0  T0002  20:30:37          2025-04-16 2025-04-16 20:30:37
1  T0003  20:31:07          2025-04-16 2025-04-16 20:31:07
2  T0004  20:31:37          2025-04-16 2025-04-16 20:31:37
从第4列找到1448个有效时间戳
2025-04-23 23:51:06 [INFO] 使用ZZZZ表格中的时间戳，有效行数: 1448
2025-04-23 23:51:06 [INFO] 找到网络配置命令: ifconfig
2025-04-23 23:51:06 [INFO] 找到网络配置命令: ifconfig
2025-04-23 23:51:06 [INFO] 找到网络配置命令: ifconfig
2025-04-23 23:51:06 [INFO] 找到IP地址: *********92
2025-04-23 23:51:06 [INFO] IP地址: *********92
2025-04-23 23:51:06 [INFO] 成功处理: *********92_b-06-ATMV-db01_20250416200406.nmon.xlsx
2025-04-23 23:51:06 [INFO] 正在处理: *********_b-06-ATMV-AP01_20250416200452.nmon.xlsx
2025-04-23 23:51:07 [DEBUG] 守护线程仍在运行
2025-04-23 23:51:07 [INFO] 磁盘繁忙度: 0.86% (磁盘: vda)
2025-04-23 23:51:07 [DEBUG] ZZZZ表格列名: ['T0001', datetime.time(20, 29, 53), datetime.datetime(2025, 4, 16, 0, 0), datetime.datetime(2025, 4, 16, 20, 29, 53)]
2025-04-23 23:51:07 [DEBUG] ZZZZ表格前3行:
   T0001  20:29:53 2025-04-16 00:00:00 2025-04-16 20:29:53
0  T0002  20:30:23          2025-04-16 2025-04-16 20:30:23
1  T0003  20:30:53          2025-04-16 2025-04-16 20:30:53
2  T0004  20:31:23          2025-04-16 2025-04-16 20:31:23
从第4列找到1448个有效时间戳
2025-04-23 23:51:07 [INFO] 使用ZZZZ表格中的时间戳，有效行数: 1448
2025-04-23 23:51:07 [INFO] 找到网络配置命令: ifconfig
2025-04-23 23:51:07 [INFO] 找到网络配置命令: ifconfig
2025-04-23 23:51:07 [INFO] 找到网络配置命令: ifconfig
2025-04-23 23:51:07 [INFO] 找到IP地址: *********
2025-04-23 23:51:07 [INFO] IP地址: *********
2025-04-23 23:51:07 [INFO] 成功处理: *********_b-06-ATMV-AP01_20250416200452.nmon.xlsx
2025-04-23 23:51:07 [INFO] 处理完成: 共 3 个文件, 成功 2 个, 跳过 1 个, 错误 0 个
2025-04-23 23:51:07 [INFO] 成功处理了 2 个文件。
2025-04-23 23:51:12 [DEBUG] 守护线程仍在运行
2025-04-23 23:51:17 [DEBUG] 守护线程仍在运行
2025-04-23 23:51:22 [DEBUG] 守护线程仍在运行
2025-04-23 23:51:27 [DEBUG] 守护线程仍在运行
2025-04-23 23:51:32 [DEBUG] 守护线程仍在运行
2025-04-23 23:51:37 [DEBUG] 守护线程仍在运行
2025-04-23 23:51:42 [DEBUG] 守护线程仍在运行
2025-04-23 23:51:45 [INFO] 已选择输出文件: H:/PyWorkspace/Work/Work/4.xlsx
2025-04-23 23:51:46 [INFO] 开始保存结果操作
2025-04-23 23:51:46 [INFO] 显示保存选项对话框
2025-04-23 23:51:47 [DEBUG] 守护线程仍在运行
2025-04-23 23:51:49 [INFO] 选择的保存模式: full
2025-04-23 23:51:49 [INFO] 选择的保存模式: full
2025-04-23 23:51:49 [INFO] 禁用保存按钮
2025-04-23 23:51:49 [INFO] 创建进度对话框
2025-04-23 23:51:49 [INFO] 进度对话框已创建
2025-04-23 23:51:49 [INFO] 创建保存结果线程，模式: full, 输出路径: H:/PyWorkspace/Work/Work/4.xlsx
2025-04-23 23:51:49 [INFO] 连接线程信号
2025-04-23 23:51:49 [INFO] 启动保存线程
2025-04-23 23:51:49 [INFO] 保存线程已启动
2025-04-23 23:51:49 [INFO] 保存线程已启动 (ID: 14520, 模式: full)
2025-04-23 23:51:49 [INFO] 保存线程已启动 (ID: 14520, 模式: full)
2025-04-23 23:51:49 [INFO] 开始保存结果...
2025-04-23 23:51:49 [INFO] 保存摘要数据...
2025-04-23 23:51:49 [INFO] 打开Excel文件: H:/PyWorkspace/Work/Work/4.xlsx
2025-04-23 23:51:49 [INFO] 摘要数据保存成功
2025-04-23 23:51:49 [INFO] 生成并添加所有图表...
2025-04-23 23:51:49 [INFO] 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpaxd70ox1
2025-04-23 23:51:49 [INFO] 正在生成图表...
2025-04-23 23:51:49 [INFO] 正在生成图表...
2025-04-23 23:51:49 [INFO] 文件数量较少 (2), 使用串行处理
2025-04-23 23:51:49 [INFO] 正在生成图表...
2025-04-23 23:51:49 [INFO] 正在生成图表...
2025-04-23 23:51:49 [INFO] 正在生成图表...
2025-04-23 23:51:49 [INFO] 正在生成图表...
2025-04-23 23:51:49 [INFO] 开始为文件 *********92_b-06-ATMV-db01_20250416200406.nmon 生成图表...
2025-04-23 23:51:49 [INFO] CPU图表: 处理文件 *********92_b-06-ATMV-db01_20250416200406.nmon, 数据点数: 1451, 时间戳数: 1448
2025-04-23 23:51:49 [INFO] 警告: 时间戳数量(1448)小于CPU数据点数(1451)，截断CPU数据
2025-04-23 23:51:49 [INFO] 开始格式化时间轴...
2025-04-23 23:51:49 [INFO] 时间戳类型: pandas Series datetime64
2025-04-23 23:51:49 [INFO] 使用时:分:秒格式
2025-04-23 23:51:49 [INFO] 使用有效时间戳行数 1448 进行刻度计算
2025-04-23 23:51:49 [INFO] 设置最大刻度数量为 10
2025-04-23 23:51:49 [INFO] CPU图表: 设置时间轴范围从 2025-04-16 20:30:37 到 2025-04-17 08:34:08
2025-04-23 23:51:49 [INFO] CPU图表: 已保存到 C:\Users\<USER>\AppData\Local\Temp\tmpaxd70ox1\cpu_0.png
2025-04-23 23:51:49 [INFO] 警告: 时间戳数量(1448)小于内存数据点数(1449)，截断内存数据
2025-04-23 23:51:49 [INFO] 开始格式化时间轴...
2025-04-23 23:51:49 [INFO] 时间戳类型: pandas Series datetime64
2025-04-23 23:51:49 [INFO] 使用时:分:秒格式
2025-04-23 23:51:49 [INFO] 使用有效时间戳行数 1448 进行刻度计算
2025-04-23 23:51:49 [INFO] 设置最大刻度数量为 10
2025-04-23 23:51:49 [INFO] 设置内存图表时间轴范围从 2025-04-16 20:30:37 到 2025-04-17 08:34:08
2025-04-23 23:51:49 [INFO] 原始数据点数: 1449
2025-04-23 23:51:49 [INFO] 采样间隔: 2
2025-04-23 23:51:49 [INFO] 采样后的数据点数: 725
2025-04-23 23:51:49 [INFO] 使用时间戳作为 *********92 - *********92_b-06-ATMV-db01_20250416200406.nmon (dm-4) 的x轴
2025-04-23 23:51:49 [INFO] 设置时间轴起点为 2025-04-16 20:30:37
2025-04-23 23:51:49 [INFO] 开始格式化时间轴...
2025-04-23 23:51:49 [INFO] 时间戳类型: pandas Series datetime64
2025-04-23 23:51:49 [INFO] 使用时:分:秒格式
2025-04-23 23:51:49 [INFO] 使用有效时间戳行数 1448 进行刻度计算
2025-04-23 23:51:49 [INFO] 设置最大刻度数量为 10
设置磁盘图表时间轴范围从 2025-04-16 20:30:37 到 2025-04-17 08:34:08
2025-04-23 23:51:49 [INFO] 图表生成进度: 1/2
2025-04-23 23:51:49 [INFO] None
2025-04-23 23:51:49 [INFO] 开始为文件 *********_b-06-ATMV-AP01_20250416200452.nmon 生成图表...
2025-04-23 23:51:49 [INFO] CPU图表: 处理文件 *********_b-06-ATMV-AP01_20250416200452.nmon, 数据点数: 1451, 时间戳数: 1448
2025-04-23 23:51:49 [INFO] 警告: 时间戳数量(1448)小于CPU数据点数(1451)，截断CPU数据
2025-04-23 23:51:49 [INFO] None
2025-04-23 23:51:49 [INFO] 开始格式化时间轴...
2025-04-23 23:51:49 [INFO] 时间戳类型: pandas Series datetime64
2025-04-23 23:51:49 [INFO] 使用时:分:秒格式
2025-04-23 23:51:49 [INFO] 使用有效时间戳行数 1448 进行刻度计算
2025-04-23 23:51:49 [INFO] 设置最大刻度数量为 10
2025-04-23 23:51:49 [INFO] CPU图表: 设置时间轴范围从 2025-04-16 20:30:23 到 2025-04-17 08:33:54
2025-04-23 23:51:49 [INFO] CPU图表: 已保存到 C:\Users\<USER>\AppData\Local\Temp\tmpaxd70ox1\cpu_1.png
2025-04-23 23:51:49 [INFO] 警告: 时间戳数量(1448)小于内存数据点数(1449)，截断内存数据
2025-04-23 23:51:49 [INFO] 开始格式化时间轴...
2025-04-23 23:51:49 [INFO] 时间戳类型: pandas Series datetime64
2025-04-23 23:51:49 [INFO] 使用时:分:秒格式
2025-04-23 23:51:49 [INFO] 使用有效时间戳行数 1448 进行刻度计算
2025-04-23 23:51:49 [INFO] 设置最大刻度数量为 10
2025-04-23 23:51:49 [INFO] 设置内存图表时间轴范围从 2025-04-16 20:30:23 到 2025-04-17 08:33:54
2025-04-23 23:51:50 [INFO] 原始数据点数: 1449
2025-04-23 23:51:50 [INFO] 采样间隔: 2
2025-04-23 23:51:50 [INFO] 采样后的数据点数: 725
2025-04-23 23:51:50 [INFO] 使用时间戳作为 ********* - *********_b-06-ATMV-AP01_20250416200452.nmon (vda) 的x轴
2025-04-23 23:51:50 [INFO] 设置时间轴起点为 2025-04-16 20:30:23
2025-04-23 23:51:50 [INFO] 开始格式化时间轴...
2025-04-23 23:51:50 [INFO] 时间戳类型: pandas Series datetime64
2025-04-23 23:51:50 [INFO] 使用时:分:秒格式
2025-04-23 23:51:50 [INFO] 使用有效时间戳行数 1448 进行刻度计算
2025-04-23 23:51:50 [INFO] 设置最大刻度数量为 10
设置磁盘图表时间轴范围从 2025-04-16 20:30:23 到 2025-04-17 08:33:54
2025-04-23 23:51:50 [INFO] 图表生成进度: 2/2
2025-04-23 23:51:50 [INFO] None
2025-04-23 23:51:50 [INFO] None
2025-04-23 23:51:50 [INFO] 正在将图表添加到Excel...
2025-04-23 23:51:50 [INFO] 正在将图表添加到Excel...
2025-04-23 23:51:50 [INFO] 添加图表进度: 1/2
2025-04-23 23:51:50 [INFO] 正在将图表添加到Excel...
2025-04-23 23:51:50 [INFO] None
2025-04-23 23:51:50 [INFO] None
2025-04-23 23:51:50 [INFO] 添加图表进度: 2/2
2025-04-23 23:51:50 [INFO] None
2025-04-23 23:51:50 [INFO] None
2025-04-23 23:51:50 [INFO] 保存Excel文件: H:/PyWorkspace/Work/Work/4.xlsx
2025-04-23 23:51:50 [INFO] 图表添加完成
2025-04-23 23:51:50 [INFO] 图表添加完成
2025-04-23 23:51:50 [INFO] 图表添加完成
2025-04-23 23:51:50 [INFO] 清理临时文件完成
2025-04-23 23:51:50 [INFO] 结果已成功保存到 H:/PyWorkspace/Work/Work/4.xlsx
2025-04-23 23:51:50 [INFO] 图表生成完成，共处理 10 条日志信息
2025-04-23 23:51:50 [INFO] 收到保存成功信号
2025-04-23 23:51:50 [INFO] 结果已成功保存到 H:/PyWorkspace/Work/Work/4.xlsx
2025-04-23 23:51:50 [INFO] 在显示成功对话框前更新进度对话框
2025-04-23 23:51:50 [INFO] 资源清理完成
2025-04-23 23:51:50 [INFO] 保存线程已结束 (ID: 14520)
2025-04-23 23:51:50 [INFO] 准备发送完成信号
2025-04-23 23:51:50 [INFO] 完成信号已发送
2025-04-23 23:51:50 [INFO] 保存线程完成信号已接收
2025-04-23 23:51:50 [INFO] 保存完成方法已执行完毕
2025-04-23 23:51:50 [INFO] 显示成功对话框
2025-04-23 23:51:52 [DEBUG] 守护线程仍在运行
2025-04-23 23:51:52 [INFO] 成功对话框已显示
2025-04-23 23:51:53 [INFO] 延时清理：进度对话框已隐藏
2025-04-23 23:51:57 [DEBUG] 守护线程仍在运行
2025-04-23 23:52:02 [DEBUG] 守护线程仍在运行
2025-04-23 23:52:07 [DEBUG] 守护线程仍在运行
2025-04-23 23:52:12 [DEBUG] 守护线程仍在运行
2025-04-23 23:52:17 [DEBUG] 守护线程仍在运行
2025-04-23 23:52:22 [DEBUG] 守护线程仍在运行
2025-04-23 23:52:27 [DEBUG] 守护线程仍在运行
2025-04-23 23:52:32 [DEBUG] 守护线程仍在运行
2025-04-23 23:52:37 [DEBUG] 守护线程仍在运行
2025-04-23 23:52:42 [DEBUG] 守护线程仍在运行
2025-04-23 23:52:47 [DEBUG] 守护线程仍在运行
2025-04-23 23:52:52 [DEBUG] 守护线程仍在运行
2025-04-23 23:52:57 [DEBUG] 守护线程仍在运行
2025-04-23 23:53:02 [DEBUG] 守护线程仍在运行
2025-04-23 23:53:07 [DEBUG] 守护线程仍在运行
2025-04-23 23:53:12 [DEBUG] 守护线程仍在运行
2025-04-23 23:53:17 [DEBUG] 守护线程仍在运行
2025-04-23 23:53:22 [DEBUG] 守护线程仍在运行
2025-04-23 23:53:27 [DEBUG] 守护线程仍在运行
2025-04-23 23:53:32 [DEBUG] 守护线程仍在运行
2025-04-23 23:53:37 [DEBUG] 守护线程仍在运行
2025-04-23 23:53:42 [DEBUG] 守护线程仍在运行
2025-04-23 23:53:47 [DEBUG] 守护线程仍在运行
2025-04-23 23:53:52 [DEBUG] 守护线程仍在运行
2025-04-23 23:53:57 [DEBUG] 守护线程仍在运行
2025-04-23 23:54:02 [DEBUG] 守护线程仍在运行
2025-04-23 23:54:07 [DEBUG] 守护线程仍在运行
2025-04-23 23:54:12 [DEBUG] 守护线程仍在运行
2025-04-23 23:54:17 [DEBUG] 守护线程仍在运行
2025-04-23 23:54:22 [DEBUG] 守护线程仍在运行
2025-04-23 23:54:27 [DEBUG] 守护线程仍在运行
2025-04-23 23:54:32 [DEBUG] 守护线程仍在运行
2025-04-23 23:54:37 [DEBUG] 守护线程仍在运行
2025-04-23 23:54:42 [DEBUG] 守护线程仍在运行
2025-04-23 23:54:47 [DEBUG] 守护线程仍在运行
2025-04-23 23:54:52 [DEBUG] 守护线程仍在运行
2025-04-23 23:54:57 [DEBUG] 守护线程仍在运行
2025-04-23 23:55:02 [DEBUG] 守护线程仍在运行
2025-04-23 23:55:07 [DEBUG] 守护线程仍在运行
2025-04-23 23:55:12 [DEBUG] 守护线程仍在运行
2025-04-23 23:55:17 [DEBUG] 守护线程仍在运行
2025-04-23 23:55:22 [DEBUG] 守护线程仍在运行
2025-04-23 23:55:26 [INFO] 应用程序退出，退出代码: 0
2025-04-23 23:55:26 [CRITICAL] 系统退出函数被调用
2025-04-23 23:55:26 [CRITICAL] 退出时的调用堆栈:   File "H:\PyWorkspace\NewWork\main.py", line 226, in handle_exit
    stack = traceback.format_stack()

2025-04-23 23:55:26 [CRITICAL] 退出时的活动线程数: 4
2025-04-23 23:55:26 [CRITICAL] 线程: MainThread, 活动: False, 守护: False
2025-04-23 23:55:26 [CRITICAL] 线程: WatchdogThread, 活动: True, 守护: True
2025-04-23 23:55:26 [CRITICAL] 线程: Dummy-1, 活动: True, 守护: True
2025-04-23 23:55:26 [CRITICAL] 线程: Dummy-2, 活动: True, 守护: True
