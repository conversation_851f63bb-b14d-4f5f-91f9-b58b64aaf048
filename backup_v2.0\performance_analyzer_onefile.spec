# -*- mode: python ; coding: utf-8 -*-
import os

block_cipher = None

# 定义要排除的模块
excludes = [
    'tkinter', 'scipy', 'PyQt5.QtWebEngineWidgets', 'PyQt5.QtWebEngine',
    'PyQt5.QtWebEngineCore', 'PyQt5.QtPositioning', 'PyQt5.QtLocation',
    'PyQt5.QtBluetooth', 'PyQt5.QtNfc', 'IPython', 'Cython', 'pytest',
    'sphinx', 'notebook', 'jupyter', 'nose', 'numba', 'numexpr',
    'doctest', 'pdb', 'pywin.debugger', 'pywin.debugger.dbgcon',
    'pywin.dialogs', 'tcl', 'tk', 'wx', 'PyQt4', 'PySide', 'PySide2',
    'matplotlib.backends._backend_tk', 'matplotlib.backends.backend_tkagg',
    'matplotlib.backends.backend_qt4agg', 'matplotlib.backends.backend_qt5agg',
    'matplotlib.backends.backend_wxagg', 'matplotlib.backends.backend_gtk3agg',
    'matplotlib.backends.backend_gtk3cairo', 'matplotlib.backends.backend_gtk',
    'matplotlib.backends.backend_gtkagg', 'matplotlib.backends.backend_tkcairo',
    'matplotlib.backends.backend_qt4cairo', 'matplotlib.backends.backend_qt5cairo',
    'matplotlib.backends.backend_wxcairo', 'matplotlib.backends.backend_ps',
    'matplotlib.backends.backend_cairo', 'matplotlib.backends.backend_pgf',
    'matplotlib.backends.backend_svg', 'matplotlib.backends.backend_pdf',
    'matplotlib.backends.backend_template', 'matplotlib.backends.backend_webagg',
    'matplotlib.backends.backend_webagg_core', 'matplotlib.backends.backend_nbagg',
    'matplotlib.backends.backend_mixed', 'matplotlib.backends.backend_macosx',
    'matplotlib.backends.backend_gtk3', 'matplotlib.backends.backend_agg',
    'matplotlib.backends.backend_svg', 'matplotlib.backends.backend_pdf',
    'matplotlib.backends.backend_ps', 'matplotlib.backends.backend_pgf',
    'matplotlib.backends.backend_cairo', 'matplotlib.backends.backend_mixed',
    'matplotlib.backends.backend_template', 'matplotlib.backends.backend_webagg',
    'matplotlib.backends.backend_webagg_core', 'matplotlib.backends.backend_nbagg',
    'matplotlib.backends.backend_macosx', 'matplotlib.backends.backend_gtk3',
    'matplotlib.backends.backend_agg', 'matplotlib.backends.backend_svg',
    'matplotlib.backends.backend_pdf', 'matplotlib.backends.backend_ps',
    'matplotlib.backends.backend_pgf', 'matplotlib.backends.backend_cairo',
    'matplotlib.backends.backend_mixed', 'matplotlib.backends.backend_template',
    'matplotlib.backends.backend_webagg', 'matplotlib.backends.backend_webagg_core',
    'matplotlib.backends.backend_nbagg', 'matplotlib.backends.backend_macosx',
    'matplotlib.backends.backend_gtk3', 'matplotlib.backends.backend_agg',
]

# 定义必要的隐藏导入
hidden_imports = [
    'pandas',
    'numpy',
    'matplotlib',
    'openpyxl',
    'PyQt5',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('fonts', 'fonts'),  # 包含字体目录
        ('icons', 'icons'),  # 包含图标目录（如果存在）
        ('README.md', '.'),  # 包含readme文件
    ],
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={
        'matplotlib': {
            'backends': ['Agg'],  # 只包含Agg后端，这是最小的后端
        },
    },
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 我们不进行额外的二进制文件过滤，因为我们已经在excludes中排除了大部分不需要的模块

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='性能监控数据分析器_V1.5.1',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=['vcruntime140.dll', 'python311.dll', 'VCRUNTIME140.dll', 'VCRUNTIME140_1.dll',
                'Qt5Core.dll', 'Qt5Gui.dll', 'Qt5Widgets.dll'],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icons/app_icon.ico' if os.path.exists('icons/app_icon.ico') else None,
    version='version_info.txt',
)
