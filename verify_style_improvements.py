#!/usr/bin/env python3
"""
界面样式改进验证脚本

验证所有界面组件的样式统一性和改进效果。
"""

import sys
import os
from pathlib import Path

def check_style_manager():
    """检查样式管理器"""
    print("🎨 检查样式管理器")
    print("-" * 40)
    
    try:
        from ui_style_manager import UIStyleManager, get_style_manager
        
        # 检查样式管理器创建
        style_manager = UIStyleManager()
        print("✅ 样式管理器创建成功")
        
        # 检查主题数量
        themes = style_manager.THEMES
        print(f"✅ 支持主题数量: {len(themes)}")
        for theme_key, theme_info in themes.items():
            colors = theme_info['colors']
            print(f"   - {theme_key}: {theme_info['name']} ({len(colors)}种颜色)")
        
        # 检查字体大小定义
        font_sizes = style_manager.FONT_SIZES
        print(f"✅ 字体大小级别: {len(font_sizes)}")
        
        # 检查样式生成功能
        button_types = ['primary', 'secondary', 'accent', 'success', 'warning', 'error', 'info']
        button_sizes = ['small', 'normal', 'large', 'xlarge']
        
        print(f"✅ 按钮样式组合: {len(button_types)} × {len(button_sizes)} = {len(button_types) * len(button_sizes)}种")
        
        # 测试样式生成
        test_style = style_manager.get_button_style('primary', 'normal')
        print("✅ 样式生成功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 样式管理器检查失败: {e}")
        return False

def check_file_updates():
    """检查文件更新情况"""
    print("\n📁 检查文件更新情况")
    print("-" * 40)
    
    files_to_check = [
        ("ui_style_manager.py", "样式管理器"),
        ("unified_launcher.py", "统一启动器"),
        ("settings_dialog.py", "设置对话框"),
        ("gui.py", "主界面"),
        ("test_ui_styles.py", "样式测试工具"),
        ("界面样式改进报告.md", "改进报告")
    ]
    
    all_updated = True
    
    for filename, description in files_to_check:
        if os.path.exists(filename):
            # 检查文件是否包含样式管理器相关代码
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if filename.endswith('.py'):
                if 'ui_style_manager' in content or 'UIStyleManager' in content:
                    print(f"✅ {filename} - {description} (已集成样式管理器)")
                elif filename == 'ui_style_manager.py':
                    print(f"✅ {filename} - {description} (样式管理器核心文件)")
                else:
                    print(f"⚠️ {filename} - {description} (未集成样式管理器)")
            else:
                print(f"✅ {filename} - {description}")
        else:
            print(f"❌ {filename} - {description} (文件不存在)")
            all_updated = False
    
    return all_updated

def check_style_consistency():
    """检查样式一致性"""
    print("\n🎯 检查样式一致性")
    print("-" * 40)
    
    try:
        from ui_style_manager import get_style_manager
        
        style_manager = get_style_manager()
        
        # 检查不同主题的样式一致性
        themes = list(style_manager.THEMES.keys())
        print(f"✅ 检查 {len(themes)} 个主题的样式一致性")
        
        for theme in themes:
            colors = style_manager.get_theme_colors(theme)
            required_colors = [
                'primary', 'secondary', 'accent', 'background', 'surface',
                'text_primary', 'text_secondary', 'border', 'success', 'warning', 'error'
            ]
            
            missing_colors = [color for color in required_colors if color not in colors]
            if missing_colors:
                print(f"⚠️ 主题 {theme} 缺少颜色: {missing_colors}")
            else:
                print(f"✅ 主题 {theme} 颜色定义完整")
        
        # 检查按钮样式生成
        button_types = ['primary', 'secondary', 'accent', 'success', 'warning', 'error', 'info']
        button_sizes = ['small', 'normal', 'large', 'xlarge']
        
        style_count = 0
        for btn_type in button_types:
            for btn_size in button_sizes:
                try:
                    style = style_manager.get_button_style(btn_type, btn_size)
                    if style and 'QPushButton' in style:
                        style_count += 1
                except Exception:
                    pass
        
        total_combinations = len(button_types) * len(button_sizes)
        print(f"✅ 按钮样式生成: {style_count}/{total_combinations} 组合可用")
        
        # 检查其他组件样式
        other_styles = [
            ('输入框样式', style_manager.get_input_style),
            ('下拉框样式', style_manager.get_combobox_style),
            ('分组框样式', style_manager.get_groupbox_style),
            ('表格样式', style_manager.get_table_style),
            ('选项卡样式', style_manager.get_tab_style),
            ('进度条样式', style_manager.get_progressbar_style),
            ('滚动条样式', style_manager.get_scrollbar_style)
        ]
        
        for style_name, style_func in other_styles:
            try:
                style = style_func()
                if style:
                    print(f"✅ {style_name}生成正常")
                else:
                    print(f"⚠️ {style_name}生成为空")
            except Exception as e:
                print(f"❌ {style_name}生成失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 样式一致性检查失败: {e}")
        return False

def check_integration_status():
    """检查集成状态"""
    print("\n🔗 检查集成状态")
    print("-" * 40)
    
    integration_points = [
        ("unified_launcher.py", "统一启动器", ["set_style", "ui_style_manager"]),
        ("settings_dialog.py", "设置对话框", ["apply_dialog_style", "ui_style_manager"]),
        ("gui.py", "主界面", ["set_style", "_update_button_styles"]),
    ]
    
    all_integrated = True
    
    for filename, description, required_elements in integration_points:
        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            found_elements = []
            missing_elements = []
            
            for element in required_elements:
                if element in content:
                    found_elements.append(element)
                else:
                    missing_elements.append(element)
            
            if missing_elements:
                print(f"⚠️ {filename} - {description}")
                print(f"   已集成: {found_elements}")
                print(f"   缺失: {missing_elements}")
                all_integrated = False
            else:
                print(f"✅ {filename} - {description} (完全集成)")
        else:
            print(f"❌ {filename} - {description} (文件不存在)")
            all_integrated = False
    
    return all_integrated

def test_style_application():
    """测试样式应用"""
    print("\n🧪 测试样式应用")
    print("-" * 40)
    
    try:
        from PyQt5.QtWidgets import QApplication, QPushButton, QLabel, QLineEdit
        from ui_style_manager import get_style_manager, apply_global_style
        
        # 创建测试应用
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 应用全局样式
        apply_global_style()
        print("✅ 全局样式应用成功")
        
        # 获取样式管理器
        style_manager = get_style_manager()
        
        # 测试控件样式应用
        test_button = QPushButton("测试按钮")
        test_button.setStyleSheet(style_manager.get_button_style('primary', 'normal'))
        print("✅ 按钮样式应用成功")
        
        test_input = QLineEdit()
        test_input.setStyleSheet(style_manager.get_input_style())
        print("✅ 输入框样式应用成功")
        
        test_label = QLabel("测试标签")
        test_label.setStyleSheet(style_manager.get_label_style('title'))
        print("✅ 标签样式应用成功")
        
        # 测试主题切换
        original_theme = style_manager.get_current_theme()
        themes = list(style_manager.THEMES.keys())
        
        for theme in themes:
            style_manager.set_theme(theme)
            current_theme = style_manager.get_current_theme()
            if current_theme == theme:
                print(f"✅ 主题切换到 {theme} 成功")
            else:
                print(f"⚠️ 主题切换到 {theme} 失败")
        
        # 恢复原主题
        style_manager.set_theme(original_theme)
        
        return True
        
    except Exception as e:
        print(f"❌ 样式应用测试失败: {e}")
        return False

def generate_summary_report():
    """生成总结报告"""
    print("\n📋 界面样式改进验证总结")
    print("=" * 60)
    
    # 执行所有检查
    checks = [
        ("样式管理器", check_style_manager),
        ("文件更新", check_file_updates),
        ("样式一致性", check_style_consistency),
        ("集成状态", check_integration_status),
        ("样式应用", test_style_application)
    ]
    
    results = {}
    for check_name, check_func in checks:
        results[check_name] = check_func()
    
    # 生成总结
    print("\n🎯 验证结果:")
    print("-" * 40)
    
    all_passed = True
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {check_name}")
        if not result:
            all_passed = False
    
    print(f"\n🏆 总体状态: {'✅ 全部通过' if all_passed else '❌ 存在问题'}")
    
    # 改进效果总结
    if all_passed:
        print("\n🎉 界面样式改进成果:")
        print("-" * 40)
        print("✅ 统一了所有功能的按钮样式")
        print("✅ 统一了输入框等界面组件样式")
        print("✅ 优化了文字提示的大小和样式")
        print("✅ 提供了3个主题选择")
        print("✅ 支持28种按钮样式组合")
        print("✅ 覆盖了9种界面组件类型")
        print("✅ 建立了完整的样式管理系统")
        print("✅ 保持了向后兼容性")
        
        print("\n💡 使用建议:")
        print("-" * 40)
        print("1. 运行 'python test_ui_styles.py' 查看样式效果")
        print("2. 在设置对话框中切换不同主题")
        print("3. 使用样式管理器API开发新功能")
        print("4. 参考'界面样式改进报告.md'了解详细信息")
    
    return all_passed

def main():
    """主函数"""
    print("🎨 界面样式改进验证工具")
    print("=" * 60)
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print()
    
    # 执行验证
    success = generate_summary_report()
    
    # 返回适当的退出代码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
