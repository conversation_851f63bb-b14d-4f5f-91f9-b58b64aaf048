import os
import shutil
import subprocess

print("开始构建性能监控数据分析器 v3.0...")

# 清理之前的构建
print("清理之前的构建...")
if os.path.exists("build"):
    shutil.rmtree("build")
if os.path.exists("dist"):
    shutil.rmtree("dist")

# 使用PyInstaller构建
print("使用PyInstaller构建...")
cmd = [
    "pyinstaller",
    "--clean",
    "--name=性能监控数据分析器_v3.0",
    "--icon=icons/app_icon.ico",
    "--add-data=icons;icons",
    "--add-data=fonts;fonts",
    "--version-file=version_info.txt",
    "--noconsole",
    "--noconfirm",
    "run_unified_tool.py"
]

try:
    subprocess.run(cmd, check=True)
    print("构建完成！")
    print("可执行文件位于: dist/性能监控数据分析器_v3.0/性能监控数据分析器_v3.0.exe")
except subprocess.CalledProcessError as e:
    print(f"构建失败: {e}")
    exit(1)

# 创建ZIP文件
print("创建ZIP文件...")
if os.path.exists("dist/性能监控数据分析器_v3.0"):
    try:
        import zipfile
        
        zip_filename = "性能监控数据分析器_v3.0.zip"
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk("dist/性能监控数据分析器_v3.0"):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, "dist")
                    zipf.write(file_path, arcname)
        
        print("ZIP包创建完成！")
        print(f"ZIP文件位于: {zip_filename}")
    except Exception as e:
        print(f"创建ZIP文件失败: {e}")
else:
    print("错误：dist/性能监控数据分析器_v3.0 目录不存在！")
