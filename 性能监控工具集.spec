# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('icons', 'icons'), ('version.py', '.'), ('logger_utils.py', '.'), ('settings_dialog.py', '.'), ('unified_launcher.py', '.'), ('run_performance_analyzer.py', '.'), ('run_unified_nmon_analyzer.py', '.'), ('file_version_info.txt', '.')],
    hiddenimports=['pandas', 'numpy', 'openpyxl', 'matplotlib', 'PyQt5', 'logger_utils', 'version'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='性能监控工具集',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='file_version_info.txt',
    icon=['icons\\app_icon.ico'],
)
