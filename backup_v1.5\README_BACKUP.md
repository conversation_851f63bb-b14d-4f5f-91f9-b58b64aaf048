# 性能监控数据分析器 V1.5 - 源代码备份

这是性能监控数据分析器 V1.5 版本的完整源代码备份。

## 备份内容

此备份包含以下内容：

1. 所有Python源代码文件
2. 内嵌字体文件（fonts目录）
3. 应用图标文件（icons目录）
4. 配置和构建文件
5. 版本信息文件

## 版本特性

V1.5版本包含以下主要特性和改进：

- 修复了目录处理时磁盘繁忙度图表缺少数据的问题
- 增强了图表生成过程中的错误处理
- 改进了日志记录，提供更详细的诊断信息
- 优化了内存使用，减少了大量文件处理时的内存占用
- 增加了图表生成过程中的进度反馈
- 修复了时间戳和数据长度不匹配的问题

## 恢复备份

如需从此备份恢复源代码，只需将所有文件复制到一个新的工作目录即可。

## 构建可执行文件

要从源代码构建可执行文件，请运行：

```
python -m PyInstaller performance_analyzer.spec
```

构建完成后，可执行文件将位于`dist/性能监控数据分析器_V1.5`目录中。

## 备份日期

备份日期：2025-04-24
