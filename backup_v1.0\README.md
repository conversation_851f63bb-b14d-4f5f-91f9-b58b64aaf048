# 性能监控数据分析器

一个用于分析Excel文件中的CPU、内存和磁盘使用率数据的工具。

## 功能

- 从包含CPU、内存和磁盘使用率信息的Excel文件中提取和分析数据
- 处理目录中的多个文件
- 生成包含关键指标的摘要报告
- 为CPU、内存和磁盘使用率创建可视化图表
- 用户友好的图形界面

## 要求

- Python 3.6 或更高版本
- `requirements.txt` 中列出的依赖项

## 安装

1. 克隆或下载此仓库
2. 安装所需的依赖项：

```bash
pip install -r requirements.txt
```

## 使用方法

运行应用程序：

```bash
python main.py
```

### 处理单个文件

1. 点击“选择文件”选择Excel文件
2. 点击“处理文件”分析数据
3. 使用“选择输出文件”选择输出文件
4. 点击“保存结果”保存分析结果和图表

### 处理多个文件

1. 点击“选择目录”选择包含Excel文件的目录
2. 点击“处理目录”分析目录中的所有Excel文件
3. 使用“选择输出文件”选择输出文件
4. 点击“保存结果”保存分析结果和图表

## 输入文件格式

输入的Excel文件应包含以下工作表：

- **CPU_ALL**: 包含带有“CPU%”列的CPU使用率数据
- **MEM**: 包含带有“memtotal”、“memfree”、“cached”和“buffers”列的内存使用率数据
- **DISKBUSY**: 包含不同磁盘列的磁盘使用率数据

## 输出

程序生成一个Excel文件，其中包含：

- 一个包含每个输入文件的平均CPU、内存和最大磁盘使用率的摘要表
- 可视化CPU、内存和磁盘使用率数据的图表

## 许可证

本项目采用MIT许可证 - 详情请参见LICENSE文件。
