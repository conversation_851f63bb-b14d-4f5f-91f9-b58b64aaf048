import os
import requests
import zipfile
import io
import shutil

def download_font():
    """Download Source Han Sans CN font and extract it to the fonts directory"""
    # Create fonts directory if it doesn't exist
    if not os.path.exists('fonts'):
        os.makedirs('fonts')

    # URL for Source Han Sans CN font (smaller file)
    url = "https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/SimplifiedChinese/SourceHanSansSC-Regular.otf"

    print("Downloading Source Han Sans CN font...")

    try:
        # Download the font file
        response = requests.get(url)
        response.raise_for_status()  # Raise an exception for HTTP errors

        # Save the font file
        font_path = os.path.join('fonts', 'SourceHanSansSC-Regular.otf')
        with open(font_path, 'wb') as f:
            f.write(response.content)

        print(f"Font downloaded successfully to {font_path}")
        return font_path

    except Exception as e:
        print(f"Error downloading font: {e}")
        return None

if __name__ == "__main__":
    download_font()
