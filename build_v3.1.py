"""
构建性能监控数据分析器 v3.1 可执行文件

这个脚本用于构建性能监控数据分析器 v3.1 的可执行文件，
并将其打包成ZIP文件以便分发。
"""

import os
import shutil
import subprocess
import zipfile
import time

def main():
    """主函数。"""
    print("=" * 80)
    print("开始构建性能监控数据分析器 v3.1...")
    print("=" * 80)
    
    # 清理之前的构建
    print("\n清理之前的构建...")
    if os.path.exists("build"):
        shutil.rmtree("build")
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    
    # 使用PyInstaller构建
    print("\n使用PyInstaller构建...")
    cmd = [
        "pyinstaller",
        "--clean",
        "--name=性能监控数据分析器_v3.1",
        "--icon=icons/app_icon.ico",
        "--add-data=icons;icons",
        "--add-data=fonts;fonts",
        "--version-file=version_info.txt",
        "--noconsole",
        "--noconfirm",
        "run_unified_tool.py"
    ]
    
    try:
        subprocess.run(cmd, check=True)
        print("\n构建完成！")
        print(f"可执行文件位于: dist/性能监控数据分析器_v3.1/性能监控数据分析器_v3.1.exe")
    except subprocess.CalledProcessError as e:
        print(f"\n构建失败: {e}")
        return
    
    # 创建ZIP文件
    print("\n创建ZIP文件...")
    if os.path.exists("dist/性能监控数据分析器_v3.1"):
        try:
            zip_filename = "性能监控数据分析器_v3.1.zip"
            
            # 如果ZIP文件已存在，先删除
            if os.path.exists(zip_filename):
                os.remove(zip_filename)
            
            # 创建ZIP文件
            with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 添加根目录
                for root, dirs, files in os.walk("dist/性能监控数据分析器_v3.1"):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, "dist")
                        print(f"添加文件: {arcname}")
                        zipf.write(file_path, arcname)
            
            print("\nZIP包创建完成！")
            print(f"ZIP文件位于: {zip_filename}")
            
            # 获取ZIP文件大小
            zip_size = os.path.getsize(zip_filename) / (1024 * 1024)  # 转换为MB
            print(f"ZIP文件大小: {zip_size:.2f} MB")
        except Exception as e:
            print(f"\n创建ZIP文件失败: {e}")
    else:
        print("\n错误：dist/性能监控数据分析器_v3.1 目录不存在！")

if __name__ == "__main__":
    start_time = time.time()
    main()
    end_time = time.time()
    print(f"\n总耗时: {end_time - start_time:.2f} 秒")
    print("\n按任意键退出...")
    input()
