import logging
import sys
import os
import traceback
from datetime import datetime

# 内部日志记录器
logger = logging.getLogger('app')

# 初始化日志记录器
def init_logger(log_file=None, log_level=None):
    """
    初始化日志记录器

    参数:
        log_file: 日志文件路径，如果为None，则使用默认路径
        log_level: 日志级别，如果为None，则从设置中读取
    """
    global logger

    # 如果没有提供日志级别，从设置中读取
    if log_level is None:
        try:
            from PyQt5.QtCore import QSettings
            settings = QSettings("PerformanceAnalyzer", "Settings")
            log_level = settings.value("log_level", logging.INFO, type=int)
        except Exception:
            # 如果无法从设置中读取，使用默认级别
            log_level = logging.INFO

    # 如果没有提供日志文件路径，使用默认路径
    if log_file is None:
        # 创建日志目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 创建日志文件名（包含日期）
        log_file = os.path.join(log_dir, f"app_{datetime.now().strftime('%Y%m%d')}.log")

    # 配置日志格式
    log_format = "%(asctime)s [%(levelname)s] %(message)s"
    date_format = "%Y-%m-%d %H:%M:%S"
    formatter = logging.Formatter(log_format, date_format)

    # 清除现有处理程序
    if logger.handlers:
        for handler in logger.handlers:
            logger.removeHandler(handler)

    # 添加文件处理程序
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # 移除控制台处理程序，只保留日志文件
    # console_handler = logging.StreamHandler(sys.stdout)
    # console_handler.setFormatter(formatter)
    # logger.addHandler(console_handler)

    # 设置日志级别
    logger.setLevel(log_level)

    # 记录日志初始化信息
    level_name = logging.getLevelName(log_level)
    logger.info(f"日志系统初始化成功，日志文件: {log_file}，日志级别: {level_name}")

    return log_file

# 内存中的日志缓存，用于GUI显示
log_messages = []

def log(message, level="INFO"):
    """
    记录日志消息

    参数:
        message: 日志消息
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    # 添加到内存缓存
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_messages.append((timestamp, message, level))

    # 确保日志记录器已初始化
    if not logger.handlers:
        init_logger(None)

    # 记录到日志文件
    try:
        if level == "DEBUG":
            logger.debug(message)
        elif level == "INFO":
            logger.info(message)
        elif level == "WARNING":
            logger.warning(message)
        elif level == "ERROR":
            logger.error(message)
        elif level == "CRITICAL":
            logger.critical(message)
        else:
            logger.info(message)
    except Exception as e:
        # 如果日志记录失败，不输出到控制台
        # 仅在内存中记录日志记录失败的信息
        log_messages.append((timestamp, f"日志记录失败: {e}", "ERROR"))

    return f"[{timestamp}] {message}"

def get_logs():
    """获取内存中的日志消息"""
    return log_messages.copy()

def clear_logs():
    """清空内存中的日志消息"""
    global log_messages
    log_messages = []

def log_exception(e, context=""):
    """
    记录异常信息

    参数:
        e: 异常对象
        context: 上下文信息
    """
    error_message = f"{context}错误: {str(e)}"
    error_traceback = traceback.format_exc()

    # 确保日志记录器已初始化
    if not logger.handlers:
        init_logger(None)

    # 记录到日志文件
    try:
        logger.error(error_message)
        logger.error(f"异常详情: {error_traceback}")
    except Exception as log_error:
        # 如果日志记录失败，不输出到控制台
        # 仅在内存中记录日志记录失败的信息
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_messages.append((timestamp, f"日志记录异常失败: {log_error}", "ERROR"))

    # 添加到内存缓存
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_messages.append((timestamp, error_message, "ERROR"))
    log_messages.append((timestamp, f"异常详情: {error_traceback}", "ERROR"))

    return error_message, error_traceback
