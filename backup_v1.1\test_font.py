import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import numpy as np

def test_embedded_font():
    """Test that the embedded font works correctly for Chinese characters"""
    # Check if the font file exists
    font_path = os.path.join('fonts', 'SourceHanSansSC-Regular.otf')
    if not os.path.exists(font_path):
        print(f"Error: Font file not found at {font_path}")
        return False

    # Register the font
    try:
        font_prop = fm.FontProperties(fname=font_path)
        print(f"Font loaded successfully from {font_path}")
    except Exception as e:
        print(f"Error loading font: {e}")
        return False

    # Create a simple plot with Chinese characters
    plt.figure(figsize=(10, 6))

    # Generate some data
    x = np.linspace(0, 10, 100)
    y = np.sin(x)

    # Set font properties for all text elements
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['font.sans-serif'] = ['Source Han Sans SC'] + plt.rcParams['font.sans-serif']
    plt.rcParams['axes.unicode_minus'] = False

    # Plot with Chinese labels
    plt.plot(x, y, label='正弦波')
    plt.title('中文字体测试', fontproperties=font_prop)
    plt.xlabel('时间 (秒)', fontproperties=font_prop)
    plt.ylabel('振幅', fontproperties=font_prop)

    # Set legend with font properties
    legend = plt.legend()
    for text in legend.get_texts():
        text.set_fontproperties(font_prop)

    plt.grid(True)

    # Save the plot
    output_path = 'font_test.png'
    try:
        plt.savefig(output_path, bbox_inches='tight')
        print(f"Test plot saved to {output_path}")
        plt.close()
        return True
    except Exception as e:
        print(f"Error saving plot: {e}")
        plt.close()
        return False

if __name__ == "__main__":
    print("Testing embedded Chinese font...")
    success = test_embedded_font()
    if success:
        print("Font test completed successfully!")
    else:
        print("Font test failed!")
