import functools
import traceback
import sys
from logger_utils import log, log_exception

def exception_handler(func):
    """
    装饰器：捕获并记录函数执行过程中的异常

    参数:
        func: 要装饰的函数

    返回:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # 获取函数名称
            func_name = func.__name__
            # 记录异常信息
            error_msg, error_tb = log_exception(e, f"执行 {func_name} 函数时")

            # 记录系统信息
            log(f"Python版本: {sys.version}", "DEBUG")
            log(f"当前工作目录: {sys.path[0]}", "DEBUG")

            # 重新抛出异常，让上层调用者处理
            raise

    return wrapper

def gui_exception_handler(func):
    """
    装饰器：捕获并记录GUI函数执行过程中的异常，但不重新抛出

    参数:
        func: 要装饰的函数

    返回:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # 获取函数名称
            func_name = func.__name__
            # 记录异常信息
            error_msg, error_tb = log_exception(e, f"执行 {func_name} 函数时")

            # 记录系统信息
            log(f"Python版本: {sys.version}", "DEBUG")
            log(f"当前工作目录: {sys.path[0]}", "DEBUG")

            # 在GUI中显示错误信息
            if len(args) > 0 and hasattr(args[0], 'log_message'):
                args[0].log_message(f"错误: {error_msg}")
                args[0].log_message(error_tb)

            # 不重新抛出异常，避免GUI崩溃
            return None

    return wrapper
